# Test-Spark Code Index

This document provides a high-level index of the Test-Spark codebase, summarizing the main components, their responsibilities, and locations in the project. It is intended to help developers navigate and understand the structure of the codebase efficiently.

---

## Project Overview

Test-Spark is a full-stack adaptive learning platform with a Go backend and React/TypeScript frontend. The backend follows clean architecture principles with distinct layers for API, services, repositories, and models. The frontend is built with React and uses modern state management and routing.

---

## Backend

### 1. API Layer (`internal/api/`)

- Contains HTTP handlers, routing, and middleware.
- Responsible for request validation, authentication, serialization, and routing.
- Key files:
  - `user_handler.go` - User-related API endpoints.
  - `auth_handler.go` - Authentication endpoints.
  - `router.go` - API router setup.
  - `middleware.go` - Middleware implementations (logging, security, etc.).

### 2. Service Layer (`internal/services/`)

- Implements business logic and AI integration.
- Coordinates between repositories and external AI providers.
- Key services:
  - `TestService` - Manages test lifecycle and question generation.
  - `AnalyticsService` - Handles performance analytics.
  - `AIProvider` implementations - Groq, OpenAI, Hugging Face, etc.
  - `RecommendationEngine` - Personalized recommendations.

### 3. Repository Layer (`internal/database/`)

- Abstracts database operations using the repository pattern.
- Provides data access interfaces and implementations.
- Key repositories:
  - `UserRepository` - User data access.
  - `TestRepository` - Test data management.
  - `AnalyticsRepository` - Analytics data queries.
  - `ContentRepository` - Content-related data.

### 4. Models (`internal/models/`)

- Defines Go structs representing database tables and domain entities.
- Includes data transfer objects (DTOs) and enums.
- Examples:
  - `User` - User entity.
  - `Test` - Test instance.
  - `Question` - Question data.
  - `Report` - Analytics reports.

---

## Frontend

### 1. Components (`frontend/src/components/`)

- Reusable UI components organized by feature.
- Examples:
  - `charts/` - Data visualization components.
  - `questions/` - Question display components.
  - `layout/` - Layout and navigation components.

### 2. Pages (`frontend/src/pages/`)

- Page-level components representing routes.
- Examples:
  - `Dashboard` - Analytics dashboard.
  - `TestInterface` - Test-taking UI.

### 3. Hooks (`frontend/src/hooks/`)

- Custom React hooks for shared logic.
- Examples:
  - `useAuth` - Authentication state management.
  - `useAnalyticsData` - Fetching analytics data.

### 4. Providers and Stores (`frontend/src/providers/`, `frontend/src/stores/`)

- Context providers and state management stores.

---

## Database

- PostgreSQL database with schema defined in `schema.sql` and migration files.
- Key tables:
  - `users`, `tests`, `questions`, `test_results`, `analytics`.
- Indexes for performance on frequently queried columns.
- Database access via repository layer.

---

## Configuration and Deployment

- Environment configuration in `.env` files.
- Docker support with `Dockerfile` and `docker-compose.yml`.
- Deployment scripts and documentation in root and `docs/`.

---

## Documentation

- API specification: `apilist.md`
- Code documentation standards: `docs/CODE_DOCUMENTATION.md`
- Architecture overview: `docs/ARCHITECTURE.md`
- Database design: `docs/DATABASE_DESIGN.md`
- Development and deployment guides in `docs/`.

---

## Additional Notes

- The project uses clean architecture and dependency injection for maintainability.
- AI integration supports multiple providers with fallback mechanisms.
- Testing is implemented with Go test and React Testing Library.

---

This index will be updated as the project evolves to maintain accuracy and usefulness.
