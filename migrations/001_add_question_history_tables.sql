-- Migration: Add Question History and Enhanced Generation Tables
-- This migration adds the new tables needed for the enhanced question generation system
-- Run this against your existing database to add the new functionality

-- Set the search path to your schema
SET search_path TO testspark_db;

-- User Question History for preventing repetition
CREATE TABLE IF NOT EXISTS user_question_history (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    question_id UUID NOT NULL,
    first_seen_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_seen_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    times_seen INT NOT NULL DEFAULT 1,
    times_answered INT NOT NULL DEFAULT 0,
    times_correct INT NOT NULL DEFAULT 0,
    avg_response_time_ms INT,
    last_response_time_ms INT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_user_question_history_user FOREI<PERSON><PERSON>(user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_question_history_question FOR<PERSON><PERSON><PERSON> KEY(question_id) REFERENCES questions(id) ON DELETE CASCADE,
    UNIQUE(user_id, question_id)
);

-- Question Generation Sessions to track AI generation patterns
CREATE TABLE IF NOT EXISTS question_generation_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    session_context JSONB NOT NULL, -- Contains topic_ids, difficulty, subject, etc.
    questions_generated INT NOT NULL DEFAULT 0,
    ai_provider VARCHAR(100),
    generation_prompt_hash VARCHAR(64), -- Hash of the prompt used for deduplication
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_question_gen_session_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Link questions to their generation sessions
CREATE TABLE IF NOT EXISTS question_generation_links (
    question_id UUID NOT NULL,
    session_id UUID NOT NULL,
    generation_order INT NOT NULL,
    PRIMARY KEY (question_id, session_id),
    CONSTRAINT fk_question_gen_link_question FOREIGN KEY(question_id) REFERENCES questions(id) ON DELETE CASCADE,
    CONSTRAINT fk_question_gen_link_session FOREIGN KEY(session_id) REFERENCES question_generation_sessions(id) ON DELETE CASCADE
);

-- Indexes for User Question History
CREATE INDEX IF NOT EXISTS idx_user_question_history_user_id ON user_question_history(user_id);
CREATE INDEX IF NOT EXISTS idx_user_question_history_question_id ON user_question_history(question_id);
CREATE INDEX IF NOT EXISTS idx_user_question_history_user_last_seen ON user_question_history(user_id, last_seen_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_question_history_user_times_seen ON user_question_history(user_id, times_seen ASC);

-- Indexes for Question Generation Sessions
CREATE INDEX IF NOT EXISTS idx_question_generation_sessions_user_id ON question_generation_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_question_generation_sessions_created_at ON question_generation_sessions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_question_generation_sessions_prompt_hash ON question_generation_sessions(generation_prompt_hash);
CREATE INDEX IF NOT EXISTS idx_question_generation_links_session_id ON question_generation_links(session_id);

-- Add indexes to existing questions table for better performance
CREATE INDEX IF NOT EXISTS idx_questions_created_at ON questions(created_at);
CREATE INDEX IF NOT EXISTS idx_questions_author_ai_model ON questions(author_ai_model);

-- Verify tables were created
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'testspark_db' AND table_name = 'user_question_history') THEN
        RAISE NOTICE 'SUCCESS: user_question_history table created';
    ELSE
        RAISE EXCEPTION 'FAILED: user_question_history table not created';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'testspark_db' AND table_name = 'question_generation_sessions') THEN
        RAISE NOTICE 'SUCCESS: question_generation_sessions table created';
    ELSE
        RAISE EXCEPTION 'FAILED: question_generation_sessions table not created';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'testspark_db' AND table_name = 'question_generation_links') THEN
        RAISE NOTICE 'SUCCESS: question_generation_links table created';
    ELSE
        RAISE EXCEPTION 'FAILED: question_generation_links table not created';
    END IF;
END $$;

-- Show table counts for verification
SELECT 
    'user_question_history' as table_name, 
    COUNT(*) as row_count 
FROM user_question_history
UNION ALL
SELECT 
    'question_generation_sessions' as table_name, 
    COUNT(*) as row_count 
FROM question_generation_sessions
UNION ALL
SELECT 
    'question_generation_links' as table_name, 
    COUNT(*) as row_count 
FROM question_generation_links;

COMMIT;
