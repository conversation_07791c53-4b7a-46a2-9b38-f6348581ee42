# Database Migrations

## Overview

This directory contains database migration files for the Test-Spark application. Migrations are used to version control database schema changes and ensure consistent database state across different environments.

## Migration Files

### Current Migrations

1. **Base Schema** (`../schema.sql`)
   - Initial database schema with all core tables
   - Custom types and basic indexes
   - Core triggers and functions
   - Should be run first for new installations

2. **004_analytics_optimization.sql**
   - Performance optimizations for analytics queries
   - Additional indexes for dashboard performance
   - Materialized views for trend analysis
   - Analytics helper functions

## Migration Naming Convention

Migration files follow the pattern: `{number}_{description}.sql`

- **Number**: Sequential number (001, 002, 003, etc.)
- **Description**: Brief description using underscores
- **Extension**: Always `.sql`

Examples:
- `001_initial_schema.sql`
- `002_add_user_profiles.sql`
- `003_analytics_tables.sql`
- `004_analytics_optimization.sql`

## Migration Structure

Each migration file should include:

```sql
-- Migration: {number} - {Title}
-- Description: Detailed description of changes
-- Date: YYYY-MM-DD
-- Dependencies: List any dependencies

-- Set search path
SET search_path TO testspark_db;

-- Migration content here
-- ...

-- Add comments for documentation
COMMENT ON TABLE new_table IS 'Description of the table';

COMMIT;
```

## Running Migrations

### New Installation

For a fresh database setup:

```bash
# 1. Create database and user
createdb generativeTest
psql -d generativeTest -c "CREATE USER admin WITH PASSWORD 'password';"
psql -d generativeTest -c "GRANT ALL PRIVILEGES ON DATABASE generativeTest TO admin;"

# 2. Run base schema
psql -h localhost -U admin -d generativeTest -f schema.sql

# 3. Run migrations in order
psql -h localhost -U admin -d generativeTest -f migrations/004_analytics_optimization.sql
```

### Existing Installation

For updating an existing database:

```bash
# Run only new migrations
psql -h localhost -U admin -d generativeTest -f migrations/005_new_migration.sql
```

## Migration Best Practices

### 1. Backward Compatibility

- Always ensure migrations are backward compatible when possible
- Use `IF NOT EXISTS` for creating objects
- Use `IF EXISTS` for dropping objects
- Add columns with default values

```sql
-- Good: Safe column addition
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone VARCHAR(20) DEFAULT '';

-- Good: Safe index creation
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
```

### 2. Data Safety

- Always backup before running migrations in production
- Test migrations on staging environment first
- Use transactions where appropriate
- Include rollback instructions in comments

```sql
-- Migration: Add user preferences
-- Rollback: DROP TABLE user_preferences;

BEGIN;
-- Migration content
COMMIT;
```

### 3. Performance Considerations

- Create indexes concurrently in production
- Consider maintenance windows for large changes
- Monitor query performance after migrations

```sql
-- For production, use CONCURRENTLY
CREATE INDEX CONCURRENTLY idx_large_table_column ON large_table(column);
```

### 4. Documentation

- Include clear descriptions and comments
- Document any manual steps required
- Explain the purpose of changes

## Migration Checklist

Before creating a migration:

- [ ] Backup database
- [ ] Test on development environment
- [ ] Review for backward compatibility
- [ ] Include proper error handling
- [ ] Add appropriate comments
- [ ] Test rollback procedure (if applicable)
- [ ] Update documentation

## Common Migration Patterns

### Adding a Table

```sql
-- Migration: 005 - Add notification system
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_unread ON notifications(user_id, is_read) WHERE is_read = FALSE;

COMMENT ON TABLE notifications IS 'User notification system';
```

### Adding a Column

```sql
-- Migration: 006 - Add user timezone
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) DEFAULT 'UTC';

COMMENT ON COLUMN user_profiles.timezone IS 'User timezone for scheduling';
```

### Creating an Index

```sql
-- Migration: 007 - Optimize question queries
CREATE INDEX IF NOT EXISTS idx_questions_created_at ON questions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_questions_topic_created ON questions(topic_id, created_at DESC);

COMMENT ON INDEX idx_questions_created_at IS 'Optimizes recent questions queries';
```

### Adding a Function

```sql
-- Migration: 008 - Add utility functions
CREATE OR REPLACE FUNCTION calculate_user_streak(p_user_id UUID)
RETURNS INTEGER AS $$
DECLARE
    streak_count INTEGER := 0;
BEGIN
    -- Function implementation
    RETURN streak_count;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION calculate_user_streak IS 'Calculates consecutive days of user activity';
```

## Troubleshooting

### Common Issues

1. **Permission Errors**
   ```bash
   # Grant necessary permissions
   GRANT CREATE ON SCHEMA testspark_db TO admin;
   GRANT USAGE ON SCHEMA testspark_db TO admin;
   ```

2. **Search Path Issues**
   ```sql
   -- Always set search path in migrations
   SET search_path TO testspark_db;
   ```

3. **Dependency Errors**
   ```sql
   -- Check if objects exist before creating
   CREATE TABLE IF NOT EXISTS ...
   CREATE INDEX IF NOT EXISTS ...
   ```

### Rollback Procedures

For critical migrations, include rollback instructions:

```sql
-- Migration: 009 - Major schema change
-- Rollback instructions:
-- 1. DROP TABLE new_table;
-- 2. ALTER TABLE old_table DROP COLUMN new_column;
-- 3. DROP FUNCTION new_function();

-- Migration content here...
```

## Production Deployment

### Pre-deployment

1. **Backup Database**
   ```bash
   pg_dump -h host -U user -d database > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Test on Staging**
   ```bash
   # Apply migration to staging
   psql -h staging-host -U user -d database -f migration.sql
   
   # Run application tests
   go test ./...
   ```

3. **Review Performance Impact**
   ```sql
   -- Check query plans
   EXPLAIN ANALYZE SELECT ...;
   
   -- Monitor index usage
   SELECT * FROM pg_stat_user_indexes;
   ```

### Deployment

1. **Maintenance Window** (if required)
2. **Apply Migration**
   ```bash
   psql -h prod-host -U user -d database -f migration.sql
   ```
3. **Verify Application**
4. **Monitor Performance**

### Post-deployment

1. **Verify Data Integrity**
2. **Check Application Logs**
3. **Monitor Database Performance**
4. **Update Documentation**

## Migration Tools

### Manual Execution

```bash
# Single migration
psql -h host -U user -d database -f migration.sql

# Multiple migrations
for file in migrations/*.sql; do
    echo "Running $file"
    psql -h host -U user -d database -f "$file"
done
```

### Automated Tools

Consider using migration tools for complex deployments:

- **golang-migrate**: Go-based migration tool
- **Flyway**: Java-based migration tool
- **Liquibase**: Database-independent migration tool

## Version Control

### Git Workflow

1. Create migration file
2. Test locally
3. Commit to feature branch
4. Test on staging
5. Merge to main
6. Deploy to production

### File Naming

- Use sequential numbers
- Include descriptive names
- Keep names concise but clear

```
migrations/
├── 001_initial_schema.sql
├── 002_add_user_profiles.sql
├── 003_analytics_tables.sql
├── 004_analytics_optimization.sql
└── README.md
```
