-- Migration: 004 - Analytics Performance Optimization
-- Description: Add indexes and optimizations for analytics queries
-- Date: 2024-01-15
-- Dependencies: Requires base schema from schema.sql

-- Add indexes for time-based filtering on test results
CREATE INDEX IF NOT EXISTS idx_test_results_user_completed_at 
ON test_results(user_id, completed_at) 
WHERE completed_at IS NOT NULL;

-- Add index for test results with score filtering
CREATE INDEX IF NOT EXISTS idx_test_results_user_score_date 
ON test_results(user_id, score, completed_at) 
WHERE completed_at IS NOT NULL;

-- Add composite index for tests table for analytics queries
CREATE INDEX IF NOT EXISTS idx_tests_user_completed_subject 
ON tests(user_id, completed_at, test_context_subject_id) 
WHERE completed_at IS NOT NULL;

-- Add index for topic performance summary analytics
CREATE INDEX IF NOT EXISTS idx_topic_performance_user_score 
ON topic_performance_summary(user_id, proficiency_score DESC);

-- Add index for topic performance with last tested date
CREATE INDEX IF NOT EXISTS idx_topic_performance_user_last_tested 
ON topic_performance_summary(user_id, last_tested_at DESC) 
WHERE last_tested_at IS NOT NULL;

-- Add partial index for weak topics (commonly queried)
CREATE INDEX IF NOT EXISTS idx_topic_performance_weak_topics 
ON topic_performance_summary(user_id, proficiency_score ASC, topic_id) 
WHERE proficiency_score < 70;

-- Add partial index for strong topics
CREATE INDEX IF NOT EXISTS idx_topic_performance_strong_topics 
ON topic_performance_summary(user_id, proficiency_score DESC, topic_id) 
WHERE proficiency_score >= 70;

-- Add index for user answers with timing information
CREATE INDEX IF NOT EXISTS idx_user_answers_timing 
ON user_answers(answered_at, is_correct);

-- Add index for test questions with order
CREATE INDEX IF NOT EXISTS idx_test_questions_test_order 
ON test_questions(test_id, question_order);

-- Create materialized view for performance trends (if supported)
-- This will speed up trend calculations significantly
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_user_performance_trends AS
SELECT 
    tr.user_id,
    DATE_TRUNC('week', t.completed_at) as week_start,
    DATE_TRUNC('month', t.completed_at) as month_start,
    DATE_TRUNC('quarter', t.completed_at) as quarter_start,
    s.name as subject_name,
    s.id as subject_id,
    AVG(tr.score) as avg_score,
    COUNT(*) as test_count,
    MIN(tr.score) as min_score,
    MAX(tr.score) as max_score,
    STDDEV(tr.score) as score_stddev
FROM test_results tr
JOIN tests t ON tr.test_id = t.id
LEFT JOIN subjects s ON t.test_context_subject_id = s.id
WHERE t.completed_at IS NOT NULL
GROUP BY tr.user_id, week_start, month_start, quarter_start, s.name, s.id;

-- Add indexes on the materialized view
CREATE INDEX IF NOT EXISTS idx_mv_trends_user_week 
ON mv_user_performance_trends(user_id, week_start DESC);

CREATE INDEX IF NOT EXISTS idx_mv_trends_user_month 
ON mv_user_performance_trends(user_id, month_start DESC);

CREATE INDEX IF NOT EXISTS idx_mv_trends_user_quarter 
ON mv_user_performance_trends(user_id, quarter_start DESC);

-- Create function to refresh the materialized view
CREATE OR REPLACE FUNCTION refresh_performance_trends()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_user_performance_trends;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically refresh trends when test results are added
-- Note: In production, you might want to refresh this periodically instead of on every insert
CREATE OR REPLACE FUNCTION trigger_refresh_trends()
RETURNS trigger AS $$
BEGIN
    -- Only refresh if it's been more than 1 hour since last refresh
    -- This prevents too frequent refreshes
    PERFORM refresh_performance_trends();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add the trigger (commented out for now - enable in production with careful consideration)
-- CREATE TRIGGER tr_refresh_trends_on_test_result
--     AFTER INSERT ON test_results
--     FOR EACH STATEMENT
--     EXECUTE FUNCTION trigger_refresh_trends();

-- Create indexes for user insights table
CREATE INDEX IF NOT EXISTS idx_user_insights_user_priority 
ON user_insights(user_id, priority DESC, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_insights_user_type 
ON user_insights(user_id, type, created_at DESC);

-- Add index for questions by topic and difficulty (for recommendation engine)
CREATE INDEX IF NOT EXISTS idx_questions_topic_difficulty 
ON questions(topic_id, difficulty);

-- Create a function to get user analytics summary efficiently
CREATE OR REPLACE FUNCTION get_user_analytics_summary(p_user_id UUID)
RETURNS TABLE(
    total_tests INTEGER,
    avg_score NUMERIC,
    recent_avg_score NUMERIC,
    improvement_trend NUMERIC,
    consistency_score NUMERIC,
    strongest_subject TEXT,
    weakest_subject TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH user_stats AS (
        SELECT 
            COUNT(*)::INTEGER as total_tests,
            AVG(tr.score) as avg_score,
            AVG(CASE WHEN t.completed_at >= NOW() - INTERVAL '30 days' THEN tr.score END) as recent_avg_score
        FROM test_results tr
        JOIN tests t ON tr.test_id = t.id
        WHERE tr.user_id = p_user_id AND t.completed_at IS NOT NULL
    ),
    subject_performance AS (
        SELECT 
            s.name as subject_name,
            AVG(tr.score) as subject_avg,
            ROW_NUMBER() OVER (ORDER BY AVG(tr.score) DESC) as rank_desc,
            ROW_NUMBER() OVER (ORDER BY AVG(tr.score) ASC) as rank_asc
        FROM test_results tr
        JOIN tests t ON tr.test_id = t.id
        JOIN subjects s ON t.test_context_subject_id = s.id
        WHERE tr.user_id = p_user_id AND t.completed_at IS NOT NULL
        GROUP BY s.name
        HAVING COUNT(*) >= 2  -- Only subjects with at least 2 tests
    ),
    consistency AS (
        SELECT 
            (COUNT(DISTINCT DATE(t.completed_at))::NUMERIC / 
             GREATEST(1, EXTRACT(days FROM (MAX(t.completed_at) - MIN(t.completed_at))))::NUMERIC * 100) as consistency_score
        FROM tests t
        WHERE t.user_id = p_user_id AND t.completed_at >= NOW() - INTERVAL '30 days'
    )
    SELECT 
        us.total_tests,
        us.avg_score,
        us.recent_avg_score,
        COALESCE(us.recent_avg_score - us.avg_score, 0) as improvement_trend,
        COALESCE(c.consistency_score, 0) as consistency_score,
        (SELECT subject_name FROM subject_performance WHERE rank_desc = 1) as strongest_subject,
        (SELECT subject_name FROM subject_performance WHERE rank_asc = 1) as weakest_subject
    FROM user_stats us
    CROSS JOIN consistency c;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get topic recommendations efficiently
CREATE OR REPLACE FUNCTION get_topic_recommendations(p_user_id UUID, p_limit INTEGER DEFAULT 5)
RETURNS TABLE(
    topic_id INTEGER,
    topic_name TEXT,
    subject_name TEXT,
    proficiency_score NUMERIC,
    total_attempted INTEGER,
    recommendation_priority INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tps.topic_id,
        t.name as topic_name,
        s.name as subject_name,
        tps.proficiency_score,
        tps.total_attempted,
        CASE 
            WHEN tps.proficiency_score < 40 THEN 5
            WHEN tps.proficiency_score < 60 THEN 4
            WHEN tps.proficiency_score < 70 THEN 3
            ELSE 2
        END as recommendation_priority
    FROM topic_performance_summary tps
    JOIN topics t ON tps.topic_id = t.id
    JOIN subjects s ON t.subject_id = s.id
    WHERE tps.user_id = p_user_id 
    AND tps.total_attempted > 0
    ORDER BY 
        recommendation_priority DESC,
        tps.proficiency_score ASC,
        tps.total_attempted DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON INDEX idx_test_results_user_completed_at IS 'Optimizes time-based analytics queries';
COMMENT ON INDEX idx_topic_performance_weak_topics IS 'Optimizes weak topic identification for recommendations';
COMMENT ON MATERIALIZED VIEW mv_user_performance_trends IS 'Pre-computed performance trends for faster analytics';
COMMENT ON FUNCTION get_user_analytics_summary IS 'Efficiently computes user analytics summary';
COMMENT ON FUNCTION get_topic_recommendations IS 'Efficiently generates topic recommendations based on performance';

-- Create a scheduled job to refresh materialized views (PostgreSQL with pg_cron extension)
-- This is commented out as it requires the pg_cron extension
-- SELECT cron.schedule('refresh-analytics', '0 */6 * * *', 'SELECT refresh_performance_trends();');

COMMIT;
