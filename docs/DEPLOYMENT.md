# Test-Spark Deployment Guide

## Overview

This guide covers deploying the Test-Spark application in various environments, from local development to production.

## Prerequisites

### System Requirements
- **Go**: 1.21 or later
- **Node.js**: 18 or later
- **PostgreSQL**: 12 or later
- **Git**: For version control

### Required API Keys
- **Groq API Key**: For AI question generation
- **OpenAI API Key**: (Optional) For fallback AI provider
- **Hugging Face API Key**: (Optional) For open-source models

## Local Development Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd test-spark
```

### 2. Database Setup
```bash
# Install PostgreSQL (if not already installed)
# macOS
brew install postgresql
brew services start postgresql

# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql

# Create database and user
sudo -u postgres psql
CREATE DATABASE generativeTest;
CREATE USER admin WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE generativeTest TO admin;
\q

# Run schema setup
psql -h localhost -U admin -d generativeTest -f schema.sql
```

### 3. Environment Configuration

#### Backend (.env)
```bash
# Copy example environment file
cp .env.example .env

# Edit .env with your configuration
DATABASE_URL=postgres://admin:your_password@localhost:5432/generativeTest?sslmode=disable
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters
JWT_ACCESS_TOKEN_EXP_MINUTES=15
JWT_REFRESH_TOKEN_EXP_DAYS=7
GROQ_API_KEY=your-groq-api-key
PORT=8080
```

#### Frontend (frontend/.env)
```bash
cd frontend
cp .env.example .env

# Edit frontend/.env
VITE_API_BASE_URL=http://localhost:8080
VITE_NODE_ENV=development
VITE_DEBUG=false
VITE_API_TIMEOUT=30000
```

### 4. Install Dependencies
```bash
# Backend dependencies
go mod download

# Frontend dependencies
cd frontend
npm install
cd ..
```

### 5. Start Development Servers

#### Option A: Automated Start (Recommended)
```bash
chmod +x start-dev.sh
./start-dev.sh
```

#### Option B: Manual Start
```bash
# Terminal 1: Backend
go run cmd/api/main.go

# Terminal 2: Frontend
cd frontend
npm run dev
```

### 6. Verify Installation
- Backend: http://localhost:8080/health
- Frontend: http://localhost:3000
- API Documentation: See `docs/API_DOCUMENTATION.md`

## Production Deployment

### Docker Deployment

#### 1. Create Dockerfile for Backend
```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o main cmd/api/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/templates ./templates

CMD ["./main"]
```

#### 2. Create Dockerfile for Frontend
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 3. Docker Compose Setup
```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: generativeTest
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./schema.sql:/docker-entrypoint-initdb.d/schema.sql
    ports:
      - "5432:5432"

  backend:
    build: .
    environment:
      DATABASE_URL: postgres://admin:${DB_PASSWORD}@postgres:5432/generativeTest?sslmode=disable
      JWT_SECRET: ${JWT_SECRET}
      GROQ_API_KEY: ${GROQ_API_KEY}
      PORT: 8080
    ports:
      - "8080:8080"
    depends_on:
      - postgres

  frontend:
    build: ./frontend
    environment:
      VITE_API_BASE_URL: http://localhost:8080
    ports:
      - "3000:80"
    depends_on:
      - backend

volumes:
  postgres_data:
```

#### 4. Deploy with Docker Compose
```bash
# Create production environment file
cp .env.example .env.production

# Edit .env.production with production values
# Start services
docker-compose --env-file .env.production up -d
```

### Cloud Deployment (AWS/GCP/Azure)

#### 1. Database Setup
```bash
# Create managed PostgreSQL instance
# AWS RDS, Google Cloud SQL, or Azure Database for PostgreSQL

# Update DATABASE_URL in environment variables
DATABASE_URL=**********************************************/generativeTest?sslmode=require
```

#### 2. Backend Deployment
```bash
# Build for production
CGO_ENABLED=0 GOOS=linux go build -o test-spark-backend cmd/api/main.go

# Deploy to cloud service
# - AWS: Elastic Beanstalk, ECS, or Lambda
# - GCP: App Engine, Cloud Run, or GKE
# - Azure: App Service, Container Instances, or AKS
```

#### 3. Frontend Deployment
```bash
# Build frontend
cd frontend
npm run build

# Deploy static files to:
# - AWS S3 + CloudFront
# - GCP Cloud Storage + CDN
# - Azure Blob Storage + CDN
# - Netlify, Vercel, or similar
```

### Environment Variables for Production

#### Backend Environment Variables
```bash
# Database
DATABASE_URL=postgres://user:pass@host:port/db?sslmode=require

# Authentication
JWT_SECRET=your-production-jwt-secret-minimum-32-characters
JWT_ACCESS_TOKEN_EXP_MINUTES=15
JWT_REFRESH_TOKEN_EXP_DAYS=7

# AI Providers
GROQ_API_KEY=your-groq-api-key
OPENAI_API_KEY=your-openai-api-key  # Optional
HUGGINGFACE_API_KEY=your-hf-api-key  # Optional

# Server
PORT=8080
```

#### Frontend Environment Variables
```bash
VITE_API_BASE_URL=https://your-api-domain.com
VITE_NODE_ENV=production
VITE_DEBUG=false
VITE_API_TIMEOUT=30000
```

## Security Considerations

### SSL/TLS Configuration
```bash
# Use HTTPS in production
# Configure SSL certificates
# Enable HSTS headers
# Use secure cookie settings
```

### Database Security
```bash
# Use connection pooling
# Enable SSL connections
# Restrict database access
# Regular security updates
```

### API Security
```bash
# Rate limiting
# CORS configuration
# Input validation
# SQL injection prevention
```

## Monitoring and Logging

### Application Monitoring
```bash
# Health check endpoint: /health
# Metrics collection
# Error tracking
# Performance monitoring
```

### Database Monitoring
```bash
# Connection pool metrics
# Query performance
# Storage usage
# Backup verification
```

### Log Management
```bash
# Structured logging
# Log aggregation
# Error alerting
# Audit trails
```

## Backup and Recovery

### Database Backups
```bash
# Automated daily backups
pg_dump -h host -U user -d generativeTest > backup_$(date +%Y%m%d).sql

# Point-in-time recovery setup
# Cross-region backup replication
```

### Application Backups
```bash
# Source code in version control
# Environment configuration backup
# Generated reports backup
```

## Scaling Considerations

### Horizontal Scaling
```bash
# Load balancer configuration
# Multiple backend instances
# Database read replicas
# CDN for static assets
```

### Performance Optimization
```bash
# Database query optimization
# Connection pooling tuning
# Caching strategies
# Asset optimization
```

## Troubleshooting

### Common Issues
1. **Database Connection Errors**
   - Check DATABASE_URL format
   - Verify network connectivity
   - Confirm credentials

2. **JWT Token Issues**
   - Verify JWT_SECRET length (minimum 32 characters)
   - Check token expiration settings
   - Validate token format

3. **AI Provider Errors**
   - Verify API keys
   - Check rate limits
   - Monitor provider status

### Debug Commands
```bash
# Check backend health
curl http://localhost:8080/health

# Test database connection
psql $DATABASE_URL -c "SELECT 1;"

# View backend logs
docker-compose logs backend

# Check frontend build
cd frontend && npm run build
```

## Maintenance

### Regular Tasks
- Database maintenance and optimization
- Security updates for dependencies
- Log rotation and cleanup
- Performance monitoring review
- Backup verification

### Update Procedures
1. Test updates in staging environment
2. Create database backup
3. Deploy backend updates
4. Deploy frontend updates
5. Verify functionality
6. Monitor for issues
