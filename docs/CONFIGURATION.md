# Test-Spark Configuration Guide

## Overview

Test-Spark uses environment variables for configuration to ensure security and flexibility across different deployment environments. This guide explains all available configuration options.

## Configuration Files

### Backend Configuration
- **Main file**: `.env` (copy from `.env.example`)
- **Location**: Project root directory
- **Format**: Standard environment variables

### Frontend Configuration
- **Main file**: `frontend/.env` (copy from `frontend/.env.example`)
- **Location**: `frontend/` directory
- **Format**: Vite environment variables (prefixed with `VITE_`)

### AI Provider Configuration
- **Reference file**: `config/ai_providers.example.env`
- **Purpose**: Detailed AI provider configuration examples
- **Usage**: Reference for setting up AI providers

## Backend Configuration Options

### Database Settings

```bash
# PostgreSQL connection string
DATABASE_URL=postgres://username:password@host:port/database?options

# Connection pool settings
DB_MAX_CONNECTIONS=25        # Maximum concurrent connections
DB_MIN_CONNECTIONS=5         # Minimum connections to maintain
DB_MAX_IDLE_TIME=30m        # Maximum idle time before closing
DB_MAX_LIFETIME=1h          # Maximum connection lifetime
```

### Server Settings

```bash
# HTTP server configuration
PORT=8080                   # Server port
SERVER_READ_TIMEOUT=15s     # Request read timeout
SERVER_WRITE_TIMEOUT=15s    # Response write timeout
SERVER_IDLE_TIMEOUT=60s     # Keep-alive timeout
```

### Authentication Settings

```bash
# JWT configuration
JWT_SECRET=your-secret-key                    # Must be 32+ characters
JWT_ACCESS_TOKEN_EXP_MINUTES=15              # Access token expiry
JWT_REFRESH_TOKEN_EXP_DAYS=7                 # Refresh token expiry
```

### AI Provider Settings

```bash
# Primary AI provider
AI_PRIMARY_PROVIDER=groq                     # groq, openai, huggingface, mock
AI_FALLBACK_PROVIDER=mock                    # Fallback provider
AI_ENABLE_FALLBACK=true                      # Enable fallback functionality
AI_TIMEOUT_SECONDS=60                        # Request timeout
AI_MAX_RETRIES=3                            # Maximum retry attempts

# Groq configuration
GROQ_API_KEY=your-api-key
GROQ_MODEL=llama-3.3-70b-versatile
GROQ_TEMPERATURE=0.7
GROQ_MAX_TOKENS=4000

# OpenAI configuration (optional)
OPENAI_API_KEY=your-api-key
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=4000
OPENAI_ORG_ID=your-org-id

# Hugging Face configuration (optional)
HUGGINGFACE_API_KEY=your-api-key
HUGGINGFACE_MODEL=microsoft/DialoGPT-large
HUGGINGFACE_TEMPERATURE=0.7
HUGGINGFACE_MAX_TOKENS=4000
```

### Logging and Monitoring

```bash
# Logging configuration
LOG_LEVEL=info                              # debug, info, warn, error
LOG_FORMAT=json                             # json, text
LOG_REQUESTS=true                           # Log HTTP requests

# Environment settings
ENVIRONMENT=development                      # development, staging, production
DEBUG=false                                 # Enable debug mode
```

### CORS and Security

```bash
# CORS configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization

# Rate limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20
AI_MAX_REQUESTS_PER_MINUTE=60
AI_MAX_QUESTIONS_PER_REQUEST=50
```

## Frontend Configuration Options

### API Settings

```bash
# Backend API configuration
VITE_API_BASE_URL=http://localhost:8080      # Backend API URL
VITE_API_TIMEOUT=30000                       # Request timeout (ms)
VITE_API_MAX_RETRIES=3                       # Maximum retry attempts
VITE_API_RETRY_DELAY=1000                    # Retry delay (ms)
```

### Application Settings

```bash
# Application metadata
VITE_APP_NAME=Test-Spark
VITE_APP_VERSION=1.0.0
VITE_NODE_ENV=development                    # development, staging, production
```

### Development Settings

```bash
# Debug and monitoring
VITE_DEBUG=false                            # Enable debug logging
VITE_ENABLE_PERFORMANCE_MONITORING=true     # Performance monitoring
VITE_ENABLE_ERROR_LOGGING=true              # Error boundary logging
```

### UI/UX Settings

```bash
# Theme configuration
VITE_DEFAULT_THEME=system                   # light, dark, system
VITE_PERSIST_THEME=true                     # Save theme preference

# Animation settings
VITE_ENABLE_ANIMATIONS=true
VITE_ANIMATION_DURATION=300                 # Animation duration (ms)
```

### Feature Flags

```bash
# Feature toggles
VITE_ENABLE_ANALYTICS=true                  # Analytics dashboard
VITE_ENABLE_REPORTS=true                    # Report generation
VITE_ENABLE_AI_TUTOR=true                   # AI tutor features
VITE_ENABLE_GAMIFICATION=true               # Gamification features
VITE_ENABLE_ACCESSIBILITY=true              # Accessibility features
```

### Performance Settings

```bash
# Data and caching
VITE_CHART_REFRESH_INTERVAL=30000           # Chart refresh rate (ms)
VITE_DATA_CACHE_DURATION=300000             # Cache duration (ms)
VITE_MAX_CHART_DATA_POINTS=100              # Maximum chart data points

# Optimization
VITE_ENABLE_SERVICE_WORKER=false            # Service worker caching
VITE_ENABLE_IMAGE_OPTIMIZATION=true         # Image optimization
VITE_IMAGE_QUALITY=80                       # Image quality (1-100)
VITE_ENABLE_LAZY_LOADING=true               # Lazy loading
```

### Security Settings

```bash
# Authentication
VITE_TOKEN_STORAGE=localStorage              # localStorage, sessionStorage, memory
VITE_AUTO_LOGOUT_TIMEOUT=3600000            # Auto-logout timeout (ms, 0=disabled)

# Testing (development only)
VITE_TEST_MODE=false                        # Enable test mode
VITE_MOCK_API=false                         # Mock API responses
VITE_TEST_USER_EMAIL=<EMAIL>       # Test user email
VITE_TEST_USER_PASSWORD=demo1234            # Test user password
```

## Environment-Specific Configurations

### Development Environment

```bash
# Backend (.env)
DATABASE_URL=postgres://admin:password@localhost:5432/generativeTest?sslmode=disable
JWT_SECRET=development-secret-key-minimum-32-characters
GROQ_API_KEY=your-groq-api-key
AI_PRIMARY_PROVIDER=groq
AI_FALLBACK_PROVIDER=mock
DEBUG=true
LOG_LEVEL=debug
ENVIRONMENT=development

# Frontend (frontend/.env)
VITE_API_BASE_URL=http://localhost:8080
VITE_DEBUG=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_NODE_ENV=development
```

### Staging Environment

```bash
# Backend
DATABASE_URL=************************************/testspark?sslmode=require
JWT_SECRET=staging-secret-key-change-in-production
GROQ_API_KEY=staging-groq-api-key
AI_PRIMARY_PROVIDER=groq
AI_FALLBACK_PROVIDER=openai
DEBUG=false
LOG_LEVEL=info
ENVIRONMENT=staging

# Frontend
VITE_API_BASE_URL=https://staging-api.test-spark.com
VITE_DEBUG=false
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_NODE_ENV=staging
```

### Production Environment

```bash
# Backend
DATABASE_URL=*********************************/testspark?sslmode=require
JWT_SECRET=production-secret-key-very-secure-32-plus-characters
GROQ_API_KEY=production-groq-api-key
OPENAI_API_KEY=production-openai-api-key
AI_PRIMARY_PROVIDER=openai
AI_FALLBACK_PROVIDER=groq
DEBUG=false
LOG_LEVEL=warn
ENVIRONMENT=production
RATE_LIMIT_REQUESTS_PER_MINUTE=1000

# Frontend
VITE_API_BASE_URL=https://api.test-spark.com
VITE_DEBUG=false
VITE_ENABLE_PERFORMANCE_MONITORING=false
VITE_AUTO_LOGOUT_TIMEOUT=1800000
VITE_NODE_ENV=production
```

## Security Best Practices

### API Keys and Secrets

1. **Never commit secrets to version control**
2. **Use different keys for different environments**
3. **Rotate keys regularly**
4. **Use environment variables or secret management services**
5. **Monitor API usage and costs**

### JWT Configuration

1. **Use strong, random JWT secrets (32+ characters)**
2. **Set appropriate token expiration times**
3. **Use HTTPS in production**
4. **Implement proper token refresh logic**

### Database Security

1. **Use SSL connections in production**
2. **Limit database user permissions**
3. **Use connection pooling**
4. **Regular security updates**

## Troubleshooting

### Common Configuration Issues

1. **Database Connection Errors**
   - Check DATABASE_URL format
   - Verify credentials and network access
   - Ensure database exists and schema is loaded

2. **JWT Token Issues**
   - Verify JWT_SECRET is 32+ characters
   - Check token expiration settings
   - Ensure consistent secret across instances

3. **AI Provider Errors**
   - Verify API keys are correct and active
   - Check rate limits and quotas
   - Test with mock provider to isolate issues

4. **CORS Errors**
   - Update CORS_ALLOWED_ORIGINS
   - Check frontend URL configuration
   - Verify protocol (http vs https)

### Validation Commands

```bash
# Test database connection
psql $DATABASE_URL -c "SELECT 1;"

# Verify backend configuration
go run cmd/api/main.go --validate-config

# Check frontend build
cd frontend && npm run build

# Test API connectivity
curl http://localhost:8080/health
```

## Configuration Management Tools

### Docker Compose

Use environment files with Docker Compose:

```yaml
# docker-compose.yml
services:
  backend:
    env_file:
      - .env
      - .env.production
```

### Kubernetes

Use ConfigMaps and Secrets:

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-spark-config
data:
  PORT: "8080"
  LOG_LEVEL: "info"
```

### Cloud Providers

- **AWS**: Parameter Store, Secrets Manager
- **GCP**: Secret Manager, Config Connector
- **Azure**: Key Vault, App Configuration
