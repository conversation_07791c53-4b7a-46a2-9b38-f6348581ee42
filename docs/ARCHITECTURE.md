# Test-Spark Architecture Overview

## System Architecture

Test-Spark is a full-stack adaptive learning platform built with modern technologies and clean architecture principles.

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (React/TypeScript)              │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   Components    │ │     Pages       │ │     Hooks       ││
│  │                 │ │                 │ │                 ││
│  │ • UI Components │ │ • Dashboard     │ │ • useAuth       ││
│  │ • Charts        │ │ • Analytics     │ │ • useAnalytics  ││
│  │ • Forms         │ │ • Test Interface│ │ • usePerformance││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
│                              │                              │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   API Client    │ │   State Mgmt    │ │     Utils       ││
│  │                 │ │                 │ │                 ││
│  │ • HTTP Client   │ │ • React Query   │ │ • Validation    ││
│  │ • Auth Manager  │ │ • Local State   │ │ • Formatting    ││
│  │ • Error Handler │ │ • Theme Context │ │ • Memoization   ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                         HTTP/REST API
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Backend (Go)                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   API Layer     │ │   Middleware    │ │     Router      ││
│  │                 │ │                 │ │                 ││
│  │ • Handlers      │ │ • Authentication│ │ • Chi Router    ││
│  │ • Validation    │ │ • CORS          │ │ • Route Groups  ││
│  │ • Serialization │ │ • Logging       │ │ • Middleware    ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
│                              │                              │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │  Service Layer  │ │   Auth Service  │ │   AI Services   ││
│  │                 │ │                 │ │                 ││
│  │ • Test Service  │ │ • JWT Manager   │ │ • Groq Client   ││
│  │ • Analytics     │ │ • Token Refresh │ │ • OpenAI        ││
│  │ • Report Service│ │ • User Auth     │ │ • Hugging Face  ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
│                              │                              │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │  Repository     │ │     Models      │ │   Configuration ││
│  │                 │ │                 │ │                 ││
│  │ • User Repo     │ │ • User Model    │ │ • Environment   ││
│  │ • Test Repo     │ │ • Test Model    │ │ • Database      ││
│  │ • Analytics Repo│ │ • Question Model│ │ • AI Providers  ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                         PostgreSQL
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Database Layer                           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │     Tables      │ │     Indexes     │ │   Constraints   ││
│  │                 │ │                 │ │                 ││
│  │ • users         │ │ • Performance   │ │ • Foreign Keys  ││
│  │ • tests         │ │ • Search        │ │ • Unique Keys   ││
│  │ • questions     │ │ • Analytics     │ │ • Check Rules   ││
│  │ • test_results  │ │                 │ │                 ││
│  │ • analytics     │ │                 │ │                 ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## Technology Stack

### Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: React Query (TanStack Query)
- **Routing**: React Router v6
- **Charts**: Recharts
- **Testing**: Jest + React Testing Library

### Backend
- **Language**: Go 1.23
- **Web Framework**: Chi Router
- **Database**: PostgreSQL with pgx driver
- **Authentication**: JWT with refresh tokens
- **AI Integration**: Multiple providers (Groq, OpenAI, Hugging Face)
- **PDF Generation**: gofpdf
- **Testing**: Go testing package + testify

### Infrastructure
- **Database**: PostgreSQL 12+
- **Connection Pooling**: pgxpool
- **Environment**: Docker-ready
- **Logging**: Structured logging
- **CORS**: Configurable cross-origin support

## Design Patterns

### Backend Architecture

#### Clean Architecture
The backend follows clean architecture principles:

1. **API Layer** (`internal/api/`): HTTP handlers, routing, middleware
2. **Service Layer** (`internal/services/`): Business logic, AI integration
3. **Repository Layer** (`internal/database/`): Data access abstraction
4. **Models** (`internal/models/`): Domain entities and DTOs

#### Repository Pattern
- Abstracts database operations
- Enables easy testing with mock implementations
- Provides consistent data access interface

#### Dependency Injection
- Services receive dependencies through constructors
- Enables loose coupling and testability
- Facilitates configuration management

### Frontend Architecture

#### Component-Based Design
- Reusable UI components in `src/components/`
- Page-level components in `src/pages/`
- Custom hooks for shared logic in `src/hooks/`

#### API Client Architecture
- Centralized HTTP client with interceptors
- Automatic token refresh handling
- Type-safe API calls with TypeScript

#### State Management Strategy
- Server state: React Query for caching and synchronization
- Client state: React hooks and context
- Form state: Controlled components with validation

## Security Architecture

### Authentication Flow
1. User login with email/password
2. Server validates credentials
3. JWT access token (15 min) + refresh token (7 days) issued
4. Client stores tokens securely
5. Automatic token refresh on expiry

### Authorization
- JWT tokens contain user claims
- Middleware validates tokens on protected routes
- Role-based access control ready for implementation

### Data Protection
- Password hashing with bcrypt
- SQL injection prevention with parameterized queries
- CORS configuration for cross-origin requests
- Input validation and sanitization

## Database Design

### Schema Organization
- **testspark_db schema**: Application tables
- **public schema**: Extensions and utilities
- Proper indexing for performance
- Foreign key constraints for data integrity

### Key Tables
- `users`: User accounts and profiles
- `tests`: Test instances and metadata
- `questions`: Generated questions and answers
- `test_results`: User responses and scores
- `topic_performance_summary`: Analytics aggregations

### Performance Optimizations
- Connection pooling (5-25 connections)
- Prepared statements for common queries
- Indexes on frequently queried columns
- Materialized views for analytics

## AI Integration Architecture

### Provider Abstraction
- Common interface for all AI providers
- Configurable primary/fallback providers
- Graceful degradation on provider failures

### Supported Providers
1. **Groq**: Primary provider for fast inference
2. **OpenAI**: Fallback with GPT models
3. **Hugging Face**: Open-source model support
4. **Mock Provider**: Testing and development

### Question Generation Pipeline
1. Context preparation (subject, difficulty, topics)
2. Prompt template selection
3. AI provider invocation
4. Response parsing and validation
5. Question storage and caching

## Scalability Considerations

### Horizontal Scaling
- Stateless backend design
- Database connection pooling
- Load balancer ready

### Performance Optimizations
- React Query caching
- Database query optimization
- Lazy loading of components
- Memoization of expensive operations

### Monitoring and Observability
- Structured logging throughout the application
- Performance monitoring hooks
- Error boundary implementation
- Health check endpoints

## Development Workflow

### Code Organization
- Modular architecture with clear separation of concerns
- Consistent naming conventions
- Comprehensive error handling
- Type safety with TypeScript and Go

### Testing Strategy
- Unit tests for business logic
- Integration tests for API endpoints
- Component tests for React components
- End-to-end testing capability

### Build and Deployment
- Automated build scripts
- Environment-based configuration
- Docker containerization ready
- CI/CD pipeline compatible
