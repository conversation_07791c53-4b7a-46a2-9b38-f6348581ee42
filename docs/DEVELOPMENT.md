# Test-Spark Development Guide

## Getting Started

This guide helps developers set up their environment and understand the codebase structure for contributing to Test-Spark.

## Development Environment Setup

### Prerequisites
- **Go 1.21+**: Backend development
- **Node.js 18+**: Frontend development
- **PostgreSQL 12+**: Database
- **Git**: Version control
- **VS Code** (recommended): IDE with Go and TypeScript extensions

### Quick Setup
```bash
# Clone repository
git clone <repository-url>
cd test-spark

# Run automated setup
chmod +x start-dev.sh
./start-dev.sh
```

This script will:
- Check prerequisites
- Set up environment files
- Install dependencies
- Start both backend and frontend servers

## Project Structure

```
test-spark/
├── cmd/api/                 # Application entry point
│   └── main.go             # Main server file
├── internal/               # Private application code
│   ├── api/                # HTTP handlers and routing
│   ├── auth/               # Authentication service
│   ├── config/             # Configuration management
│   ├── database/           # Repository pattern implementation
│   ├── models/             # Data models and DTOs
│   ├── services/           # Business logic layer
│   └── types/              # Type definitions
├── frontend/               # React frontend application
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Page-level components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── lib/            # Utilities and API client
│   │   └── stores/         # State management
│   ├── public/             # Static assets
│   └── dist/               # Build output
├── docs/                   # Documentation
├── migrations/             # Database migrations
├── templates/              # Report templates
├── schema.sql              # Database schema
├── go.mod                  # Go dependencies
└── README.md               # Project overview
```

## Backend Development

### Architecture Overview
The backend follows clean architecture principles with clear separation of concerns:

1. **API Layer** (`internal/api/`): HTTP handlers, middleware, routing
2. **Service Layer** (`internal/services/`): Business logic, AI integration
3. **Repository Layer** (`internal/database/`): Data access abstraction
4. **Models** (`internal/models/`): Domain entities and DTOs

### Adding New Endpoints

#### 1. Define the Model
```go
// internal/models/example.go
package models

type Example struct {
    ID          string    `json:"id" db:"id"`
    Name        string    `json:"name" db:"name"`
    Description string    `json:"description" db:"description"`
    CreatedAt   time.Time `json:"created_at" db:"created_at"`
}
```

#### 2. Create Repository Methods
```go
// internal/database/example_repository.go
package database

func (s *SQLStore) CreateExample(ctx context.Context, example *models.Example) error {
    query := `
        INSERT INTO examples (id, name, description, created_at)
        VALUES ($1, $2, $3, $4)`
    
    _, err := s.db.Exec(ctx, query, example.ID, example.Name, example.Description, example.CreatedAt)
    return err
}

func (s *SQLStore) GetExample(ctx context.Context, id string) (*models.Example, error) {
    query := `SELECT id, name, description, created_at FROM examples WHERE id = $1`
    
    var example models.Example
    err := s.db.QueryRow(ctx, query, id).Scan(
        &example.ID, &example.Name, &example.Description, &example.CreatedAt,
    )
    if err != nil {
        return nil, err
    }
    return &example, nil
}
```

#### 3. Implement Service Logic
```go
// internal/services/example_service.go
package services

type ExampleService struct {
    store database.Store
}

func NewExampleService(store database.Store) *ExampleService {
    return &ExampleService{store: store}
}

func (es *ExampleService) CreateExample(ctx context.Context, name, description string) (*models.Example, error) {
    example := &models.Example{
        ID:          uuid.New().String(),
        Name:        name,
        Description: description,
        CreatedAt:   time.Now(),
    }
    
    if err := es.store.CreateExample(ctx, example); err != nil {
        return nil, fmt.Errorf("failed to create example: %w", err)
    }
    
    return example, nil
}
```

#### 4. Create HTTP Handler
```go
// internal/api/example_handler.go
package api

type ExampleHandler struct {
    exampleService *services.ExampleService
}

func NewExampleHandler(exampleService *services.ExampleService) *ExampleHandler {
    return &ExampleHandler{exampleService: exampleService}
}

func (eh *ExampleHandler) CreateExample(w http.ResponseWriter, r *http.Request) {
    var req struct {
        Name        string `json:"name"`
        Description string `json:"description"`
    }
    
    if err := ParseJSONBody(r, &req); err != nil {
        WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
        return
    }
    
    example, err := eh.exampleService.CreateExample(r.Context(), req.Name, req.Description)
    if err != nil {
        WriteErrorResponse(w, http.StatusInternalServerError, "Failed to create example")
        return
    }
    
    WriteJSONResponse(w, http.StatusCreated, example)
}
```

#### 5. Add Routes
```go
// internal/api/router.go
func (router *Router) SetupRoutes() http.Handler {
    r := chi.NewRouter()
    
    // ... existing routes
    
    // Example routes
    r.Route("/api/v1/examples", func(r chi.Router) {
        r.Use(router.authService.RequireAuth)
        r.Post("/", router.exampleHandler.CreateExample)
        r.Get("/{id}", router.exampleHandler.GetExample)
    })
    
    return r
}
```

### Testing Backend Code

#### Unit Tests
```go
// internal/services/example_service_test.go
package services

func TestExampleService_CreateExample(t *testing.T) {
    // Setup
    mockStore := &database.MockStore{}
    service := NewExampleService(mockStore)
    
    // Test
    example, err := service.CreateExample(context.Background(), "Test", "Description")
    
    // Assert
    assert.NoError(t, err)
    assert.NotNil(t, example)
    assert.Equal(t, "Test", example.Name)
}
```

#### Integration Tests
```go
// internal/api/example_handler_test.go
package api

func TestExampleHandler_CreateExample(t *testing.T) {
    // Setup test server
    store := database.NewTestStore()
    service := services.NewExampleService(store)
    handler := NewExampleHandler(service)
    
    // Create request
    body := `{"name":"Test","description":"Description"}`
    req := httptest.NewRequest("POST", "/examples", strings.NewReader(body))
    req.Header.Set("Content-Type", "application/json")
    
    // Execute
    w := httptest.NewRecorder()
    handler.CreateExample(w, req)
    
    // Assert
    assert.Equal(t, http.StatusCreated, w.Code)
}
```

### Running Tests
```bash
# Run all tests
go test ./... -v

# Run specific package tests
go test ./internal/services/ -v

# Run with coverage
go test ./... -cover

# Run integration tests
go test ./internal/api/ -v
```

## Frontend Development

### Architecture Overview
The frontend uses modern React patterns with TypeScript:

1. **Components**: Reusable UI components using shadcn/ui
2. **Pages**: Route-level components
3. **Hooks**: Custom hooks for shared logic
4. **API Client**: Type-safe HTTP client with React Query
5. **State Management**: React Query for server state, React hooks for client state

### Adding New Components

#### 1. Create Component
```tsx
// src/components/example/ExampleCard.tsx
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface ExampleCardProps {
  title: string;
  description: string;
  onEdit?: () => void;
  onDelete?: () => void;
}

export function ExampleCard({ title, description, onEdit, onDelete }: ExampleCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground mb-4">{description}</p>
        <div className="flex gap-2">
          {onEdit && (
            <Button variant="outline" onClick={onEdit}>
              Edit
            </Button>
          )}
          {onDelete && (
            <Button variant="destructive" onClick={onDelete}>
              Delete
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
```

#### 2. Create API Hook
```tsx
// src/hooks/useExamples.ts
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@/lib/api-client";

interface Example {
  id: string;
  name: string;
  description: string;
  created_at: string;
}

export function useExamples() {
  return useQuery({
    queryKey: ["examples"],
    queryFn: () => apiClient.get<Example[]>("/examples"),
  });
}

export function useCreateExample() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: { name: string; description: string }) =>
      apiClient.post<Example>("/examples", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["examples"] });
    },
  });
}
```

#### 3. Create Page Component
```tsx
// src/pages/Examples.tsx
import { useState } from "react";
import { ExampleCard } from "@/components/example/ExampleCard";
import { useExamples, useCreateExample } from "@/hooks/useExamples";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function Examples() {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  
  const { data: examples, isLoading } = useExamples();
  const createExample = useCreateExample();
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createExample.mutate({ name, description });
    setName("");
    setDescription("");
  };
  
  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Examples</h1>
      
      <form onSubmit={handleSubmit} className="mb-6 space-y-4">
        <Input
          placeholder="Name"
          value={name}
          onChange={(e) => setName(e.target.value)}
        />
        <Input
          placeholder="Description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
        />
        <Button type="submit" disabled={createExample.isPending}>
          Create Example
        </Button>
      </form>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {examples?.map((example) => (
          <ExampleCard
            key={example.id}
            title={example.name}
            description={example.description}
          />
        ))}
      </div>
    </div>
  );
}
```

### Testing Frontend Code

#### Component Tests
```tsx
// src/components/example/__tests__/ExampleCard.test.tsx
import { render, screen, fireEvent } from "@testing-library/react";
import { ExampleCard } from "../ExampleCard";

describe("ExampleCard", () => {
  it("renders title and description", () => {
    render(
      <ExampleCard
        title="Test Title"
        description="Test Description"
      />
    );
    
    expect(screen.getByText("Test Title")).toBeInTheDocument();
    expect(screen.getByText("Test Description")).toBeInTheDocument();
  });
  
  it("calls onEdit when edit button is clicked", () => {
    const onEdit = jest.fn();
    render(
      <ExampleCard
        title="Test"
        description="Test"
        onEdit={onEdit}
      />
    );
    
    fireEvent.click(screen.getByText("Edit"));
    expect(onEdit).toHaveBeenCalled();
  });
});
```

#### Running Frontend Tests
```bash
cd frontend

# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run linting
npm run lint

# Run type checking
npm run type-check
```

## Code Style and Standards

### Go Code Style
- Follow standard Go formatting (`gofmt`)
- Use meaningful variable and function names
- Add comments for exported functions
- Handle errors explicitly
- Use context for cancellation and timeouts

### TypeScript/React Style
- Use TypeScript strict mode
- Prefer functional components with hooks
- Use proper TypeScript types (avoid `any`)
- Follow React best practices
- Use ESLint and Prettier for formatting

### Database Guidelines
- Use parameterized queries to prevent SQL injection
- Add proper indexes for performance
- Use transactions for multi-step operations
- Follow PostgreSQL naming conventions

## Debugging

### Backend Debugging
```bash
# Enable debug logging
export LOG_LEVEL=debug

# Use Go debugger (delve)
go install github.com/go-delve/delve/cmd/dlv@latest
dlv debug cmd/api/main.go

# Check database connections
psql $DATABASE_URL -c "SELECT 1;"
```

### Frontend Debugging
```bash
# Use browser dev tools
# React Developer Tools extension
# Check network tab for API calls
# Use console.log for debugging (remove before commit)

# Check build issues
npm run build
npm run preview
```

## Contributing Guidelines

### Before Submitting
1. Run tests: `go test ./...` and `npm test`
2. Check linting: `npm run lint`
3. Update documentation if needed
4. Add tests for new features
5. Follow commit message conventions

### Pull Request Process
1. Create feature branch from main
2. Make changes with proper tests
3. Update documentation
4. Submit pull request with description
5. Address review feedback
6. Merge after approval

### Commit Message Format
```
type(scope): description

feat(auth): add password reset functionality
fix(api): handle null values in analytics
docs(readme): update installation instructions
test(services): add unit tests for user service
```
