# Mixed Generation Feature

## Overview

The Mixed Generation feature allows Test-Spark to distribute question generation across multiple AI providers simultaneously. This provides better variety, reduces dependency on a single provider, and allows for optimal resource utilization.

## Key Features

- **Multi-Provider Distribution**: Distribute questions across multiple AI providers (e.g., 60% Cerebras, 40% Groq)
- **Configurable Ratios**: Easily adjust the distribution ratios via API
- **Fallback Support**: Automatic fallback if any provider fails
- **Provider Attribution**: Questions are tagged with their generating provider
- **Randomization**: Optional shuffling of questions from different providers

## Configuration

### Default Configuration

```json
{
  "provider_ratios": {
    "cerebras": 0.6,
    "groq": 0.4
  },
  "min_questions_per_provider": 1,
  "enable_randomization": true,
  "enable_fallback": true
}
```

### Configuration Parameters

- **provider_ratios**: Map of provider names to their allocation ratios (must sum to 1.0)
- **min_questions_per_provider**: Minimum questions each provider should generate
- **enable_randomization**: Whether to shuffle questions from different providers
- **enable_fallback**: Whether to use fallback generation if mixed generation fails

## API Endpoints

### Get Current Configuration

```bash
GET /api/v1/tests/mixed-generation/config
Authorization: Bearer <token>
```

**Response:**
```json
{
  "provider_ratios": {
    "cerebras": 0.6,
    "groq": 0.4
  },
  "min_questions_per_provider": 1,
  "enable_randomization": true,
  "enable_fallback": true
}
```

### Update Configuration

```bash
PUT /api/v1/tests/mixed-generation/config
Authorization: Bearer <token>
Content-Type: application/json

{
  "provider_ratios": {
    "cerebras": 0.7,
    "groq": 0.3
  },
  "min_questions_per_provider": 2,
  "enable_randomization": true,
  "enable_fallback": true
}
```

**Response:**
```json
{
  "message": "Mixed generation configuration updated successfully",
  "config": {
    "provider_ratios": {
      "cerebras": 0.7,
      "groq": 0.3
    },
    "min_questions_per_provider": 2,
    "enable_randomization": true,
    "enable_fallback": true
  }
}
```

## How It Works

### Question Allocation

When a test requests 5 questions with the default configuration:

1. **Cerebras**: 5 × 0.6 = 3 questions
2. **Groq**: 5 × 0.4 = 2 questions

### Generation Process

1. Calculate allocation for each provider based on ratios
2. Generate questions from each provider in parallel
3. Tag questions with provider information
4. Shuffle questions if randomization is enabled
5. Return combined question set

### Fallback Mechanism

If any provider fails:
1. Log the failure
2. Continue with available providers
3. If total questions are insufficient, use fallback generation
4. Ensure minimum question count is met

## Provider Attribution

Questions generated through mixed generation are tagged in the `author_ai_model` field:

- **Cerebras questions**: `"cerebras-mixed"`
- **Groq questions**: `"groq-mixed"`
- **Fallback questions**: `"fallback-mixed"`

## Environment Configuration

Ensure both providers are configured in your `.env` file:

```bash
# Groq Configuration
GROQ_API_KEY=your_groq_api_key
GROQ_MODEL=llama-3.3-70b-versatile
GROQ_TEMPERATURE=0.7
GROQ_MAX_TOKENS=4000

# Cerebras Configuration
CEREBRAS_API_KEY=your_cerebras_api_key
CEREBRAS_MODEL=llama3.1-8b
CEREBRAS_TEMPERATURE=0.7
CEREBRAS_MAX_TOKENS=4000
CEREBRAS_BASE_URL=https://api.cerebras.ai/v1/chat/completions
```

## Usage Examples

### Example 1: Equal Distribution

```json
{
  "provider_ratios": {
    "cerebras": 0.5,
    "groq": 0.5
  },
  "min_questions_per_provider": 1,
  "enable_randomization": true,
  "enable_fallback": true
}
```

For 10 questions: 5 from Cerebras, 5 from Groq

### Example 2: Cerebras-Heavy

```json
{
  "provider_ratios": {
    "cerebras": 0.8,
    "groq": 0.2
  },
  "min_questions_per_provider": 1,
  "enable_randomization": true,
  "enable_fallback": true
}
```

For 10 questions: 8 from Cerebras, 2 from Groq

### Example 3: Three Providers

```json
{
  "provider_ratios": {
    "cerebras": 0.5,
    "groq": 0.3,
    "openai": 0.2
  },
  "min_questions_per_provider": 1,
  "enable_randomization": true,
  "enable_fallback": true
}
```

For 10 questions: 5 from Cerebras, 3 from Groq, 2 from OpenAI

## Benefits

1. **Variety**: Different providers may generate different styles of questions
2. **Reliability**: Reduced dependency on a single provider
3. **Cost Optimization**: Use cheaper providers for bulk generation
4. **Performance**: Parallel generation can be faster
5. **Quality**: Combine strengths of different models

## Monitoring

Monitor mixed generation through:

- **Logs**: Check provider allocation and generation success
- **Question Attribution**: Analyze which providers generated which questions
- **Performance Metrics**: Compare generation times and success rates
- **Quality Assessment**: Evaluate question quality by provider

## Testing

Use the provided test script to verify mixed generation:

```bash
./test-mixed-generation.sh
```

This script will:
1. Login to the system
2. Get current configuration
3. Update configuration
4. Create tests with different configurations
5. Demonstrate the mixed generation feature

## Troubleshooting

### Common Issues

1. **Provider Not Available**: Check API keys and network connectivity
2. **Ratio Validation Error**: Ensure ratios sum to 1.0
3. **Insufficient Questions**: Check minimum questions per provider setting
4. **Fallback Triggered**: Review provider availability and error logs

### Debug Steps

1. Check provider configuration in `.env`
2. Verify API keys are valid
3. Test individual providers separately
4. Review application logs for errors
5. Validate configuration parameters

## Future Enhancements

- **Dynamic Ratios**: Adjust ratios based on provider performance
- **Cost-Based Allocation**: Consider provider costs in allocation
- **Quality Metrics**: Use question quality scores for allocation
- **Load Balancing**: Distribute load based on provider capacity
- **A/B Testing**: Compare different allocation strategies
