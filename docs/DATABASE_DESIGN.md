# Test-Spark Database Design

## Overview

The Test-Spark database is designed to support an adaptive learning platform with user management, test generation, performance analytics, and reporting capabilities. The schema uses PostgreSQL with the `testspark_db` schema for application tables.

## Database Schema

### Schema Organization

```sql
-- Main application schema
CREATE SCHEMA IF NOT EXISTS testspark_db;
SET search_path TO testspark_db;
```

### Custom Types

```sql
-- Enumerated types for data consistency
CREATE TYPE difficulty_level AS ENUM ('easy', 'medium', 'hard');
CREATE TYPE test_status AS ENUM ('pending', 'in_progress', 'completed', 'cancelled');
```

## Core Tables

### User Management

#### users
Primary user accounts and authentication.

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

**Indexes:**
- `idx_users_email` on `email` (unique constraint + fast lookup)

#### user_profiles
Extended user information and preferences.

```sql
CREATE TABLE user_profiles (
    user_id UUID PRIMARY KEY,
    full_name VARCHAR(255),
    age INT,
    board VARCHAR(100),        -- Educational board (CBSE, ICSE, etc.)
    class VARCHAR(100),        -- Grade/Class level
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### refresh_tokens
JWT refresh token management for secure authentication.

```sql
CREATE TABLE refresh_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    is_revoked BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

**Indexes:**
- `idx_refresh_tokens_user_id` on `user_id`
- `idx_refresh_tokens_token_hash` on `token_hash`

### Content Management

#### subjects
Academic subjects (Mathematics, Science, etc.).

```sql
CREATE TABLE subjects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT
);
```

#### exams
Standardized exams and test formats.

```sql
CREATE TABLE exams (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT
);
```

#### topics
Subject subdivisions for granular performance tracking.

```sql
CREATE TABLE topics (
    id SERIAL PRIMARY KEY,
    subject_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    CONSTRAINT fk_subject FOREIGN KEY(subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    UNIQUE(subject_id, name)
);
```

**Indexes:**
- `idx_topics_subject_id` on `subject_id`

#### exam_subjects
Many-to-many relationship between exams and subjects.

```sql
CREATE TABLE exam_subjects (
    exam_id INT NOT NULL,
    subject_id INT NOT NULL,
    PRIMARY KEY (exam_id, subject_id),
    CONSTRAINT fk_exam FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
    CONSTRAINT fk_subject FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);
```

### Question Bank

#### questions
AI-generated questions with metadata.

```sql
CREATE TABLE questions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    topic_id INT NOT NULL,
    content JSONB NOT NULL,           -- Question text, options, correct answer
    difficulty difficulty_level,
    author_ai_model VARCHAR(100),     -- AI model that generated the question
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_topic FOREIGN KEY(topic_id) REFERENCES topics(id) ON DELETE SET NULL
);
```

**Content JSONB Structure:**
```json
{
  "question": "What is 2 + 2?",
  "options": ["3", "4", "5", "6"],
  "correct_option": 1,
  "explanation": "2 + 2 equals 4"
}
```

**Indexes:**
- `idx_questions_topic_id` on `topic_id`
- `idx_questions_difficulty` on `difficulty`
- `idx_questions_topic_difficulty` on `(topic_id, difficulty)`

### Test Management

#### tests
Test instances and lifecycle management.

```sql
CREATE TABLE tests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    test_context_subject_id INT,
    test_context_exam_id INT,
    status test_status NOT NULL DEFAULT 'pending',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_subject FOREIGN KEY(test_context_subject_id) REFERENCES subjects(id) ON DELETE SET NULL,
    CONSTRAINT fk_exam FOREIGN KEY(test_context_exam_id) REFERENCES exams(id) ON DELETE SET NULL
);
```

**Indexes:**
- `idx_tests_user_id` on `user_id`
- `idx_tests_user_completed_subject` on `(user_id, completed_at, test_context_subject_id)`

#### test_questions
Questions assigned to specific tests with ordering.

```sql
CREATE TABLE test_questions (
    id SERIAL PRIMARY KEY,
    test_id UUID NOT NULL,
    question_id UUID NOT NULL,
    question_order INT NOT NULL,
    CONSTRAINT fk_test FOREIGN KEY(test_id) REFERENCES tests(id) ON DELETE CASCADE,
    CONSTRAINT fk_question FOREIGN KEY(question_id) REFERENCES questions(id) ON DELETE RESTRICT,
    UNIQUE(test_id, question_id),
    UNIQUE(test_id, question_order)
);
```

**Indexes:**
- `idx_test_questions_test_id` on `test_id`
- `idx_test_questions_question_id` on `question_id`
- `idx_test_questions_test_order` on `(test_id, question_order)`

#### user_answers
User responses to test questions.

```sql
CREATE TABLE user_answers (
    id SERIAL PRIMARY KEY,
    test_question_id INT UNIQUE NOT NULL,
    selected_option_index INT NOT NULL,
    is_correct BOOLEAN NOT NULL,
    answered_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_test_question FOREIGN KEY(test_question_id) REFERENCES test_questions(id) ON DELETE CASCADE
);
```

**Indexes:**
- `idx_user_answers_timing` on `(answered_at DESC, is_correct)`

### Analytics and Performance

#### test_results
Aggregated test results for performance analysis.

```sql
CREATE TABLE test_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    test_id UUID UNIQUE NOT NULL,
    user_id UUID NOT NULL,
    score DECIMAL(5, 2) NOT NULL,
    total_questions INT NOT NULL,
    correct_answers INT NOT NULL,
    incorrect_answers INT NOT NULL,
    time_taken_seconds INT NOT NULL,
    CONSTRAINT fk_test FOREIGN KEY(test_id) REFERENCES tests(id) ON DELETE CASCADE,
    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

**Indexes:**
- `idx_test_results_user_id` on `user_id`
- `idx_test_results_user_completed_at` on `(user_id, created_at DESC)`
- `idx_test_results_score_date` on `(user_id, score, created_at DESC)`

#### topic_performance_summary
Pre-aggregated topic performance for fast analytics.

```sql
CREATE TABLE topic_performance_summary (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    topic_id INT NOT NULL,
    total_attempted INT NOT NULL DEFAULT 0,
    total_correct INT NOT NULL DEFAULT 0,
    proficiency_score DECIMAL(5, 2) NOT NULL DEFAULT 0.00,
    last_tested_at TIMESTAMPTZ,
    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_topic FOREIGN KEY(topic_id) REFERENCES topics(id) ON DELETE CASCADE,
    UNIQUE(user_id, topic_id)
);
```

**Indexes:**
- `idx_topic_performance_summary_user_id_topic_id` on `(user_id, topic_id)`
- `idx_topic_performance_user_score` on `(user_id, proficiency_score DESC)`
- `idx_topic_performance_weak_topics` on `(user_id, proficiency_score ASC, topic_id)` WHERE `proficiency_score < 70`
- `idx_topic_performance_strong_topics` on `(user_id, proficiency_score DESC, topic_id)` WHERE `proficiency_score >= 70`

## Views

### v_user_test_history
Simplified view for user test history with scores.

```sql
CREATE OR REPLACE VIEW v_user_test_history AS
SELECT
    t.id AS test_id,
    t.user_id,
    t.status,
    t.created_at,
    t.completed_at,
    s.name AS subject_name,
    e.name AS exam_name,
    tr.score,
    tr.total_questions,
    tr.correct_answers,
    tr.incorrect_answers,
    tr.time_taken_seconds
FROM tests t
LEFT JOIN subjects s ON t.test_context_subject_id = s.id
LEFT JOIN exams e ON t.test_context_exam_id = e.id
LEFT JOIN test_results tr ON t.id = tr.test_id
ORDER BY t.created_at DESC;
```

## Triggers and Functions

### Automatic Performance Summary Updates

The database includes an automated trigger system that updates topic performance summaries when tests are completed:

```sql
CREATE OR REPLACE FUNCTION update_topic_performance_summary()
RETURNS TRIGGER AS $$
-- Function updates topic_performance_summary table
-- when new test results are inserted
$$;

CREATE TRIGGER trg_update_topic_performance
AFTER INSERT ON test_results
FOR EACH ROW
EXECUTE FUNCTION update_topic_performance_summary();
```

**Purpose:**
- Automatically calculates proficiency scores
- Updates attempt and correct answer counts
- Maintains real-time analytics data

## Performance Optimizations

### Indexing Strategy

1. **Primary Keys**: All tables use appropriate primary keys (UUID for entities, SERIAL for relationships)
2. **Foreign Keys**: All foreign key columns are indexed
3. **Query Patterns**: Indexes optimized for common query patterns:
   - User-specific data retrieval
   - Time-based filtering
   - Performance analytics
   - Weak/strong topic identification

### Materialized Views (Optional)

For high-traffic applications, consider materialized views for complex analytics:

```sql
-- Pre-computed performance trends
CREATE MATERIALIZED VIEW mv_user_performance_trends AS
SELECT 
    tr.user_id,
    DATE_TRUNC('week', t.completed_at) as week_start,
    AVG(tr.score) as avg_score,
    COUNT(*) as test_count
FROM test_results tr
JOIN tests t ON tr.test_id = t.id
WHERE t.completed_at IS NOT NULL
GROUP BY tr.user_id, week_start;
```

### Connection Pooling

The application uses pgxpool for efficient connection management:
- Maximum connections: 25
- Minimum connections: 5
- Connection lifetime: 1 hour
- Idle timeout: 30 minutes

## Data Integrity

### Constraints

1. **Foreign Key Constraints**: Ensure referential integrity
2. **Unique Constraints**: Prevent duplicate data
3. **Check Constraints**: Validate data ranges and formats
4. **NOT NULL Constraints**: Ensure required fields

### Cascade Rules

- **User Deletion**: Cascades to all user-related data
- **Subject/Topic Deletion**: Sets foreign keys to NULL in questions
- **Test Deletion**: Cascades to test questions and answers

## Security Considerations

### Data Protection

1. **Password Hashing**: Passwords stored as bcrypt hashes
2. **Token Security**: Refresh tokens hashed and time-limited
3. **SQL Injection Prevention**: Parameterized queries only
4. **Schema Isolation**: Application data in dedicated schema

### Access Control

1. **Database User Permissions**: Minimal required permissions
2. **Connection Security**: SSL required in production
3. **Audit Logging**: Track data modifications
4. **Backup Encryption**: Encrypted database backups

## Migration Strategy

### Version Control

Database changes are managed through numbered migration files:
- `001_initial_schema.sql`: Base schema
- `002_add_analytics.sql`: Analytics enhancements
- `003_performance_optimization.sql`: Index optimizations

### Deployment Process

1. **Backup**: Create database backup before migration
2. **Test**: Run migrations on staging environment
3. **Deploy**: Apply migrations to production
4. **Verify**: Confirm schema integrity and performance
