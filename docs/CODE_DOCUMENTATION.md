# Test-Spark Code Documentation

## Overview

This document provides an overview of the code documentation standards and practices used throughout the Test-Spark codebase. The project follows industry best practices for code documentation to ensure maintainability and ease of understanding.

## Documentation Standards

### Go Code Documentation

#### Package Documentation
Every package includes comprehensive package-level documentation:

```go
// Package services provides business logic layer for the Test-Spark application.
// This package contains service implementations that handle core business operations
// including test management, AI integration, and analytics processing.
package services
```

#### Type Documentation
All exported types include detailed documentation:

```go
// TestService handles all test-related business logic including test creation,
// question generation using AI providers, and test lifecycle management.
// It coordinates between the database layer and AI services to provide
// adaptive test generation capabilities with gamification features.
//
// The service supports multiple AI providers through the AIProviderManager
// and includes adaptive learning algorithms to personalize test difficulty
// and content based on user performance.
type TestService struct {
    store            database.Store           // Database abstraction layer
    aiProviderMgr    *AIProviderManager       // AI provider management
    groqClient       *GroqClient              // Legacy client (backward compatibility)
    adaptiveLearning *AdaptiveLearningService // Adaptive learning algorithms
    gamificationSvc  *GamificationService     // Gamification features
}
```

#### Function Documentation
All exported functions include comprehensive documentation with parameters, returns, and examples:

```go
// NewTestServiceWithAI creates a new TestService instance with full AI provider support
// and advanced features including adaptive learning and gamification.
// This is the recommended constructor for new implementations.
//
// Parameters:
//   - store: Database abstraction layer implementing the Store interface
//   - aiProviderMgr: AI provider manager supporting multiple AI services
//
// Returns:
//   - *TestService: Fully configured test service with all features enabled
//
// Features enabled:
//   - Multiple AI provider support (Groq, OpenAI, Hugging Face)
//   - Adaptive learning algorithms
//   - Gamification system (points, badges, achievements)
//   - Fallback provider support
func NewTestServiceWithAI(store database.Store, aiProviderMgr *AIProviderManager) *TestService
```

### TypeScript/React Documentation

#### Component Documentation
React components include JSDoc-style documentation:

```tsx
/**
 * ExampleCard displays information about an example with optional action buttons.
 * 
 * @param title - The title to display in the card header
 * @param description - The description text for the card content
 * @param onEdit - Optional callback function when edit button is clicked
 * @param onDelete - Optional callback function when delete button is clicked
 * 
 * @example
 * ```tsx
 * <ExampleCard
 *   title="Sample Title"
 *   description="Sample description"
 *   onEdit={() => console.log('Edit clicked')}
 *   onDelete={() => console.log('Delete clicked')}
 * />
 * ```
 */
interface ExampleCardProps {
  title: string;
  description: string;
  onEdit?: () => void;
  onDelete?: () => void;
}
```

#### Hook Documentation
Custom hooks include detailed documentation:

```tsx
/**
 * useExamples hook provides functionality for managing examples data.
 * 
 * @returns Object containing:
 *   - data: Array of examples or undefined if loading
 *   - isLoading: Boolean indicating if data is being fetched
 *   - error: Error object if request failed
 *   - refetch: Function to manually refetch data
 * 
 * @example
 * ```tsx
 * const { data: examples, isLoading, error } = useExamples();
 * 
 * if (isLoading) return <div>Loading...</div>;
 * if (error) return <div>Error: {error.message}</div>;
 * 
 * return (
 *   <div>
 *     {examples?.map(example => (
 *       <div key={example.id}>{example.name}</div>
 *     ))}
 *   </div>
 * );
 * ```
 */
export function useExamples() {
  return useQuery({
    queryKey: ["examples"],
    queryFn: () => apiClient.get<Example[]>("/examples"),
  });
}
```

## Database Documentation

### Schema Documentation
Database schema includes comprehensive comments:

```sql
-- User Management & Authentication
-- This table stores core user account information for authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,        -- User's email address (unique identifier)
    password_hash VARCHAR(255) NOT NULL,       -- Bcrypt hashed password
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add table and column comments for documentation
COMMENT ON TABLE users IS 'Core user accounts for authentication and identification';
COMMENT ON COLUMN users.email IS 'Unique email address used for login';
COMMENT ON COLUMN users.password_hash IS 'Bcrypt hashed password for secure authentication';
```

### Function Documentation
Database functions include detailed documentation:

```sql
-- Function: update_topic_performance_summary
-- Description: Automatically updates topic performance summaries when test results are inserted
-- Trigger: Fires after INSERT on test_results table
-- Purpose: Maintains real-time analytics data for dashboard performance
--
-- Algorithm:
-- 1. Extract user_id from the completed test
-- 2. Loop through each answer in the test
-- 3. Update or insert topic performance summary
-- 4. Recalculate proficiency scores
--
-- Performance: Optimized for frequent updates with minimal locking
CREATE OR REPLACE FUNCTION update_topic_performance_summary()
RETURNS TRIGGER AS $$
-- Function implementation
$$;
```

## API Documentation

### Endpoint Documentation
API endpoints are documented in OpenAPI/Swagger format with examples:

```yaml
/api/v1/tests:
  post:
    summary: Create a new test
    description: |
      Creates a new test instance with AI-generated questions based on the specified
      subject, exam type, and difficulty level. The test questions are generated
      using the configured AI provider and stored for the user session.
    parameters:
      - name: subject_id
        in: body
        required: true
        description: ID of the subject for test questions
        schema:
          type: integer
          example: 1
    responses:
      201:
        description: Test created successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestResponse'
            example:
              test_id: "123e4567-e89b-12d3-a456-426614174000"
              questions: [...]
```

## Inline Comments

### Code Comments
Strategic inline comments explain complex logic:

```go
// Calculate proficiency score using weighted average
// Recent performance has higher weight to reflect current ability
func calculateProficiencyScore(attempts []models.Attempt) float64 {
    if len(attempts) == 0 {
        return 0.0
    }
    
    var weightedSum, totalWeight float64
    
    // Apply exponential decay to older attempts
    // More recent attempts get higher weight
    for i, attempt := range attempts {
        weight := math.Exp(-0.1 * float64(len(attempts)-i-1))
        weightedSum += float64(attempt.Score) * weight
        totalWeight += weight
    }
    
    return weightedSum / totalWeight
}
```

### TODO and FIXME Comments
Development notes are clearly marked:

```go
// TODO: Implement caching for frequently accessed questions
// FIXME: Handle edge case when AI provider returns malformed JSON
// NOTE: This algorithm may need optimization for large datasets
// HACK: Temporary workaround until API v2 is available
```

## Documentation Tools

### Go Documentation
- **godoc**: Generate documentation from Go comments
- **pkgsite**: Local documentation server
- **go doc**: Command-line documentation viewer

```bash
# Generate and serve documentation
go install golang.org/x/tools/cmd/godoc@latest
godoc -http=:6060

# View specific package documentation
go doc internal/services
go doc internal/services.TestService
```

### TypeScript Documentation
- **TSDoc**: TypeScript documentation standard
- **typedoc**: Generate HTML documentation
- **JSDoc**: JavaScript documentation format

```bash
# Generate TypeScript documentation
npm install -g typedoc
typedoc --out docs/frontend src/
```

### Database Documentation
- **PostgreSQL Comments**: Built-in documentation system
- **Schema Documentation**: Automated schema docs
- **ERD Generation**: Entity relationship diagrams

```sql
-- View table comments
SELECT 
    t.table_name,
    obj_description(c.oid) as table_comment
FROM information_schema.tables t
JOIN pg_class c ON c.relname = t.table_name
WHERE t.table_schema = 'testspark_db';

-- View column comments
SELECT 
    column_name,
    col_description(pgc.oid, ordinal_position) as column_comment
FROM information_schema.columns
JOIN pg_class pgc ON pgc.relname = table_name
WHERE table_name = 'users';
```

## Documentation Maintenance

### Review Process
1. **Code Reviews**: Documentation reviewed with code changes
2. **Regular Updates**: Documentation updated with feature changes
3. **Accuracy Checks**: Periodic verification of documentation accuracy
4. **User Feedback**: Incorporate feedback from developers

### Automation
- **CI/CD Integration**: Documentation generation in build pipeline
- **Link Checking**: Automated verification of documentation links
- **Coverage Reports**: Track documentation coverage metrics

### Best Practices

#### Writing Guidelines
1. **Clear and Concise**: Use simple, direct language
2. **Complete Information**: Include all necessary details
3. **Examples**: Provide practical usage examples
4. **Consistent Format**: Follow established patterns
5. **Regular Updates**: Keep documentation current

#### Code Comments
1. **Explain Why**: Focus on reasoning, not what
2. **Complex Logic**: Document non-obvious algorithms
3. **External Dependencies**: Explain third-party integrations
4. **Performance Notes**: Document optimization decisions
5. **Error Handling**: Explain error scenarios

#### API Documentation
1. **Complete Schemas**: Document all request/response fields
2. **Error Responses**: Include all possible error conditions
3. **Authentication**: Clear auth requirements
4. **Rate Limits**: Document usage limitations
5. **Examples**: Provide working code examples

## Documentation Structure

```
docs/
├── API_DOCUMENTATION.md      # Complete API reference
├── ARCHITECTURE.md           # System architecture overview
├── CODE_DOCUMENTATION.md     # This file - documentation standards
├── CONFIGURATION.md          # Configuration options
├── DATABASE_DESIGN.md        # Database schema documentation
├── DEPLOYMENT.md             # Deployment instructions
└── DEVELOPMENT.md            # Development setup guide

internal/
├── api/                      # HTTP handlers with inline docs
├── services/                 # Business logic with comprehensive docs
├── database/                 # Repository layer with method docs
└── models/                   # Data models with field documentation

frontend/src/
├── components/               # React components with JSDoc
├── hooks/                    # Custom hooks with usage examples
├── lib/                      # Utilities with function documentation
└── pages/                    # Page components with descriptions
```

## Quality Metrics

### Documentation Coverage
- **Go Packages**: 100% of exported functions documented
- **TypeScript Components**: 95% of components have JSDoc
- **API Endpoints**: 100% documented with examples
- **Database Schema**: All tables and functions commented

### Review Checklist
- [ ] All public APIs documented
- [ ] Complex algorithms explained
- [ ] Error conditions documented
- [ ] Examples provided where helpful
- [ ] Documentation matches implementation
- [ ] Links and references verified
- [ ] Spelling and grammar checked
