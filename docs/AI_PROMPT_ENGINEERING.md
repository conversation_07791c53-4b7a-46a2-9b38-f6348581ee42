# AI Prompt Engineering & Provider System

This document describes the enhanced AI prompt engineering system and provider-agnostic architecture implemented for Test-Spark.

## Overview

The system has been redesigned with three key improvements:

1. **100% Accuracy Focus**: Enhanced prompt templates with multi-layer validation
2. **Grade/Class Adaptation**: Age-appropriate question generation based on student level
3. **Provider Agnostic**: Support for multiple AI providers (Groq, OpenAI, etc.) with easy switching

## Key Features

### 🎯 Enhanced Prompt Engineering

- **Multi-layer validation** ensures 100% answer accuracy
- **Grade-specific guidelines** for age-appropriate content
- **Board-specific standards** (CBSE, ICSE, IB, State boards)
- **Cognitive development alignment** based on educational psychology
- **Comprehensive quality assurance** with mandatory validation checks

### 🔄 Provider-Agnostic Architecture

- **Interface-based design** allows easy provider switching
- **Automatic fallback** to secondary providers if primary fails
- **Configuration-driven** provider selection
- **Unified API** regardless of underlying provider

### 📚 Grade & Board Support

- **Automatic grade detection** from user profiles
- **Educational board alignment** (CBSE, ICSE, IB, State)
- **Age-appropriate vocabulary** and complexity
- **Curriculum standard compliance**

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Test Service  │───▶│ AI Provider Mgr  │───▶│  Groq Provider  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                ┌─────────────────┐
                                └───────────────▶│ OpenAI Provider │
                                                 └─────────────────┘
                                                 ┌─────────────────┐
                                                 │  Mock Provider  │
                                                 └─────────────────┘
```

## Prompt Template System

### Template Structure

```
1. System Prompt (Expert Persona)
2. Educational Context (Subject, Grade, Board)
3. Generation Requirements
4. Critical Accuracy Requirements
5. Grade-Specific Guidelines
6. Board-Specific Guidelines
7. Validation Protocol
8. Output Format Specification
```

### Accuracy Validation Layers

1. **Factual Verification**: Cross-reference with authoritative sources
2. **Logical Consistency**: Ensure sound reasoning and no contradictions
3. **Educational Appropriateness**: Grade-level and curriculum alignment
4. **Distractor Quality**: Plausible but definitively incorrect options
5. **Explanation Validation**: Clear, educational explanations

### Grade-Specific Adaptations

#### Primary (Grades 1-5)
- Simple, concrete language
- Visual and hands-on thinking scenarios
- Fundamental concepts and basic applications
- Familiar, everyday examples

#### Middle School (Grades 6-8)
- Bridge concrete and abstract thinking
- Moderate complexity with clear explanations
- Analytical and application-based questions
- Age-appropriate examples and scenarios

#### Secondary (Grades 9-10)
- Academic vocabulary and concepts
- Analytical and synthesis-level questions
- Conceptual understanding and application
- Complex problem-solving scenarios

#### Higher Secondary (Grades 11-12)
- Advanced academic language
- High-level analytical and evaluative questions
- Critical thinking and complex applications
- Preparation for competitive exams

## Configuration

### Environment Variables

```bash
# Primary provider selection
AI_PRIMARY_PROVIDER=groq
AI_FALLBACK_PROVIDER=mock

# Provider-specific configuration
GROQ_API_KEY=your_groq_api_key
GROQ_MODEL=llama-3.3-70b-versatile
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4

# Feature toggles
AI_ENABLE_GRADE_ADAPTATION=true
AI_ENABLE_BOARD_SUPPORT=true
AI_ENABLE_VALIDATION=true
```

### Provider Configuration

```go
config := &AIProviderConfig{
    PrimaryProvider:  "groq",
    FallbackProvider: "mock",
    EnableFallback:   true,
    GroqConfig: &GroqProviderConfig{
        APIKey:      os.Getenv("GROQ_API_KEY"),
        Model:       "llama-3.3-70b-versatile",
        Temperature: 0.7,
        MaxTokens:   4000,
    },
}
```

## Usage Examples

### Creating Test Service with AI Provider

```go
// Load configuration
config, err := config.LoadAIConfig()
if err != nil {
    log.Fatal(err)
}

// Create service factory
factory := NewAIServiceFactory(config, store)

// Create test service with AI provider manager
testService, err := factory.CreateTestService()
if err != nil {
    log.Fatal(err)
}
```

### Grade-Aware Question Generation

```go
// Request with grade and board information
req := &CreateTestRequest{
    SubjectID:        &mathSubjectID,
    NumQuestions:     10,
    DifficultyLevels: []DifficultyLevel{DifficultyMedium},
    Grade:            stringPtr("Class 10"),
    Board:            stringPtr("CBSE"),
}

// Generate questions with grade-specific content
test, err := testService.CreateNewTest(ctx, userID, req)
```

## Quality Assurance

### Validation Checklist

Every generated question must pass:

✓ **Factual Accuracy**: 100% correct and verifiable
✓ **Answer Certainty**: Unambiguous correct answer
✓ **Distractor Quality**: Clearly wrong but plausible options
✓ **Grade Appropriateness**: Suitable for target grade level
✓ **Educational Value**: Tests meaningful understanding
✓ **Language Clarity**: Clear, grammatically correct
✓ **Curriculum Alignment**: Matches educational standards

### Error Prevention

- **Multi-source verification** for factual content
- **Expert review simulation** in prompts
- **Common misconception testing** for distractors
- **Readability analysis** for grade appropriateness
- **Curriculum standard mapping** for alignment

## Provider Comparison

| Feature | Groq | OpenAI | Mock |
|---------|------|--------|------|
| Speed | ⚡ Fast | 🐌 Moderate | ⚡ Instant |
| Cost | 💰 Low | 💰💰 High | 🆓 Free |
| Quality | ⭐⭐⭐ Good | ⭐⭐⭐⭐⭐ Excellent | ⭐⭐ Basic |
| Reliability | ⭐⭐⭐ Good | ⭐⭐⭐⭐ Very Good | ⭐⭐⭐⭐⭐ Perfect |
| Setup | 🔧 Easy | 🔧 Easy | 🔧 None |

## Best Practices

### For Development
1. Use Groq as primary with Mock fallback
2. Enable all validation features
3. Test with various grade levels and boards
4. Monitor prompt effectiveness

### For Production
1. Use OpenAI as primary with Groq fallback
2. Implement proper rate limiting
3. Monitor API costs and usage
4. Set up alerting for provider failures

### For Testing
1. Use Mock provider for unit tests
2. Test provider switching scenarios
3. Validate grade-specific adaptations
4. Verify fallback mechanisms

## Troubleshooting

### Common Issues

1. **API Key Errors**: Verify keys are correct and active
2. **Rate Limiting**: Check provider quotas and limits
3. **Quality Issues**: Review prompt templates and validation
4. **Grade Mismatches**: Verify user profile data
5. **Provider Failures**: Check fallback configuration

### Debugging

```go
// Check provider status
status, err := factory.GetProviderStatus()
for name, info := range status {
    log.Printf("Provider %s: Available=%v, Primary=%v", 
        name, info.IsAvailable, info.IsPrimary)
}

// Validate configuration
if err := factory.ValidateConfiguration(); err != nil {
    log.Printf("Configuration error: %v", err)
}
```

## Future Enhancements

- **Adaptive difficulty**: Dynamic difficulty adjustment based on performance
- **Content personalization**: Tailored content based on learning style
- **Multi-language support**: Questions in regional languages
- **Real-time validation**: Live fact-checking during generation
- **Performance analytics**: Detailed metrics on question quality
