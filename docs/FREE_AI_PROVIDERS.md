# Free AI Providers for Test-Spark

This document provides a comprehensive guide to setting up and using free AI providers with Test-Spark for question generation.

## Overview

Test-Spark supports multiple AI providers with different free tiers and capabilities:

1. **Groq** - Fast inference with Llama models (Primary recommendation)
2. **Hugging Face Inference API** - Large variety of open-source models
3. **Google Gemini Flash** - Google's efficient AI model
4. **OpenAI** - Limited free tier available

## 1. Groq (Recommended Primary)

### Features
- **Free Tier**: Generous free usage limits
- **Models**: Llama 3.3 70B, Llama 3.1 70B, Mixtral 8x7B
- **Speed**: Very fast inference (2-10x faster than others)
- **Quality**: Excellent for educational content

### Setup
1. Visit [console.groq.com](https://console.groq.com)
2. Create a free account
3. Generate an API key
4. Add to `.env`:
```bash
GROQ_API_KEY=your_groq_api_key_here
```

### Best Models for Education
- `llama-3.3-70b-versatile` - Best overall quality
- `llama-3.1-70b-versatile` - Good balance of speed/quality
- `mixtral-8x7b-32768` - Faster, good for simple questions

## 2. Hugging Face Inference API (Recommended Fallback)

### Features
- **Free Tier**: 30,000 characters/month
- **Models**: 100+ open-source models available
- **Variety**: Specialized models for different tasks
- **Community**: Large open-source community

### Setup
1. Visit [huggingface.co](https://huggingface.co)
2. Create a free account
3. Go to Settings → Access Tokens
4. Create a new token with "Read" permissions
5. Add to `.env`:
```bash
HUGGINGFACE_API_KEY=your_huggingface_token_here
```

### Best Models for Education
- `microsoft/DialoGPT-large` - Good conversational AI
- `HuggingFaceH4/zephyr-7b-beta` - Instruction-following model
- `microsoft/DialoGPT-medium` - Faster, smaller model

### Configuration Example
```bash
AI_PRIMARY_PROVIDER=groq
AI_FALLBACK_PROVIDER=huggingface
HUGGINGFACE_API_KEY=hf_your_token_here
HUGGINGFACE_MODEL=microsoft/DialoGPT-large
```

## 3. Google Gemini Flash

### Features
- **Free Tier**: 15 requests/minute, 1M tokens/day
- **Model**: Gemini 1.5 Flash (fast and efficient)
- **Quality**: Excellent for educational content
- **Speed**: Very fast responses

### Setup
1. Visit [ai.google.dev](https://ai.google.dev)
2. Create a free account
3. Generate an API key
4. Add to `.env`:
```bash
GEMINI_API_KEY=your_gemini_api_key_here
```

### Configuration Example
```bash
AI_PRIMARY_PROVIDER=gemini
AI_FALLBACK_PROVIDER=huggingface
GEMINI_API_KEY=your_gemini_key_here
GEMINI_MODEL=gemini-1.5-flash
```

## 4. OpenAI (Limited Free)

### Features
- **Free Tier**: $5 credit for new accounts
- **Models**: GPT-4, GPT-3.5-turbo
- **Quality**: Highest quality output
- **Cost**: Pay-per-use after free credits

### Setup
1. Visit [platform.openai.com](https://platform.openai.com)
2. Create account and verify phone number
3. Generate API key
4. Add to `.env`:
```bash
OPENAI_API_KEY=your_openai_api_key_here
```

## Recommended Configurations

### Configuration 1: Maximum Free Usage
```bash
# Primary: Groq (generous free tier)
AI_PRIMARY_PROVIDER=groq
AI_FALLBACK_PROVIDER=huggingface
AI_ENABLE_FALLBACK=true

GROQ_API_KEY=your_groq_key
HUGGINGFACE_API_KEY=your_hf_token
```

### Configuration 2: Best Quality
```bash
# Primary: Gemini (high quality, good free tier)
AI_PRIMARY_PROVIDER=gemini
AI_FALLBACK_PROVIDER=groq
AI_ENABLE_FALLBACK=true

GEMINI_API_KEY=your_gemini_key
GROQ_API_KEY=your_groq_key
```

### Configuration 3: Development/Testing
```bash
# Primary: Hugging Face (completely free)
AI_PRIMARY_PROVIDER=huggingface
AI_FALLBACK_PROVIDER=mock
AI_ENABLE_FALLBACK=true

HUGGINGFACE_API_KEY=your_hf_token
```

## Getting API Keys

### Groq
1. Go to [console.groq.com](https://console.groq.com)
2. Sign up with email or GitHub
3. Navigate to "API Keys" section
4. Click "Create API Key"
5. Copy the key (starts with `gsk_`)

### Hugging Face
1. Go to [huggingface.co](https://huggingface.co)
2. Sign up for free account
3. Go to Settings → Access Tokens
4. Click "New token"
5. Select "Read" role
6. Copy the token (starts with `hf_`)

### Google Gemini
1. Go to [ai.google.dev](https://ai.google.dev)
2. Sign in with Google account
3. Click "Get API key"
4. Create new project or select existing
5. Generate API key
6. Copy the key

### OpenAI
1. Go to [platform.openai.com](https://platform.openai.com)
2. Sign up and verify phone number
3. Go to API → API Keys
4. Click "Create new secret key"
5. Copy the key (starts with `sk-`)

## Usage Limits and Costs

| Provider | Free Tier | Rate Limits | Best For |
|----------|-----------|-------------|----------|
| Groq | Generous daily limits | ~30 req/min | Primary provider |
| Hugging Face | 30K chars/month | Model dependent | Fallback/Development |
| Gemini | 15 req/min, 1M tokens/day | 15 req/min | High quality questions |
| OpenAI | $5 credit | 3 req/min (free) | Premium quality |

## Troubleshooting

### Common Issues

1. **"Model is loading" error (Hugging Face)**
   - Wait 30-60 seconds and retry
   - Some models need to "warm up"

2. **Rate limit exceeded**
   - Enable fallback providers
   - Reduce request frequency

3. **Invalid API key**
   - Check key is correctly copied
   - Verify key permissions
   - Regenerate if necessary

4. **Empty responses**
   - Check model availability
   - Try different model
   - Verify prompt format

### Testing Your Setup

Use this curl command to test your API keys:

```bash
# Test Groq
curl -X POST "https://api.groq.com/openai/v1/chat/completions" \
  -H "Authorization: Bearer $GROQ_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"role":"user","content":"Hello"}],"model":"llama-3.3-70b-versatile"}'

# Test Hugging Face
curl -X POST "https://api-inference.huggingface.co/models/microsoft/DialoGPT-large" \
  -H "Authorization: Bearer $HUGGINGFACE_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"inputs":"Hello"}'

# Test Gemini
curl -X POST "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=$GEMINI_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"contents":[{"parts":[{"text":"Hello"}]}]}'
```

## Best Practices

1. **Always configure fallback providers** for reliability
2. **Start with Groq + Hugging Face** combination
3. **Monitor usage** to stay within free limits
4. **Use appropriate models** for your use case
5. **Test thoroughly** before production deployment

## Support

For issues with specific providers:
- **Groq**: [docs.groq.com](https://docs.groq.com)
- **Hugging Face**: [huggingface.co/docs](https://huggingface.co/docs)
- **Gemini**: [ai.google.dev/docs](https://ai.google.dev/docs)
- **OpenAI**: [platform.openai.com/docs](https://platform.openai.com/docs)
