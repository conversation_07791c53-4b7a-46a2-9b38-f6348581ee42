# Test-Spark API Documentation

## Overview

The Test-Spark API is a RESTful service that provides endpoints for an adaptive learning platform. It supports user authentication, test generation using AI, analytics, and reporting features.

**Base URL**: `http://localhost:8080/api/v1`

## Authentication

The API uses <PERSON>W<PERSON> (JSON Web Tokens) for authentication. All protected endpoints require a valid JWT token in the Authorization header.

### Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### Token Refresh
Access tokens expire after 15 minutes. Use the refresh token to obtain new access tokens.

## Endpoints

### Authentication

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00Z"
  },
  "access_token": "jwt_token",
  "refresh_token": "refresh_token"
}
```

#### POST /auth/login
Authenticate user and receive tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>"
  },
  "access_token": "jwt_token",
  "refresh_token": "refresh_token"
}
```

#### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refresh_token": "refresh_token"
}
```

**Response:**
```json
{
  "access_token": "new_jwt_token"
}
```

### Content Management

#### GET /content/subjects
Get all available subjects.

**Response:**
```json
[
  {
    "id": 1,
    "name": "Mathematics",
    "description": "Mathematical concepts and problem solving"
  }
]
```

#### GET /content/exams
Get all available exams.

**Response:**
```json
[
  {
    "id": 1,
    "name": "SAT Math",
    "subject_id": 1,
    "difficulty": "intermediate"
  }
]
```

#### GET /content/topics
Get topics for a subject.

**Query Parameters:**
- `subject_id` (required): Subject ID

**Response:**
```json
[
  {
    "id": 1,
    "name": "Algebra",
    "subject_id": 1,
    "difficulty": "beginner"
  }
]
```

### Test Management

#### POST /tests
Create a new test.

**Request Body:**
```json
{
  "subject_id": 1,
  "exam_id": 1,
  "difficulty": "medium",
  "question_count": 10
}
```

**Response:**
```json
{
  "test_id": "uuid",
  "questions": [
    {
      "id": "uuid",
      "question": "What is 2 + 2?",
      "options": ["3", "4", "5", "6"],
      "order": 1
    }
  ]
}
```

#### GET /tests/{testId}/questions/{questionOrder}
Get a specific question from a test.

**Response:**
```json
{
  "id": "uuid",
  "question": "What is 2 + 2?",
  "options": ["3", "4", "5", "6"],
  "order": 1,
  "time_limit": 60
}
```

#### POST /tests/{testId}/questions/{questionOrder}/answer
Submit an answer for a question.

**Request Body:**
```json
{
  "selected_option": 1,
  "time_taken": 30
}
```

**Response:**
```json
{
  "correct": true,
  "explanation": "2 + 2 equals 4",
  "next_question_order": 2
}
```

#### POST /tests/{testId}/complete
Complete a test and get results.

**Response:**
```json
{
  "test_id": "uuid",
  "score": 85.5,
  "total_questions": 10,
  "correct_answers": 8,
  "time_taken": 600,
  "performance_summary": {
    "strong_topics": ["Algebra"],
    "weak_topics": ["Geometry"]
  }
}
```

### Analytics

#### GET /analysis/dashboard
Get user dashboard analytics.

**Response:**
```json
{
  "total_tests_taken": 15,
  "average_score": 78.5,
  "improvement_rate": 12.3,
  "topic_performance": [
    {
      "topic_name": "Algebra",
      "proficiency_score": 85.0,
      "total_attempted": 25,
      "total_correct": 21
    }
  ]
}
```

#### GET /analysis/performance-trends
Get performance trends over time.

**Query Parameters:**
- `time_filter`: "week", "month", "quarter", "year"
- `start_date`: ISO date string (optional)
- `end_date`: ISO date string (optional)

**Response:**
```json
[
  {
    "date": "2024-01-01",
    "score": 75.0,
    "tests_taken": 3
  }
]
```

### Reports

#### POST /reports/generate
Generate a performance report.

**Request Body:**
```json
{
  "report_type": "comprehensive",
  "format": "pdf",
  "date_range": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
  }
}
```

**Response:**
```json
{
  "report_id": "uuid",
  "download_url": "/reports/download/uuid",
  "generated_at": "2024-01-01T00:00:00Z"
}
```

#### GET /reports/download/{reportId}
Download a generated report.

**Response:** Binary file (PDF or HTML)

## Error Responses

All endpoints return standardized error responses:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": "Additional error details"
}
```

### Common HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

## Rate Limiting

API requests are rate-limited to prevent abuse:
- 100 requests per minute per user
- 1000 requests per hour per user

## Data Models

### User
```json
{
  "id": "uuid",
  "email": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Test
```json
{
  "id": "uuid",
  "user_id": "uuid",
  "subject_id": "integer",
  "exam_id": "integer",
  "difficulty": "string",
  "status": "string",
  "created_at": "datetime",
  "completed_at": "datetime"
}
```

### Question
```json
{
  "id": "uuid",
  "test_id": "uuid",
  "question": "string",
  "options": ["string"],
  "correct_option": "integer",
  "explanation": "string",
  "order": "integer"
}
```
