# Authentication Troubleshooting Guide

## 401 Unauthorized Errors

### Common Causes

1. **Expired JWT Token** (Most Common)
   - JWT tokens expire after 15 minutes
   - Solution: Implement automatic token refresh

2. **Missing Authorization Header**
   - Request doesn't include `Authorization: Bearer <token>`
   - Solution: Ensure all protected API calls include the header

3. **Invalid Token Format**
   - Token is malformed or corrupted
   - Solution: Verify token format and regenerate if needed

4. **Wrong Header Format**
   - Header doesn't start with "Bearer "
   - Solution: Use correct format: `Authorization: Bearer <token>`

### Frontend Implementation Example

```typescript
// Token refresh utility
class AuthService {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private tokenExpiry: number | null = null;

  async makeAuthenticatedRequest(url: string, options: RequestInit = {}) {
    // Check if token needs refresh
    if (this.isTokenExpired()) {
      await this.refreshAccessToken();
    }

    // Add authorization header
    const headers = {
      ...options.headers,
      'Authorization': `Bear<PERSON> ${this.accessToken}`,
      'Content-Type': 'application/json',
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    // Handle 401 errors
    if (response.status === 401) {
      // Try to refresh token once
      await this.refreshAccessToken();
      
      // Retry the request with new token
      const retryHeaders = {
        ...options.headers,
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      };

      return fetch(url, {
        ...options,
        headers: retryHeaders,
      });
    }

    return response;
  }

  private isTokenExpired(): boolean {
    if (!this.tokenExpiry) return true;
    return Date.now() >= this.tokenExpiry - 60000; // Refresh 1 minute before expiry
  }

  private async refreshAccessToken(): Promise<void> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await fetch('/api/v1/auth/refresh-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        refresh_token: this.refreshToken,
      }),
    });

    if (!response.ok) {
      // Refresh failed, redirect to login
      this.logout();
      throw new Error('Token refresh failed');
    }

    const data = await response.json();
    this.setTokens(data.access_token, data.refresh_token);
  }

  setTokens(accessToken: string, refreshToken: string): void {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    
    // Decode JWT to get expiry (simplified)
    const payload = JSON.parse(atob(accessToken.split('.')[1]));
    this.tokenExpiry = payload.exp * 1000; // Convert to milliseconds
    
    // Store in localStorage for persistence
    localStorage.setItem('access_token', accessToken);
    localStorage.setItem('refresh_token', refreshToken);
  }

  logout(): void {
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    // Redirect to login page
    window.location.href = '/login';
  }
}

// Usage example for test completion
async function completeTest(testId: string, timeTakenSeconds: number) {
  const authService = new AuthService();
  
  try {
    const response = await authService.makeAuthenticatedRequest(
      `/api/v1/tests/${testId}/complete`,
      {
        method: 'POST',
        body: JSON.stringify({
          time_taken_seconds: timeTakenSeconds,
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to complete test:', error);
    throw error;
  }
}
```

### Testing with cURL

```bash
# 1. Login to get fresh token
TOKEN=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "demo1234"}' \
  http://localhost:8080/api/v1/auth/login | jq -r '.access_token')

# 2. Use token for test completion
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"time_taken_seconds": 120}' \
  http://localhost:8080/api/v1/tests/{testId}/complete
```

### Debugging Steps

1. **Check Token Validity**:
   ```bash
   # Decode JWT payload (requires jq)
   echo "YOUR_TOKEN" | cut -d. -f2 | base64 -d | jq
   ```

2. **Verify Token Expiry**:
   ```javascript
   // In browser console
   const token = "YOUR_TOKEN";
   const payload = JSON.parse(atob(token.split('.')[1]));
   const expiry = new Date(payload.exp * 1000);
   console.log('Token expires at:', expiry);
   console.log('Is expired:', Date.now() > payload.exp * 1000);
   ```

3. **Test Authentication Endpoint**:
   ```bash
   curl -X GET \
     -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8080/api/v1/users/me
   ```

### Error Messages Reference

| Error Message | Cause | Solution |
|---------------|-------|----------|
| "Authorization header is required" | Missing header | Add `Authorization: Bearer <token>` |
| "Authorization header must start with 'Bearer '" | Wrong format | Use correct format |
| "Token is required" | Empty token | Provide valid token |
| "Invalid or expired token" | Bad/expired token | Refresh or re-login |
| "User ID not found in context" | Token validation failed | Check token validity |

### Best Practices

1. **Automatic Token Refresh**: Implement before token expires
2. **Secure Storage**: Store tokens securely (httpOnly cookies preferred)
3. **Error Handling**: Gracefully handle 401 errors with retry logic
4. **Logout on Failure**: Clear tokens and redirect on auth failure
5. **Token Validation**: Check token expiry before making requests

### Production Considerations

1. **HTTPS Only**: Always use HTTPS in production
2. **Secure Headers**: Implement proper security headers
3. **Rate Limiting**: Protect against brute force attacks
4. **Token Rotation**: Implement proper token rotation
5. **Monitoring**: Log authentication failures for security monitoring
