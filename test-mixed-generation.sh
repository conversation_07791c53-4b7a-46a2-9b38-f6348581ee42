#!/bin/bash

# Test script for mixed generation functionality
# This script demonstrates how to configure and test the mixed generation feature

set -e

BASE_URL="http://localhost:8080/api/v1"
AUTH_TOKEN=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Test-Spark Mixed Generation Demo ===${NC}"
echo ""

# Function to make authenticated requests
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3

    if [ -n "$data" ]; then
        curl -s -X "$method" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $AUTH_TOKEN" \
            -d "$data" \
            "$BASE_URL$endpoint"
    else
        curl -s -X "$method" \
            -H "Authorization: Bearer $AUTH_TOKEN" \
            "$BASE_URL$endpoint"
    fi
}

# Function to login and get token
login() {
    echo -e "${YELLOW}Logging in...${NC}"

    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{
            "email": "<EMAIL>",
            "password": "demo1234"
        }' \
        "$BASE_URL/auth/login")

    AUTH_TOKEN=$(echo "$response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

    if [ -n "$AUTH_TOKEN" ]; then
        echo -e "${GREEN}✓ Login successful${NC}"
    else
        echo -e "${RED}✗ Login failed${NC}"
        echo "Response: $response"
        exit 1
    fi
}

# Function to get current mixed generation config
get_config() {
    echo -e "${YELLOW}Getting current mixed generation configuration...${NC}"

    local response=$(make_request "GET" "/tests/mixed-generation/config")
    echo -e "${BLUE}Current configuration:${NC}"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
    echo ""
}

# Function to update mixed generation config
update_config() {
    echo -e "${YELLOW}Updating mixed generation configuration...${NC}"

    local config='{
        "provider_ratios": {
            "gemini": 0.5,
            "cerebras": 0.4,
            "groq": 0.1
        },
        "min_questions_per_provider": 1,
        "enable_randomization": true,
        "enable_fallback": true
    }'

    local response=$(make_request "PUT" "/tests/mixed-generation/config" "$config")
    echo -e "${BLUE}Update response:${NC}"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
    echo ""
}

# Function to create a test with mixed generation
create_test() {
    echo -e "${YELLOW}Creating a test with mixed generation (5 questions)...${NC}"

    local test_request='{
        "subject_id": 1,
        "exam_id": 1,
        "num_questions": 5,
        "difficulty_levels": ["easy", "medium"],
        "grade": "10",
        "board": "CBSE"
    }'

    local response=$(make_request "POST" "/tests" "$test_request")
    echo -e "${BLUE}Test creation response:${NC}"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
    echo ""

    # Extract test ID for further operations
    TEST_ID=$(echo "$response" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    if [ -n "$TEST_ID" ]; then
        echo -e "${GREEN}✓ Test created with ID: $TEST_ID${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed to create test${NC}"
        return 1
    fi
}

# Function to demonstrate different configurations
demo_configurations() {
    echo -e "${BLUE}=== Demonstrating Different Mixed Generation Configurations ===${NC}"
    echo ""

    # Configuration 1: New default with Gemini priority
    echo -e "${YELLOW}Configuration 1: 50% Gemini, 40% Cerebras, 10% Groq${NC}"
    local config1='{
        "provider_ratios": {
            "gemini": 0.5,
            "cerebras": 0.4,
            "groq": 0.1
        },
        "min_questions_per_provider": 1,
        "enable_randomization": true,
        "enable_fallback": true
    }'
    make_request "PUT" "/tests/mixed-generation/config" "$config1" > /dev/null
    create_test
    echo ""

    # Configuration 2: Equal split between Gemini and Cerebras
    echo -e "${YELLOW}Configuration 2: 60% Gemini, 40% Cerebras${NC}"
    local config2='{
        "provider_ratios": {
            "gemini": 0.6,
            "cerebras": 0.4
        },
        "min_questions_per_provider": 2,
        "enable_randomization": true,
        "enable_fallback": true
    }'
    make_request "PUT" "/tests/mixed-generation/config" "$config2" > /dev/null
    create_test
    echo ""

    # Configuration 3: Gemini-heavy configuration
    echo -e "${YELLOW}Configuration 3: 70% Gemini, 20% Cerebras, 10% Groq${NC}"
    local config3='{
        "provider_ratios": {
            "gemini": 0.7,
            "cerebras": 0.2,
            "groq": 0.1
        },
        "min_questions_per_provider": 1,
        "enable_randomization": false,
        "enable_fallback": true
    }'
    make_request "PUT" "/tests/mixed-generation/config" "$config3" > /dev/null
    create_test
    echo ""
}

# Function to check provider availability
check_providers() {
    echo -e "${YELLOW}Checking provider availability...${NC}"

    # This would require a new endpoint to check provider status
    # For now, we'll just show the current configuration
    get_config
}

# Main execution
main() {
    echo -e "${BLUE}Starting mixed generation demonstration...${NC}"
    echo ""

    # Check if jq is available for JSON formatting
    if ! command -v jq &> /dev/null; then
        echo -e "${YELLOW}Note: jq not found. JSON output will not be formatted.${NC}"
        echo ""
    fi

    # Login
    login
    echo ""

    # Get current configuration
    get_config

    # Update configuration
    update_config

    # Verify configuration was updated
    get_config

    # Create a test to demonstrate mixed generation
    create_test

    # Demonstrate different configurations
    demo_configurations

    echo -e "${GREEN}=== Mixed Generation Demo Complete ===${NC}"
    echo ""
    echo -e "${BLUE}Summary:${NC}"
    echo "• Mixed generation allows distributing question generation across multiple AI providers"
    echo "• Default configuration: 50% Gemini, 40% Cerebras, 10% Groq"
    echo "• Configuration can be updated via API endpoints"
    echo "• Fallback mechanisms ensure reliability"
    echo "• Questions are tagged with their generating provider"
    echo ""
    echo -e "${YELLOW}Next steps:${NC}"
    echo "• Check the generated questions to see provider attribution"
    echo "• Monitor performance differences between providers"
    echo "• Adjust ratios based on provider performance and cost"
}

# Run the demo
main
