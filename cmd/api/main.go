// Package main provides the entry point for the Test-Spark backend API server.
// This application serves as the backend for an adaptive learning platform
// that generates personalized tests using AI and provides analytics.
package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"test-spark-backend/internal/api"
	"test-spark-backend/internal/auth"
	"test-spark-backend/internal/config"
	"test-spark-backend/internal/database"
	"test-spark-backend/internal/services"

	"github.com/jackc/pgx/v5/pgxpool"
)

// main is the entry point of the Test-Spark backend application.
// It initializes all services, sets up the HTTP server with graceful shutdown,
// and handles the application lifecycle.
func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize structured logger
	loggerConfig := &services.LoggerConfig{
		Level:       cfg.LogLevel,
		Format:      cfg.LogFormat,
		ServiceName: "test-spark-backend",
		Version:     "1.0.0",
		Output:      "stdout",
	}
	services.InitGlobalLogger(loggerConfig)
	logger := services.GetLogger()

	logger.WithFields(map[string]interface{}{
		"port":       cfg.Port,
		"log_level":  cfg.LogLevel,
		"log_format": cfg.LogFormat,
		"use_redis":  cfg.UseRedis,
	}).Info("Starting Test-Spark Backend Server")

	// Initialize database connection
	dbPool, err := initDatabase(cfg.DatabaseURL)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize database")
	}
	defer dbPool.Close()

	logger.Info("Database connection established successfully")

	// Initialize store
	store := database.NewSQLStore(dbPool)

	// Initialize services
	authService := auth.NewAuthService(cfg)

	// Initialize AI configuration and services
	aiConfig, err := config.LoadAIConfig()
	if err != nil {
		logger.WithError(err).Fatal("Failed to load AI configuration")
	}

	// Create AI service factory and test service with full AI provider support
	aiServiceFactory := services.NewAIServiceFactory(aiConfig, store)
	testService, err := aiServiceFactory.CreateTestService()
	if err != nil {
		logger.WithError(err).Fatal("Failed to create test service with AI providers")
	}

	logger.Info("AI providers initialized successfully")
	analyticsService := services.NewAnalyticsService(store)

	// Initialize caching service based on configuration
	var cachedAnalyticsService services.CachedAnalyticsInterface

	if cfg.UseRedis {
		// Initialize Redis cache service for distributed caching
		redisConfig := &services.RedisConfig{
			Addr:     cfg.RedisAddr,
			Password: cfg.RedisPassword,
			DB:       cfg.RedisDB,
		}

		redisCacheService, err := services.NewRedisCacheService(redisConfig)
		if err != nil {
			logger.WithError(err).Warn("Failed to initialize Redis cache service, falling back to in-memory cache")

			// Fallback to in-memory cache
			cacheService := services.NewCacheService(nil)
			cachedAnalyticsService = services.NewCachedAnalyticsService(store, cacheService)
		} else {
			logger.WithFields(map[string]interface{}{
				"redis_addr": cfg.RedisAddr,
				"redis_db":   cfg.RedisDB,
			}).Info("Redis cache service initialized successfully")
			cachedAnalyticsService = services.NewRedisCachedAnalyticsService(store, redisCacheService)
		}
	} else {
		// Use in-memory cache service
		logger.Info("Using in-memory cache service")
		cacheService := services.NewCacheService(nil)
		cachedAnalyticsService = services.NewCachedAnalyticsService(store, cacheService)
	}

	// Initialize WebSocket service for real-time updates
	websocketService := services.NewWebSocketService()
	defer websocketService.Close()

	// Wrap cached analytics service with real-time capabilities
	realtimeAnalyticsService := services.NewRealtimeAnalyticsService(cachedAnalyticsService, websocketService, store)

	logger.Info("All services initialized successfully")

	// Initialize router with all dependencies
	router := api.NewRouter(store, authService, testService, analyticsService, realtimeAnalyticsService, websocketService)
	handler := router.SetupRoutes()

	// Create HTTP server
	server := &http.Server{
		Addr:         ":" + cfg.Port,
		Handler:      handler,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start server in a goroutine
	go func() {
		logger.WithFields(map[string]interface{}{
			"port":          cfg.Port,
			"health_url":    fmt.Sprintf("http://localhost:%s/health", cfg.Port),
			"api_url":       fmt.Sprintf("http://localhost:%s/api/v1/", cfg.Port),
			"websocket_url": fmt.Sprintf("ws://localhost:%s/api/v1/ws/analytics", cfg.Port),
		}).Info("Server starting")

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to start server")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Attempt graceful shutdown
	if err := server.Shutdown(ctx); err != nil {
		logger.WithError(err).Fatal("Server forced to shutdown")
	}

	logger.Info("Server exited successfully")
}

// initDatabase initializes and configures a PostgreSQL connection pool.
// It sets up connection pool parameters, configures the search path for the testspark_db schema,
// and validates the connection with a ping test.
//
// Parameters:
//   - databaseURL: PostgreSQL connection string
//
// Returns:
//   - *pgxpool.Pool: Configured connection pool
//   - error: Any error that occurred during initialization
func initDatabase(databaseURL string) (*pgxpool.Pool, error) {
	// Parse and validate database URL
	config, err := pgxpool.ParseConfig(databaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database URL: %w", err)
	}

	// Configure connection pool settings
	config.MaxConns = 25
	config.MinConns = 5
	config.MaxConnLifetime = time.Hour
	config.MaxConnIdleTime = time.Minute * 30

	// Configure search path for all connections in the pool
	if config.ConnConfig.RuntimeParams == nil {
		config.ConnConfig.RuntimeParams = make(map[string]string)
	}
	config.ConnConfig.RuntimeParams["search_path"] = "testspark_db,public"

	// Create connection pool
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	pool, err := pgxpool.NewWithConfig(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	// Test the connection
	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return pool, nil
}
