package types

// ProviderCapabilities describes what a provider can do
type ProviderCapabilities struct {
	MaxQuestionsPerRequest int      `json:"max_questions_per_request"`
	SupportedDifficulties  []string `json:"supported_difficulties"`
	SupportsGradeLevel     bool     `json:"supports_grade_level"`
	SupportsBoard          bool     `json:"supports_board"`
	MaxTokens              int      `json:"max_tokens"`
	SupportsStreaming      bool     `json:"supports_streaming"`
}

// AIProviderConfig holds configuration for AI providers
type AIProviderConfig struct {
	// Primary provider configuration
	PrimaryProvider  string `json:"primary_provider"`
	FallbackProvider string `json:"fallback_provider"`

	// Provider-specific configurations
	GroqConfig        *GroqProviderConfig        `json:"groq_config,omitempty"`
	OpenAIConfig      *OpenAIProviderConfig      `json:"openai_config,omitempty"`
	HuggingFaceConfig *HuggingFaceProviderConfig `json:"huggingface_config,omitempty"`
	GeminiConfig      *GeminiProviderConfig      `json:"gemini_config,omitempty"`
	CerebrasConfig    *CerebrasProviderConfig    `json:"cerebras_config,omitempty"`

	// Global settings
	EnableFallback bool `json:"enable_fallback"`
	TimeoutSeconds int  `json:"timeout_seconds"`
	MaxRetries     int  `json:"max_retries"`
}

// GroqProviderConfig holds Groq-specific configuration
type GroqProviderConfig struct {
	APIKey      string  `json:"api_key"`
	Model       string  `json:"model"`
	Temperature float64 `json:"temperature"`
	MaxTokens   int     `json:"max_tokens"`
	BaseURL     string  `json:"base_url"`
}

// OpenAIProviderConfig holds OpenAI-specific configuration
type OpenAIProviderConfig struct {
	APIKey      string  `json:"api_key"`
	Model       string  `json:"model"`
	Temperature float64 `json:"temperature"`
	MaxTokens   int     `json:"max_tokens"`
	BaseURL     string  `json:"base_url"`
	OrgID       string  `json:"org_id,omitempty"`
}

// HuggingFaceProviderConfig holds Hugging Face-specific configuration
type HuggingFaceProviderConfig struct {
	APIKey         string  `json:"api_key"`
	Model          string  `json:"model"`
	Temperature    float64 `json:"temperature"`
	MaxTokens      int     `json:"max_tokens"`
	BaseURL        string  `json:"base_url"`
	TimeoutSeconds int     `json:"timeout_seconds"`
}

// GeminiProviderConfig holds Google Gemini-specific configuration
type GeminiProviderConfig struct {
	APIKey         string  `json:"api_key"`
	Model          string  `json:"model"`
	Temperature    float64 `json:"temperature"`
	MaxTokens      int     `json:"max_tokens"`
	BaseURL        string  `json:"base_url"`
	TimeoutSeconds int     `json:"timeout_seconds"`
}

// CerebrasProviderConfig holds Cerebras-specific configuration
type CerebrasProviderConfig struct {
	APIKey         string  `json:"api_key"`
	Model          string  `json:"model"`
	Temperature    float64 `json:"temperature"`
	MaxTokens      int     `json:"max_tokens"`
	BaseURL        string  `json:"base_url"`
	TimeoutSeconds int     `json:"timeout_seconds"`
}

// ProviderType represents the type of AI provider
type ProviderType string

const (
	ProviderTypeGroq        ProviderType = "groq"
	ProviderTypeOpenAI      ProviderType = "openai"
	ProviderTypeHuggingFace ProviderType = "huggingface"
	ProviderTypeGemini      ProviderType = "gemini"
	ProviderTypeCerebras    ProviderType = "cerebras"
	ProviderTypeMock        ProviderType = "mock"
)

// DefaultAIProviderConfig returns a default configuration
func DefaultAIProviderConfig() *AIProviderConfig {
	return &AIProviderConfig{
		PrimaryProvider:  string(ProviderTypeGemini),
		FallbackProvider: string(ProviderTypeCerebras),
		EnableFallback:   true,
		TimeoutSeconds:   60,
		MaxRetries:       3,
		GeminiConfig: &GeminiProviderConfig{
			Model:       "gemini-1.5-flash",
			Temperature: 0.7,
			MaxTokens:   2000,
			BaseURL:     "https://generativelanguage.googleapis.com/v1beta/models",
		},
		CerebrasConfig: &CerebrasProviderConfig{
			Model:       "qwen-3-235b-a22b",
			Temperature: 0.7,
			MaxTokens:   4000,
			BaseURL:     "https://api.cerebras.ai/v1/chat/completions",
		},
	}
}

// AIProviderError represents an error from an AI provider
type AIProviderError struct {
	Message  string `json:"message"`
	Provider string `json:"provider"`
	Code     string `json:"code"`
}

func (e *AIProviderError) Error() string {
	return e.Message
}

// ValidateConfig validates the AI provider configuration
func ValidateConfig(config *AIProviderConfig) error {
	if config.PrimaryProvider == "" {
		return &AIProviderError{
			Message:  "primary provider must be specified",
			Provider: "config",
			Code:     "INVALID_CONFIG",
		}
	}

	if config.TimeoutSeconds <= 0 {
		return &AIProviderError{
			Message:  "timeout seconds must be positive",
			Provider: "config",
			Code:     "INVALID_CONFIG",
		}
	}

	if config.MaxRetries < 0 {
		return &AIProviderError{
			Message:  "max retries cannot be negative",
			Provider: "config",
			Code:     "INVALID_CONFIG",
		}
	}

	return nil
}
