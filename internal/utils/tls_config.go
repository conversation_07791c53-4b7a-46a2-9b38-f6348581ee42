package utils

import (
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"net/http"
	"os"
	"time"
)

// TLSConfigOptions holds options for TLS configuration
type TLSConfigOptions struct {
	ServerName         string
	InsecureSkipVerify bool
	CertFile           string
	CertDir            string
	MinVersion         uint16
	MaxVersion         uint16
}

// CreateSecureHTTPClient creates an HTTP client with proper TLS configuration
func CreateSecureHTTPClient(options *TLSConfigOptions) *http.Client {
	if options == nil {
		options = &TLSConfigOptions{}
	}

	// Set defaults
	if options.MinVersion == 0 {
		options.MinVersion = tls.VersionTLS12
	}
	if options.MaxVersion == 0 {
		options.MaxVersion = tls.VersionTLS13
	}

	// Create TLS config
	tlsConfig := &tls.Config{
		MinVersion: options.MinVersion,
		MaxVersion: options.MaxVersion,
		ServerName: options.ServerName,
	}

	// Handle certificate configuration
	if err := configureCertificates(tlsConfig, options); err != nil {
		// Failed to configure certificates - fall back to system defaults
		tlsConfig.RootCAs = nil
	}

	// Check environment variables for Docker/container environments
	if shouldSkipVerification(options) {
		tlsConfig.InsecureSkipVerify = true
		// TLS certificate verification disabled for containerized environment
	}

	// Create transport with optimized settings
	transport := &http.Transport{
		TLSClientConfig:       tlsConfig,
		DisableKeepAlives:     false,
		DisableCompression:    false,
		MaxIdleConns:          10,
		MaxIdleConnsPerHost:   2,
		MaxConnsPerHost:       10,
		IdleConnTimeout:       90 * time.Second,
		ResponseHeaderTimeout: 60 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
	}

	return &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second,
	}
}

// configureCertificates configures the certificate pool for TLS
func configureCertificates(tlsConfig *tls.Config, options *TLSConfigOptions) error {
	// Try to load system certificates first
	if rootCAs, err := x509.SystemCertPool(); err == nil {
		tlsConfig.RootCAs = rootCAs
		// Using system certificate pool
	} else {
		// Failed to load system cert pool, creating new one
		tlsConfig.RootCAs = x509.NewCertPool()
	}

	// Try to load additional certificates from file
	if options.CertFile != "" {
		if err := loadCertificateFile(tlsConfig.RootCAs, options.CertFile); err != nil {
			// Warning: Failed to load cert file
		}
	}

	// Try to load certificates from directory
	if options.CertDir != "" {
		if err := loadCertificateDirectory(tlsConfig.RootCAs, options.CertDir); err != nil {
			// Warning: Failed to load certs from directory
		}
	}

	// Try common certificate locations
	commonCertPaths := []string{
		"/etc/ssl/certs/ca-certificates.crt",
		"/etc/ssl/certs/ca-bundle.crt",
		"/etc/pki/tls/certs/ca-bundle.crt",
		"/usr/share/ca-certificates/",
		"/etc/ssl/certs/",
	}

	for _, path := range commonCertPaths {
		if _, err := os.Stat(path); err == nil {
			if err := loadCertificateFile(tlsConfig.RootCAs, path); err == nil {
				// Loaded certificates from common path
				break
			}
		}
	}

	return nil
}

// loadCertificateFile loads certificates from a file
func loadCertificateFile(certPool *x509.CertPool, filename string) error {
	certPEM, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read certificate file: %w", err)
	}

	if !certPool.AppendCertsFromPEM(certPEM) {
		return fmt.Errorf("failed to parse certificate file")
	}

	return nil
}

// loadCertificateDirectory loads certificates from a directory
func loadCertificateDirectory(certPool *x509.CertPool, dirname string) error {
	files, err := os.ReadDir(dirname)
	if err != nil {
		return fmt.Errorf("failed to read certificate directory: %w", err)
	}

	loaded := 0
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		filename := dirname + "/" + file.Name()
		if err := loadCertificateFile(certPool, filename); err == nil {
			loaded++
		}
	}

	if loaded == 0 {
		return fmt.Errorf("no certificates loaded from directory")
	}

	// Loaded certificates from directory
	return nil
}

// shouldSkipVerification determines if TLS verification should be skipped
func shouldSkipVerification(options *TLSConfigOptions) bool {
	// Explicit option
	if options.InsecureSkipVerify {
		return true
	}

	// Environment variables
	if os.Getenv("SSL_VERIFY") == "false" {
		return true
	}

	if os.Getenv("DOCKER_ENV") == "true" {
		return true
	}

	if os.Getenv("INSECURE_SKIP_VERIFY") == "true" {
		return true
	}

	return false
}

// CreateGroqHTTPClient creates an HTTP client specifically configured for Groq API
func CreateGroqHTTPClient() *http.Client {
	options := &TLSConfigOptions{
		ServerName: "api.groq.com",
		CertDir:    os.Getenv("SSL_CERT_DIR"),
		CertFile:   os.Getenv("SSL_CERT_FILE"),
	}

	return CreateSecureHTTPClient(options)
}
