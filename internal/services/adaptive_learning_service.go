package services

import (
	"context"
	"fmt"
	"math"
	"time"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"

	"github.com/google/uuid"
)

// AdaptiveLearningService implements advanced adaptive learning algorithms
type AdaptiveLearningService struct {
	store database.Store
}

// NewAdaptiveLearningService creates a new adaptive learning service
func NewAdaptiveLearningService(store database.Store) *AdaptiveLearningService {
	return &AdaptiveLearningService{
		store: store,
	}
}

// EstimateAbilityIRT estimates student ability using Item Response Theory
func (als *AdaptiveLearningService) EstimateAbilityIRT(ctx context.Context, userID uuid.UUID, responses []models.AdaptiveResponse) (float64, float64, error) {
	if len(responses) == 0 {
		return 0.0, 1.0, nil // Default ability with high uncertainty
	}

	// Newton-Rap<PERSON>on method for maximum likelihood estimation
	theta := 0.0 // Initial ability estimate
	tolerance := 0.001
	maxIterations := 50
	var finalSecondDerivative float64

	for iteration := 0; iteration < maxIterations; iteration++ {
		likelihood := 0.0
		derivative := 0.0
		secondDerivative := 0.0

		for _, response := range responses {
			a := response.IRTParameters.Discrimination
			b := response.IRTParameters.Difficulty
			c := response.IRTParameters.Guessing

			// Calculate probability of correct response (3PL model)
			prob := c + (1-c)/(1+math.Exp(-a*(theta-b)))

			// Likelihood contribution
			if response.IsCorrect {
				likelihood += math.Log(prob)
				derivative += a * (1 - prob) / prob
				secondDerivative -= a * a * (1 - prob) / (prob * prob)
			} else {
				likelihood += math.Log(1 - prob)
				derivative -= a * prob / (1 - prob)
				secondDerivative -= a * a * prob / ((1 - prob) * (1 - prob))
			}
		}

		finalSecondDerivative = secondDerivative

		// Newton-Raphson update
		if math.Abs(secondDerivative) < 1e-10 {
			break
		}

		newTheta := theta - derivative/secondDerivative

		if math.Abs(newTheta-theta) < tolerance {
			theta = newTheta
			break
		}

		theta = newTheta
	}

	// Calculate standard error (confidence)
	standardError := 1.0 / math.Sqrt(-finalSecondDerivative)

	return theta, standardError, nil
}

// CalculateMasteryLevel determines mastery level for a topic
func (als *AdaptiveLearningService) CalculateMasteryLevel(ctx context.Context, userID uuid.UUID, topicID int) (*models.MasteryLevel, error) {
	// Get recent responses for this topic
	responses, err := als.store.GetUserResponsesByTopic(ctx, userID, topicID, 30) // Last 30 days
	if err != nil {
		return nil, fmt.Errorf("failed to get user responses: %w", err)
	}

	if len(responses) == 0 {
		return &models.MasteryLevel{
			TopicID:         topicID,
			MasteryScore:    0.0,
			ConfidenceLevel: 0.0,
			LastPracticed:   time.Time{},
			PracticeCount:   0,
			NeedsReview:     true,
			NextReviewDate:  time.Now(),
		}, nil
	}

	// Calculate mastery metrics
	totalQuestions := len(responses)
	correctAnswers := 0
	consecutiveCorrect := 0
	maxConsecutive := 0
	currentConsecutive := 0

	var totalResponseTime float64
	var lastPracticed time.Time

	for i, response := range responses {
		if response.IsCorrect {
			correctAnswers++
			currentConsecutive++
			if currentConsecutive > maxConsecutive {
				maxConsecutive = currentConsecutive
			}
		} else {
			currentConsecutive = 0
		}

		totalResponseTime += float64(response.ResponseTime)

		if i == len(responses)-1 {
			consecutiveCorrect = currentConsecutive
			lastPracticed = response.Timestamp
		}
	}

	// Calculate mastery score using multiple factors
	accuracyScore := float64(correctAnswers) / float64(totalQuestions)
	consistencyScore := float64(maxConsecutive) / float64(totalQuestions)
	recentPerformanceScore := float64(consecutiveCorrect) / math.Min(float64(totalQuestions), 5.0)

	// Weighted mastery calculation
	masteryScore := (accuracyScore*0.4 + consistencyScore*0.3 + recentPerformanceScore*0.3)

	// Calculate confidence based on sample size and consistency
	confidence := math.Min(1.0, float64(totalQuestions)/20.0) * (1.0 - math.Abs(accuracyScore-consistencyScore))

	// Determine if review is needed (spaced repetition)
	daysSinceLastPractice := time.Since(lastPracticed).Hours() / 24
	needsReview := als.calculateSpacedRepetitionNeed(masteryScore, daysSinceLastPractice, totalQuestions)

	// Calculate next review date
	nextReviewDate := als.calculateNextReviewDate(masteryScore, lastPracticed, totalQuestions)

	// Determine optimal difficulty range
	difficultyRange := als.calculateOptimalDifficultyRange(masteryScore, accuracyScore)

	return &models.MasteryLevel{
		TopicID:            topicID,
		MasteryScore:       masteryScore,
		ConfidenceLevel:    confidence,
		LastPracticed:      lastPracticed,
		PracticeCount:      totalQuestions,
		ConsecutiveCorrect: consecutiveCorrect,
		NeedsReview:        needsReview,
		NextReviewDate:     nextReviewDate,
		DifficultyRange:    difficultyRange,
	}, nil
}

// GenerateAdaptiveLearningPath creates a personalized learning path
func (als *AdaptiveLearningService) GenerateAdaptiveLearningPath(ctx context.Context, userID uuid.UUID, subjectID int, targetMastery float64) (*models.LearningPath, error) {
	// Get user's current mastery levels for all topics in subject
	topics, err := als.store.GetTopicsBySubject(ctx, subjectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get topics: %w", err)
	}

	var steps []models.LearningStep
	stepNumber := 1

	// Analyze current state and create adaptive path
	for _, topic := range topics {
		mastery, err := als.CalculateMasteryLevel(ctx, userID, topic.ID)
		if err != nil {
			continue // Skip topics with errors
		}

		// Only include topics that need improvement
		if mastery.MasteryScore < targetMastery {
			step := als.createLearningStep(stepNumber, topic, mastery, targetMastery)
			steps = append(steps, step)
			stepNumber++
		}
	}

	// Sort steps by priority (weakest topics first, considering prerequisites)
	steps = als.optimizeStepOrder(steps, topics)

	// Calculate total estimated time
	totalTime := 0
	for _, step := range steps {
		totalTime += step.EstimatedTime
	}

	learningPath := &models.LearningPath{
		ID:             uuid.New(),
		UserID:         userID,
		SubjectID:      subjectID,
		PathName:       fmt.Sprintf("Adaptive Learning Path - Subject %d", subjectID),
		CurrentStep:    0,
		TotalSteps:     len(steps),
		EstimatedTime:  totalTime,
		CompletionRate: 0.0,
		Steps:          steps,
		AdaptiveFlags: models.AdaptiveFlags{
			EnableRealTimeAdaptation: true,
			EnableSpacedRepetition:   true,
			EnableMasteryProgression: true,
			EnableDifficultyScaling:  true,
			EnablePersonalization:    true,
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return learningPath, nil
}

// AdaptDifficultyRealTime adjusts question difficulty based on recent performance
func (als *AdaptiveLearningService) AdaptDifficultyRealTime(ctx context.Context, userID uuid.UUID, topicID int, recentResponses []models.AdaptiveResponse) (float64, error) {
	if len(recentResponses) == 0 {
		return 0.0, nil // Default difficulty
	}

	// Calculate recent performance metrics
	recentCorrect := 0
	totalResponseTime := 0
	for _, response := range recentResponses {
		if response.IsCorrect {
			recentCorrect++
		}
		totalResponseTime += response.ResponseTime
	}

	accuracy := float64(recentCorrect) / float64(len(recentResponses))
	avgResponseTime := float64(totalResponseTime) / float64(len(recentResponses))

	// Get current mastery level
	mastery, err := als.CalculateMasteryLevel(ctx, userID, topicID)
	if err != nil {
		return 0.0, err
	}

	// Adaptive difficulty calculation
	targetAccuracy := 0.7 // Sweet spot for learning
	difficultyAdjustment := 0.0

	if accuracy > 0.8 && avgResponseTime < 30 { // Too easy
		difficultyAdjustment = 0.3
	} else if accuracy > targetAccuracy { // Slightly too easy
		difficultyAdjustment = 0.1
	} else if accuracy < 0.4 { // Too hard
		difficultyAdjustment = -0.3
	} else if accuracy < targetAccuracy { // Slightly too hard
		difficultyAdjustment = -0.1
	}

	// Base difficulty on mastery level
	baseDifficulty := mastery.DifficultyRange.OptimalDifficulty
	newDifficulty := baseDifficulty + difficultyAdjustment

	// Clamp to reasonable bounds
	newDifficulty = math.Max(-2.0, math.Min(2.0, newDifficulty))

	return newDifficulty, nil
}

// Helper functions

func (als *AdaptiveLearningService) calculateSpacedRepetitionNeed(masteryScore, daysSinceLastPractice float64, practiceCount int) bool {
	// SM-2 algorithm inspired calculation
	interval := math.Pow(2.5, float64(practiceCount)/5.0) * masteryScore
	return daysSinceLastPractice > interval
}

func (als *AdaptiveLearningService) calculateNextReviewDate(masteryScore float64, lastPracticed time.Time, practiceCount int) time.Time {
	// Calculate optimal review interval
	baseInterval := 1.0                                             // days
	masteryMultiplier := 1.0 + masteryScore*2.0                     // Higher mastery = longer intervals
	practiceMultiplier := math.Pow(1.3, float64(practiceCount)/5.0) // More practice = longer intervals

	interval := baseInterval * masteryMultiplier * practiceMultiplier
	return lastPracticed.Add(time.Duration(interval*24) * time.Hour)
}

func (als *AdaptiveLearningService) calculateOptimalDifficultyRange(masteryScore, accuracyScore float64) models.DifficultyRange {
	// Calculate difficulty range based on current ability
	optimalDifficulty := (masteryScore - 0.5) * 2.0 // Convert 0-1 to -1 to 1 scale

	return models.DifficultyRange{
		MinDifficulty:     optimalDifficulty - 0.5,
		MaxDifficulty:     optimalDifficulty + 0.5,
		OptimalDifficulty: optimalDifficulty,
	}
}

func (als *AdaptiveLearningService) createLearningStep(stepNumber int, topic models.Topic, mastery *models.MasteryLevel, targetMastery float64) models.LearningStep {
	// Determine step type based on current mastery
	var stepType models.LearningStepType
	if mastery.MasteryScore < 0.3 {
		stepType = models.StepTypeIntroduction
	} else if mastery.MasteryScore < 0.6 {
		stepType = models.StepTypePractice
	} else {
		stepType = models.StepTypeReview
	}

	// Estimate time based on mastery gap
	masteryGap := targetMastery - mastery.MasteryScore
	estimatedTime := int(masteryGap * 60) // 60 minutes per mastery point

	return models.LearningStep{
		StepNumber:      stepNumber,
		StepType:        stepType,
		TopicID:         topic.ID,
		TargetMastery:   targetMastery,
		EstimatedTime:   estimatedTime,
		DifficultyLevel: mastery.DifficultyRange.OptimalDifficulty,
		IsCompleted:     false,
	}
}

func (als *AdaptiveLearningService) optimizeStepOrder(steps []models.LearningStep, topics []models.Topic) []models.LearningStep {
	// Simple optimization: sort by mastery score (lowest first)
	// In a more advanced implementation, this would consider prerequisites and dependencies
	return steps // For now, return as-is
}
