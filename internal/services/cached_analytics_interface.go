// Package services provides interfaces for cached analytics functionality.
// This file defines the common interface that both in-memory and Redis-based
// cached analytics services implement, allowing for flexible caching strategies.
package services

import (
	"context"
	"test-spark-backend/internal/models"
)

// CachedAnalyticsInterface defines the common interface for cached analytics services
type CachedAnalyticsInterface interface {
	// Dashboard operations
	GetDashboardSummary(ctx context.Context, userID string) (*models.DashboardSummary, error)

	// Performance trends operations
	GetPerformanceTrends(ctx context.Context, userID string, timeFilter models.TimeFilter) ([]models.PerformanceTrend, error)
	GetPerformanceTrendsWithDateRange(ctx context.Context, userID string, timeFilter models.TimeFilter, dateRange *models.DateRange) ([]models.PerformanceTrend, error)

	// Topic performance operations
	GetUserTopicPerformances(ctx context.Context, userID string) ([]models.TopicPerformanceWithName, error)
	GetTopicPerformance(ctx context.Context, userID string, topicID int) (*models.TopicPerformanceSummary, error)

	// Heatmap data operations
	GetHeatmapData(ctx context.Context, userID string) ([]models.HeatmapData, error)

	// Study patterns operations
	GetStudyPatterns(ctx context.Context, userID string) (*models.StudyPattern, error)

	// Subject comparisons operations
	GetSubjectComparisons(ctx context.Context, userID string) ([]models.SubjectComparison, error)

	// Test history operations
	GetUserTestHistory(ctx context.Context, userID string, limit int) ([]models.UserTestHistory, error)
	GetTimeFilteredTestHistory(ctx context.Context, userID string, timeFilter models.TimeFilter) ([]models.UserTestHistory, error)
	GetTimeFilteredTestHistoryWithDateRange(ctx context.Context, userID string, timeFilter models.TimeFilter, dateRange *models.DateRange) ([]models.UserTestHistory, error)

	// Insights operations
	GetUserInsights(ctx context.Context, userID string, limit int) ([]models.AnalyticsInsight, error)
	CreateInsight(ctx context.Context, insight *models.AnalyticsInsight) error

	// Weakest topics operations
	GetWeakestTopics(ctx context.Context, userID string, limit int) ([]models.TopicPerformanceWithName, error)

	// Topic operations
	GetTopicByID(ctx context.Context, topicID int) (*models.Topic, error)

	// Test result operations
	CreateTestResult(ctx context.Context, result *models.TestResult) error

	// Cache management operations
	InvalidateUserCache(userID string)
	GetCacheStats() map[string]interface{}
	GetCacheHealth() map[string]interface{}
}
