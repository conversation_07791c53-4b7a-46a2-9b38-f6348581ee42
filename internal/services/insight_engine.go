package services

import (
	"context"
	"fmt"
	"math"
	"sort"
	"time"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
)

// InsightEngine provides AI-powered insight generation
type InsightEngine struct {
	store database.Store
}

// NewInsightEngine creates a new insight engine
func NewInsightEngine(store database.Store) *InsightEngine {
	return &InsightEngine{
		store: store,
	}
}

// InsightAnalysis represents comprehensive analysis data for insight generation
type InsightAnalysis struct {
	UserID             string
	RecentTests        []models.UserTestHistory
	TopicPerformances  []models.TopicPerformanceWithName
	StudyPatterns      *models.StudyPattern
	PerformanceTrends  []models.PerformanceTrend
	SubjectComparisons []models.SubjectComparison
	WeakestTopics      []models.TopicPerformanceWithName
	StrongestTopics    []models.TopicPerformanceWithName
	AnalysisTimestamp  time.Time
}

// GenerateInsights creates comprehensive AI-powered insights
func (ie *InsightEngine) GenerateInsights(ctx context.Context, userID string) ([]models.AnalyticsInsight, error) {
	// Gather comprehensive analysis data
	analysis, err := ie.gatherAnalysisData(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to gather analysis data: %w", err)
	}

	var insights []models.AnalyticsInsight

	// Generate different types of insights
	performanceInsights := ie.generatePerformanceInsights(analysis)
	insights = append(insights, performanceInsights...)

	trendInsights := ie.generateTrendInsights(analysis)
	insights = append(insights, trendInsights...)

	consistencyInsights := ie.generateConsistencyInsights(analysis)
	insights = append(insights, consistencyInsights...)

	difficultyInsights := ie.generateDifficultyInsights(analysis)
	insights = append(insights, difficultyInsights...)

	achievementInsights := ie.generateAchievementInsights(analysis)
	insights = append(insights, achievementInsights...)

	// Sort insights by priority and limit to top 10
	sort.Slice(insights, func(i, j int) bool {
		return insights[i].Priority > insights[j].Priority
	})

	if len(insights) > 10 {
		insights = insights[:10]
	}

	return insights, nil
}

// gatherAnalysisData collects all necessary data for insight generation
func (ie *InsightEngine) gatherAnalysisData(ctx context.Context, userID string) (*InsightAnalysis, error) {
	analysis := &InsightAnalysis{
		UserID:            userID,
		AnalysisTimestamp: time.Now(),
	}

	// Get recent test history
	recentTests, err := ie.store.GetUserTestHistory(ctx, userID, 20)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent tests: %w", err)
	}
	analysis.RecentTests = recentTests

	// Get topic performances
	topicPerformances, err := ie.store.GetUserTopicPerformances(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get topic performances: %w", err)
	}
	analysis.TopicPerformances = topicPerformances

	// Get study patterns
	studyPatterns, err := ie.store.GetStudyPatterns(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get study patterns: %w", err)
	}
	analysis.StudyPatterns = studyPatterns

	// Get performance trends
	trends, err := ie.store.GetPerformanceTrends(ctx, userID, models.TimeFilterMonth)
	if err != nil {
		return nil, fmt.Errorf("failed to get performance trends: %w", err)
	}
	analysis.PerformanceTrends = trends

	// Get subject comparisons
	subjectComparisons, err := ie.store.GetSubjectComparisons(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get subject comparisons: %w", err)
	}
	analysis.SubjectComparisons = subjectComparisons

	// Identify weakest and strongest topics
	analysis.WeakestTopics = ie.identifyWeakestTopics(topicPerformances, 5)
	analysis.StrongestTopics = ie.identifyStrongestTopics(topicPerformances, 5)

	return analysis, nil
}

// generatePerformanceInsights analyzes performance patterns
func (ie *InsightEngine) generatePerformanceInsights(analysis *InsightAnalysis) []models.AnalyticsInsight {
	var insights []models.AnalyticsInsight

	// Analyze weak topics
	for i, topic := range analysis.WeakestTopics {
		if i >= 3 { // Limit to top 3 weakest topics
			break
		}

		priority := 5 - i // Higher priority for weaker topics
		if topic.ProficiencyScore < 40 {
			priority = 5 // Critical priority for very weak topics
		}

		insight := models.AnalyticsInsight{
			Type:        "concern",
			Title:       fmt.Sprintf("%s requires immediate attention", topic.TopicName),
			Description: fmt.Sprintf("Your proficiency in %s is only %.1f%%. This topic needs focused practice to improve your overall performance.", topic.TopicName, topic.ProficiencyScore),
			Action:      "Start focused practice session",
			Priority:    priority,
			CreatedAt:   analysis.AnalysisTimestamp,
			TopicID:     &topic.TopicID,
			SubjectName: &topic.SubjectName,
		}
		insights = append(insights, insight)
	}

	// Analyze strong topics for reinforcement
	if len(analysis.StrongestTopics) > 0 {
		strongestTopic := analysis.StrongestTopics[0]
		if strongestTopic.ProficiencyScore > 85 {
			insight := models.AnalyticsInsight{
				Type:        "achievement",
				Title:       fmt.Sprintf("Excellent mastery of %s!", strongestTopic.TopicName),
				Description: fmt.Sprintf("You've achieved %.1f%% proficiency in %s. Consider helping others or exploring advanced concepts.", strongestTopic.ProficiencyScore, strongestTopic.TopicName),
				Action:      "Explore advanced topics",
				Priority:    2,
				CreatedAt:   analysis.AnalysisTimestamp,
				TopicID:     &strongestTopic.TopicID,
				SubjectName: &strongestTopic.SubjectName,
			}
			insights = append(insights, insight)
		}
	}

	return insights
}

// generateTrendInsights analyzes performance trends over time
func (ie *InsightEngine) generateTrendInsights(analysis *InsightAnalysis) []models.AnalyticsInsight {
	var insights []models.AnalyticsInsight

	if len(analysis.RecentTests) < 3 {
		return insights // Need at least 3 tests for trend analysis
	}

	// Calculate recent performance trend
	recentScores := make([]float64, 0, len(analysis.RecentTests))
	for _, test := range analysis.RecentTests {
		if test.Score != nil {
			recentScores = append(recentScores, *test.Score)
		}
	}

	if len(recentScores) >= 3 {
		trend := ie.calculateTrend(recentScores)

		if trend > 5 { // Improving trend
			insight := models.AnalyticsInsight{
				Type:        "improvement",
				Title:       "Excellent upward trend!",
				Description: fmt.Sprintf("Your performance has improved by %.1f%% over recent tests. Your study strategy is working well!", trend),
				Action:      "Continue current approach",
				Priority:    4,
				CreatedAt:   analysis.AnalysisTimestamp,
			}
			insights = append(insights, insight)
		} else if trend < -5 { // Declining trend
			insight := models.AnalyticsInsight{
				Type:        "concern",
				Title:       "Performance decline detected",
				Description: fmt.Sprintf("Your scores have decreased by %.1f%% recently. Consider reviewing your study approach or taking a break.", math.Abs(trend)),
				Action:      "Review study strategy",
				Priority:    4,
				CreatedAt:   analysis.AnalysisTimestamp,
			}
			insights = append(insights, insight)
		}
	}

	return insights
}

// generateConsistencyInsights analyzes study consistency patterns
func (ie *InsightEngine) generateConsistencyInsights(analysis *InsightAnalysis) []models.AnalyticsInsight {
	var insights []models.AnalyticsInsight

	if analysis.StudyPatterns == nil {
		return insights
	}

	patterns := analysis.StudyPatterns

	// Analyze consistency score
	if patterns.ConsistencyScore >= 80 {
		insight := models.AnalyticsInsight{
			Type:        "achievement",
			Title:       "Outstanding consistency!",
			Description: fmt.Sprintf("Your consistency score of %.1f%% shows excellent study habits. Keep up this disciplined approach!", patterns.ConsistencyScore),
			Action:      "Maintain current schedule",
			Priority:    3,
			CreatedAt:   analysis.AnalysisTimestamp,
		}
		insights = append(insights, insight)
	} else if patterns.ConsistencyScore < 40 {
		insight := models.AnalyticsInsight{
			Type:        "recommendation",
			Title:       "Improve study consistency",
			Description: fmt.Sprintf("Your consistency score is %.1f%%. Regular practice leads to better retention and performance.", patterns.ConsistencyScore),
			Action:      "Set daily study reminders",
			Priority:    4,
			CreatedAt:   analysis.AnalysisTimestamp,
		}
		insights = append(insights, insight)
	}

	// Analyze streak performance
	if patterns.CurrentStreak >= 14 {
		insight := models.AnalyticsInsight{
			Type:        "achievement",
			Title:       fmt.Sprintf("Amazing %d-day streak!", patterns.CurrentStreak),
			Description: "Your dedication is paying off! Long study streaks significantly improve learning outcomes.",
			Action:      "Celebrate your progress",
			Priority:    3,
			CreatedAt:   analysis.AnalysisTimestamp,
		}
		insights = append(insights, insight)
	} else if patterns.CurrentStreak == 0 && patterns.LongestStreak > 7 {
		insight := models.AnalyticsInsight{
			Type:        "recommendation",
			Title:       "Restart your study streak",
			Description: fmt.Sprintf("You previously achieved a %d-day streak. Getting back into a routine will boost your performance.", patterns.LongestStreak),
			Action:      "Start new practice session",
			Priority:    3,
			CreatedAt:   analysis.AnalysisTimestamp,
		}
		insights = append(insights, insight)
	}

	return insights
}

// generateDifficultyInsights analyzes difficulty preference patterns
func (ie *InsightEngine) generateDifficultyInsights(analysis *InsightAnalysis) []models.AnalyticsInsight {
	var insights []models.AnalyticsInsight

	if analysis.StudyPatterns == nil {
		return insights
	}

	// Analyze session length optimization
	avgSession := analysis.StudyPatterns.AverageSessionTime
	if avgSession < 15 {
		insight := models.AnalyticsInsight{
			Type:        "recommendation",
			Title:       "Consider longer study sessions",
			Description: fmt.Sprintf("Your average session is %d minutes. Research shows 25-45 minute sessions are optimal for retention.", avgSession),
			Action:      "Try 25-minute focused sessions",
			Priority:    2,
			CreatedAt:   analysis.AnalysisTimestamp,
		}
		insights = append(insights, insight)
	} else if avgSession > 90 {
		insight := models.AnalyticsInsight{
			Type:        "recommendation",
			Title:       "Break up long study sessions",
			Description: fmt.Sprintf("Your %d-minute sessions might lead to fatigue. Consider shorter, more frequent sessions with breaks.", avgSession),
			Action:      "Try the Pomodoro technique",
			Priority:    2,
			CreatedAt:   analysis.AnalysisTimestamp,
		}
		insights = append(insights, insight)
	}

	return insights
}

// generateAchievementInsights identifies and celebrates achievements
func (ie *InsightEngine) generateAchievementInsights(analysis *InsightAnalysis) []models.AnalyticsInsight {
	var insights []models.AnalyticsInsight

	// Check for recent high scores
	if len(analysis.RecentTests) > 0 && analysis.RecentTests[0].Score != nil {
		latestScore := *analysis.RecentTests[0].Score
		if latestScore >= 90 {
			insight := models.AnalyticsInsight{
				Type:        "achievement",
				Title:       "Outstanding performance!",
				Description: fmt.Sprintf("You scored %.1f%% on your latest test - that's excellent work!", latestScore),
				Action:      "Share your success",
				Priority:    3,
				CreatedAt:   analysis.AnalysisTimestamp,
			}
			insights = append(insights, insight)
		}
	}

	// Check for improvement milestones
	if len(analysis.TopicPerformances) > 0 {
		improvedTopics := 0
		for _, topic := range analysis.TopicPerformances {
			if topic.ProficiencyScore > 70 {
				improvedTopics++
			}
		}

		if improvedTopics >= len(analysis.TopicPerformances)*3/4 { // 75% of topics are good
			insight := models.AnalyticsInsight{
				Type:        "achievement",
				Title:       "Well-rounded performance!",
				Description: fmt.Sprintf("You're performing well in %d out of %d topics. Great balanced progress!", improvedTopics, len(analysis.TopicPerformances)),
				Action:      "Focus on remaining weak areas",
				Priority:    3,
				CreatedAt:   analysis.AnalysisTimestamp,
			}
			insights = append(insights, insight)
		}
	}

	return insights
}

// Helper functions

func (ie *InsightEngine) identifyWeakestTopics(topics []models.TopicPerformanceWithName, limit int) []models.TopicPerformanceWithName {
	if len(topics) == 0 {
		return nil
	}

	// Sort by proficiency score (ascending)
	sorted := make([]models.TopicPerformanceWithName, len(topics))
	copy(sorted, topics)
	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i].ProficiencyScore < sorted[j].ProficiencyScore
	})

	if len(sorted) > limit {
		sorted = sorted[:limit]
	}

	return sorted
}

func (ie *InsightEngine) identifyStrongestTopics(topics []models.TopicPerformanceWithName, limit int) []models.TopicPerformanceWithName {
	if len(topics) == 0 {
		return nil
	}

	// Sort by proficiency score (descending)
	sorted := make([]models.TopicPerformanceWithName, len(topics))
	copy(sorted, topics)
	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i].ProficiencyScore > sorted[j].ProficiencyScore
	})

	if len(sorted) > limit {
		sorted = sorted[:limit]
	}

	return sorted
}

func (ie *InsightEngine) calculateTrend(scores []float64) float64 {
	if len(scores) < 2 {
		return 0
	}

	// Simple linear regression to calculate trend
	n := float64(len(scores))
	sumX, sumY, sumXY, sumX2 := 0.0, 0.0, 0.0, 0.0

	for i, score := range scores {
		x := float64(i)
		sumX += x
		sumY += score
		sumXY += x * score
		sumX2 += x * x
	}

	// Calculate slope (trend)
	slope := (n*sumXY - sumX*sumY) / (n*sumX2 - sumX*sumX)

	// Convert to percentage change over the period
	if len(scores) > 0 {
		return slope * (n - 1) / scores[0] * 100
	}

	return slope
}
