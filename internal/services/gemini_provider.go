package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
	"test-spark-backend/internal/types"

	"github.com/google/uuid"
)

// GeminiProvider implements AIProvider for Google Gemini API
type GeminiProvider struct {
	config     *types.GeminiProviderConfig
	store      database.Store
	httpClient *http.Client
}

// GeminiRequest represents a request to Gemini API
type GeminiRequest struct {
	Contents         []GeminiContent        `json:"contents"`
	GenerationConfig GeminiGenerationConfig `json:"generationConfig,omitempty"`
}

// GeminiContent represents content in a Gemini request
type GeminiContent struct {
	Parts []GeminiPart `json:"parts"`
}

// GeminiPart represents a part of Gemini content
type GeminiPart struct {
	Text string `json:"text"`
}

// GeminiGenerationConfig represents generation configuration for Gemini
type GeminiGenerationConfig struct {
	Temperature     float64 `json:"temperature,omitempty"`
	MaxOutputTokens int     `json:"maxOutputTokens,omitempty"`
}

// GeminiResponse represents a response from Gemini API
type GeminiResponse struct {
	Candidates []GeminiCandidate `json:"candidates"`
	Error      *GeminiError      `json:"error,omitempty"`
}

// GeminiCandidate represents a candidate response from Gemini
type GeminiCandidate struct {
	Content GeminiContent `json:"content"`
}

// GeminiError represents an error from Gemini API
type GeminiError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// NewGeminiProvider creates a new Gemini provider
func NewGeminiProvider(config *types.GeminiProviderConfig, store database.Store) *GeminiProvider {
	return &GeminiProvider{
		config: config,
		store:  store,
		httpClient: &http.Client{
			Timeout: time.Duration(config.TimeoutSeconds) * time.Second,
		},
	}
}

// GenerateQuestions implements AIProvider.GenerateQuestions
func (gm *GeminiProvider) GenerateQuestions(ctx context.Context, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	// Build the enhanced prompt
	builder := NewPromptTemplateBuilder()
	prompt := builder.BuildEnhancedPrompt(genCtx)

	// Make API request
	responseContent, err := gm.makeAPIRequest(ctx, prompt)
	if err != nil {
		return nil, fmt.Errorf("failed to make API request: %w", err)
	}

	// Parse the response
	questions, err := gm.parseQuestionsFromResponse(responseContent, genCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to parse questions: %w", err)
	}

	return questions, nil
}

// GetProviderName implements AIProvider.GetProviderName
func (gm *GeminiProvider) GetProviderName() string {
	return "gemini"
}

// IsAvailable implements AIProvider.IsAvailable
func (gm *GeminiProvider) IsAvailable(ctx context.Context) bool {
	if gm.config.APIKey == "" {
		return false
	}

	// Check if we have the required configuration
	return gm.config.BaseURL != "" && gm.config.Model != ""
}

// GetCapabilities implements AIProvider.GetCapabilities
func (gm *GeminiProvider) GetCapabilities() types.ProviderCapabilities {
	return types.ProviderCapabilities{
		MaxQuestionsPerRequest: 30, // Good free tier limits
		SupportedDifficulties:  []string{"easy", "medium", "hard"},
		SupportsGradeLevel:     true,
		SupportsBoard:          true,
		MaxTokens:              gm.config.MaxTokens,
		SupportsStreaming:      false,
	}
}

// makeAPIRequest makes a request to the Gemini API
func (gm *GeminiProvider) makeAPIRequest(ctx context.Context, prompt string) (string, error) {
	// Prepare request body
	requestBody := GeminiRequest{
		Contents: []GeminiContent{
			{
				Parts: []GeminiPart{
					{Text: prompt},
				},
			},
		},
		GenerationConfig: GeminiGenerationConfig{
			Temperature:     gm.config.Temperature,
			MaxOutputTokens: gm.config.MaxTokens,
		},
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create request URL
	url := fmt.Sprintf("%s/%s:generateContent?key=%s", gm.config.BaseURL, gm.config.Model, gm.config.APIKey)
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Make request
	resp, err := gm.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var geminiResp GeminiResponse
	if err := json.NewDecoder(resp.Body).Decode(&geminiResp); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	// Check for API errors
	if geminiResp.Error != nil {
		return "", &types.AIProviderError{
			Message:  geminiResp.Error.Message,
			Provider: "gemini",
			Code:     fmt.Sprintf("GEMINI_ERROR_%d", geminiResp.Error.Code),
		}
	}

	// Extract content from response
	if len(geminiResp.Candidates) == 0 || len(geminiResp.Candidates[0].Content.Parts) == 0 {
		return "", &types.AIProviderError{
			Message:  "empty response from Gemini API",
			Provider: "gemini",
			Code:     "EMPTY_RESPONSE",
		}
	}

	return geminiResp.Candidates[0].Content.Parts[0].Text, nil
}

// parseQuestionsFromResponse parses questions from the API response
func (gm *GeminiProvider) parseQuestionsFromResponse(responseContent string, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	// Try to parse JSON response first
	var parsedQuestions []models.QuestionContent
	if err := json.Unmarshal([]byte(responseContent), &parsedQuestions); err == nil && len(parsedQuestions) > 0 {
		return gm.convertToQuestions(parsedQuestions, genCtx), nil
	}

	// If JSON parsing fails, fall back to generating mock questions with Gemini branding
	return gm.generateFallbackQuestions(genCtx), nil
}

// convertToQuestions converts parsed question content to Question models
func (gm *GeminiProvider) convertToQuestions(parsedQuestions []models.QuestionContent, genCtx *QuestionGenerationContext) []models.Question {
	var questions []models.Question
	providerName := gm.GetProviderName()

	for i, qContent := range parsedQuestions {
		if i >= genCtx.NumQuestions {
			break
		}

		contentBytes, _ := json.Marshal(qContent)

		// Determine topic and difficulty for this question
		topicID := gm.selectTopicForQuestion(i, genCtx)
		difficulty := gm.selectDifficultyForQuestion(i, genCtx)

		question := models.Question{
			ID:            uuid.New(),
			TopicID:       topicID,
			Content:       contentBytes,
			Difficulty:    &difficulty,
			AuthorAIModel: &providerName,
			CreatedAt:     time.Now(),
		}

		questions = append(questions, question)
	}

	return questions
}

// generateFallbackQuestions generates fallback questions when parsing fails
func (gm *GeminiProvider) generateFallbackQuestions(genCtx *QuestionGenerationContext) []models.Question {
	var questions []models.Question
	providerName := gm.GetProviderName()

	for i := 0; i < genCtx.NumQuestions; i++ {
		// Select topic and difficulty cyclically
		topicName := genCtx.TopicNames[i%len(genCtx.TopicNames)]
		difficulty := genCtx.Difficulties[i%len(genCtx.Difficulties)]

		// Create fallback question content
		questionContent := models.QuestionContent{
			Question: fmt.Sprintf("[Gemini] Question %d about %s (%s level): What is a key principle in this topic?", i+1, topicName, string(difficulty)),
			Options: []string{
				"This is option A (generated by Gemini)",
				"This is the correct answer (Gemini)",
				"This is option C (generated by Gemini)",
				"This is option D (generated by Gemini)",
			},
			CorrectOptionIndex: 1, // Option B is correct
			Explanation:        fmt.Sprintf("This is a Gemini-generated question for %s. The model used was %s.", topicName, gm.config.Model),
		}

		contentBytes, _ := json.Marshal(questionContent)

		question := models.Question{
			ID:            uuid.New(),
			TopicID:       gm.selectTopicForQuestion(i, genCtx),
			Content:       contentBytes,
			Difficulty:    &difficulty,
			AuthorAIModel: &providerName,
			CreatedAt:     time.Now(),
		}

		questions = append(questions, question)
	}

	return questions
}

// Helper methods for topic and difficulty selection
func (gm *GeminiProvider) selectTopicForQuestion(questionIndex int, genCtx *QuestionGenerationContext) int {
	// Use actual topic IDs from the context
	if len(genCtx.TopicIDs) == 0 {
		return 1 // Default topic ID as fallback
	}

	// Distribute topics cyclically using actual topic IDs
	return genCtx.TopicIDs[questionIndex%len(genCtx.TopicIDs)]
}

func (gm *GeminiProvider) selectDifficultyForQuestion(questionIndex int, genCtx *QuestionGenerationContext) models.DifficultyLevel {
	return genCtx.Difficulties[questionIndex%len(genCtx.Difficulties)]
}
