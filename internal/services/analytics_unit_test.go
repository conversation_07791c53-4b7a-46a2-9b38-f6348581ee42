package services

import (
	"testing"
	"time"

	"test-spark-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

// Test helper functions and algorithms without requiring database mocks

func TestInsightEngine_CalculateTrend_Unit(t *testing.T) {
	tests := []struct {
		name     string
		scores   []float64
		expected float64
	}{
		{
			name:     "improving trend",
			scores:   []float64{70.0, 75.0, 80.0, 85.0},
			expected: 21.43, // Approximately 21.43% improvement
		},
		{
			name:     "declining trend",
			scores:   []float64{85.0, 80.0, 75.0, 70.0},
			expected: -17.65, // Approximately -17.65% decline
		},
		{
			name:     "stable trend",
			scores:   []float64{75.0, 75.0, 75.0, 75.0},
			expected: 0.0,
		},
		{
			name:     "empty scores",
			scores:   []float64{},
			expected: 0.0,
		},
		{
			name:     "single score",
			scores:   []float64{75.0},
			expected: 0.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a minimal insight engine for testing the algorithm
			ie := &InsightEngine{}
			trend := ie.calculateTrend(tt.scores)
			assert.InDelta(t, tt.expected, trend, 1.0, // Allow 1% delta for floating point precision
				"Trend calculation should be within 1% of expected value")
		})
	}
}

func TestInsightEngine_IdentifyWeakestTopics_Unit(t *testing.T) {
	topics := []models.TopicPerformanceWithName{
		{
			TopicID:          1,
			TopicName:        "Algebra",
			SubjectName:      "Mathematics",
			TotalAttempted:   10,
			TotalCorrect:     3,
			ProficiencyScore: 30.0, // Weakest
		},
		{
			TopicID:          2,
			TopicName:        "Geometry",
			SubjectName:      "Mathematics",
			TotalAttempted:   15,
			TotalCorrect:     12,
			ProficiencyScore: 80.0, // Strongest
		},
		{
			TopicID:          3,
			TopicName:        "Mechanics",
			SubjectName:      "Physics",
			TotalAttempted:   8,
			TotalCorrect:     4,
			ProficiencyScore: 50.0, // Middle
		},
	}

	ie := &InsightEngine{}
	weakest := ie.identifyWeakestTopics(topics, 2)

	assert.Len(t, weakest, 2)
	assert.Equal(t, "Algebra", weakest[0].TopicName)   // Lowest score (30.0)
	assert.Equal(t, "Mechanics", weakest[1].TopicName) // Second lowest (50.0)
}

func TestInsightEngine_IdentifyStrongestTopics_Unit(t *testing.T) {
	topics := []models.TopicPerformanceWithName{
		{
			TopicID:          1,
			TopicName:        "Algebra",
			ProficiencyScore: 30.0,
		},
		{
			TopicID:          2,
			TopicName:        "Geometry",
			ProficiencyScore: 80.0,
		},
		{
			TopicID:          3,
			TopicName:        "Mechanics",
			ProficiencyScore: 50.0,
		},
	}

	ie := &InsightEngine{}
	strongest := ie.identifyStrongestTopics(topics, 2)

	assert.Len(t, strongest, 2)
	assert.Equal(t, "Geometry", strongest[0].TopicName)  // Highest score (80.0)
	assert.Equal(t, "Mechanics", strongest[1].TopicName) // Second highest (50.0)
}

func TestPatternRecognition_HelperFunctions_Unit(t *testing.T) {
	t.Run("average calculation", func(t *testing.T) {
		tests := []struct {
			name     string
			values   []float64
			expected float64
		}{
			{"normal values", []float64{10, 20, 30}, 20.0},
			{"single value", []float64{42}, 42.0},
			{"empty slice", []float64{}, 0.0},
			{"decimal values", []float64{1.5, 2.5, 3.0}, 2.333333333333333},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				result := average(tt.values)
				assert.InDelta(t, tt.expected, result, 0.000001)
			})
		}
	})

	t.Run("standard deviation calculation", func(t *testing.T) {
		tests := []struct {
			name     string
			values   []float64
			expected float64
		}{
			{"identical values", []float64{5, 5, 5, 5}, 0.0},
			{"simple spread", []float64{1, 2, 3, 4, 5}, 1.4142135623730951},
			{"empty slice", []float64{}, 0.0},
			{"single value", []float64{10}, 0.0},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				result := standardDeviation(tt.values)
				assert.InDelta(t, tt.expected, result, 0.000001)
			})
		}
	})
}

func TestRecommendationEngine_HelperFunctions_Unit(t *testing.T) {
	re := &RecommendationEngine{}

	t.Run("findWeakestTopics", func(t *testing.T) {
		topics := []models.TopicPerformanceWithName{
			{TopicName: "Algebra", ProficiencyScore: 30.0},
			{TopicName: "Geometry", ProficiencyScore: 80.0},
			{TopicName: "Mechanics", ProficiencyScore: 50.0},
		}

		weakest := re.findWeakestTopics(topics, 2)

		assert.Len(t, weakest, 2)
		assert.Equal(t, "Algebra", weakest[0].TopicName)   // 30% proficiency
		assert.Equal(t, "Mechanics", weakest[1].TopicName) // 50% proficiency
	})

	t.Run("calculateExpectedImpact", func(t *testing.T) {
		tests := []struct {
			currentScore   float64
			expectedImpact float64
		}{
			{25.0, 40.0}, // Very low score, high impact
			{45.0, 30.0}, // Low score, medium-high impact
			{65.0, 20.0}, // Medium score, medium impact
			{85.0, 10.0}, // High score, low impact
		}

		for _, tt := range tests {
			impact := re.calculateExpectedImpact(tt.currentScore)
			assert.Equal(t, tt.expectedImpact, impact)
		}
	})

	t.Run("estimateTimeToResults", func(t *testing.T) {
		tests := []struct {
			currentScore float64
			expectedDays int
		}{
			{25.0, 21}, // Very low score, 3 weeks
			{45.0, 14}, // Low score, 2 weeks
			{65.0, 7},  // Medium score, 1 week
		}

		for _, tt := range tests {
			days := re.estimateTimeToResults(tt.currentScore)
			assert.Equal(t, tt.expectedDays, days)
		}
	})

	t.Run("assessDifficulty", func(t *testing.T) {
		tests := []struct {
			currentScore       float64
			expectedDifficulty string
		}{
			{25.0, "hard"},
			{45.0, "medium"},
			{75.0, "easy"},
		}

		for _, tt := range tests {
			difficulty := re.assessDifficulty(tt.currentScore)
			assert.Equal(t, tt.expectedDifficulty, difficulty)
		}
	})
}

func TestRecommendationEngine_GenerateActionItems_Unit(t *testing.T) {
	re := &RecommendationEngine{}

	t.Run("generateTopicActionItems", func(t *testing.T) {
		topic := models.TopicPerformanceWithName{
			TopicID:   1,
			TopicName: "Algebra",
		}

		actionItems := re.generateTopicActionItems(topic)

		assert.Len(t, actionItems, 3) // Should generate 3 action items

		// Verify action item structure
		topicMentioned := false
		for _, item := range actionItems {
			assert.NotEmpty(t, item.ID)
			assert.NotEmpty(t, item.Description)
			assert.NotEmpty(t, item.Type)
			assert.Greater(t, item.EstimatedTime, 0)
			if item.Description != "Take a mini-test to track improvement" {
				assert.Contains(t, item.Description, "Algebra") // Should mention the topic
				topicMentioned = true
			}
		}
		assert.True(t, topicMentioned, "At least one action item should mention the topic")
	})

	t.Run("generateScheduleActionItems", func(t *testing.T) {
		patterns := &models.StudyPattern{
			ConsistencyScore: 35.0,
		}

		actionItems := re.generateScheduleActionItems(patterns)

		assert.Len(t, actionItems, 3) // Should generate 3 action items

		// Verify action item structure
		for _, item := range actionItems {
			assert.NotEmpty(t, item.ID)
			assert.NotEmpty(t, item.Description)
			assert.NotEmpty(t, item.Type)
			assert.Greater(t, item.EstimatedTime, 0)
		}

		// Verify specific action types
		actionTypes := make(map[string]bool)
		for _, item := range actionItems {
			actionTypes[item.Type] = true
		}
		assert.True(t, actionTypes["schedule"])
		assert.True(t, actionTypes["strategy"])
	})
}

func TestInsightGeneration_Logic_Unit(t *testing.T) {
	t.Run("performance insight generation logic", func(t *testing.T) {
		// Test the logic for generating performance insights
		weakTopics := []models.TopicPerformanceWithName{
			{
				TopicID:          1,
				TopicName:        "Algebra",
				SubjectName:      "Mathematics",
				ProficiencyScore: 25.0, // Very weak
			},
			{
				TopicID:          2,
				TopicName:        "Geometry",
				SubjectName:      "Mathematics",
				ProficiencyScore: 45.0, // Weak
			},
		}

		// Simulate insight generation logic
		var insights []models.AnalyticsInsight
		for i, topic := range weakTopics {
			priority := 5 - i // Higher priority for weaker topics
			if topic.ProficiencyScore < 40 {
				priority = 5 // Critical priority for very weak topics
			}

			insight := models.AnalyticsInsight{
				Type:        "concern",
				Title:       "Topic needs attention",
				Description: "Low proficiency detected",
				Priority:    priority,
				CreatedAt:   time.Now(),
				TopicID:     &topic.TopicID,
			}
			insights = append(insights, insight)
		}

		assert.Len(t, insights, 2)
		assert.Equal(t, 5, insights[0].Priority) // First topic should have critical priority
		assert.Equal(t, 4, insights[1].Priority) // Second topic should have high priority
		assert.Equal(t, "concern", insights[0].Type)
	})

	t.Run("consistency insight generation logic", func(t *testing.T) {
		// Test consistency-based insight generation
		studyPatterns := &models.StudyPattern{
			ConsistencyScore: 35.0, // Low consistency
			CurrentStreak:    0,    // No current streak
			LongestStreak:    10,   // Had a good streak before
		}

		var insights []models.AnalyticsInsight

		// Low consistency generates recommendation
		if studyPatterns.ConsistencyScore < 60 {
			insight := models.AnalyticsInsight{
				Type:        "recommendation",
				Title:       "Improve study consistency",
				Description: "Regular practice leads to better retention",
				Priority:    4,
				CreatedAt:   time.Now(),
			}
			insights = append(insights, insight)
		}

		// Broken streak generates motivation
		if studyPatterns.CurrentStreak == 0 && studyPatterns.LongestStreak > 7 {
			insight := models.AnalyticsInsight{
				Type:        "recommendation",
				Title:       "Restart your study streak",
				Description: "You previously achieved a good streak",
				Priority:    3,
				CreatedAt:   time.Now(),
			}
			insights = append(insights, insight)
		}

		assert.Len(t, insights, 2)
		assert.Equal(t, "recommendation", insights[0].Type)
		assert.Equal(t, 4, insights[0].Priority)
		assert.Equal(t, 3, insights[1].Priority)
	})
}
