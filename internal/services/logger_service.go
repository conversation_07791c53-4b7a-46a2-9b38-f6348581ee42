// Package services provides structured logging functionality for the Test-Spark application.
// This service wraps logrus to provide consistent, structured logging across the application
// with proper log levels, context, and JSON formatting for production environments.
package services

import (
	"context"
	"os"
	"strings"

	"github.com/sirupsen/logrus"
)

// Logger wraps logrus.Logger with additional context and convenience methods
type Logger struct {
	*logrus.Logger
	serviceName string
	version     string
}

// LoggerConfig holds configuration for the logger service
type LoggerConfig struct {
	Level       string // debug, info, warn, error
	Format      string // json, text
	ServiceName string
	Version     string
	Output      string // stdout, stderr, file path
}

// NewLogger creates a new structured logger with the given configuration
func NewLogger(config *LoggerConfig) *Logger {
	if config == nil {
		config = &LoggerConfig{
			Level:       "info",
			Format:      "json",
			ServiceName: "test-spark-backend",
			Version:     "1.0.0",
			Output:      "stdout",
		}
	}

	logger := logrus.New()

	// Set log level
	level, err := logrus.ParseLevel(config.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// Set formatter
	if config.Format == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02T15:04:05.000Z07:00",
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "timestamp",
				logrus.FieldKeyLevel: "level",
				logrus.FieldKeyMsg:   "message",
			},
		})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02T15:04:05.000Z07:00",
		})
	}

	// Set output
	switch config.Output {
	case "stderr":
		logger.SetOutput(os.Stderr)
	case "stdout":
		logger.SetOutput(os.Stdout)
	default:
		// For file output, we'd need to open the file
		logger.SetOutput(os.Stdout)
	}

	// Add default fields
	logger = logger.WithFields(logrus.Fields{
		"service": config.ServiceName,
		"version": config.Version,
	}).Logger

	return &Logger{
		Logger:      logger,
		serviceName: config.ServiceName,
		version:     config.Version,
	}
}

// WithContext adds context information to the logger
func (l *Logger) WithContext(ctx context.Context) *logrus.Entry {
	entry := l.Logger.WithContext(ctx)

	// Add request ID if available
	if requestID := ctx.Value("request_id"); requestID != nil {
		entry = entry.WithField("request_id", requestID)
	}

	// Add user ID if available
	if userID := ctx.Value("user_id"); userID != nil {
		entry = entry.WithField("user_id", userID)
	}

	return entry
}

// WithComponent adds component information to the logger
func (l *Logger) WithComponent(component string) *logrus.Entry {
	return l.Logger.WithField("component", component)
}

// WithUserID adds user ID to the logger
func (l *Logger) WithUserID(userID string) *logrus.Entry {
	return l.Logger.WithField("user_id", userID)
}

// WithRequestID adds request ID to the logger
func (l *Logger) WithRequestID(requestID string) *logrus.Entry {
	return l.Logger.WithField("request_id", requestID)
}

// WithError adds error information to the logger
func (l *Logger) WithError(err error) *logrus.Entry {
	return l.Logger.WithError(err)
}

// WithFields adds multiple fields to the logger
func (l *Logger) WithFields(fields map[string]interface{}) *logrus.Entry {
	return l.Logger.WithFields(logrus.Fields(fields))
}

// Convenience methods for different log levels with structured data

// LogAPIRequest logs an API request with structured data
func (l *Logger) LogAPIRequest(method, path, userID, requestID string, statusCode int, duration int64) {
	l.WithFields(map[string]interface{}{
		"method":      method,
		"path":        path,
		"user_id":     userID,
		"request_id":  requestID,
		"status_code": statusCode,
		"duration_ms": duration,
		"type":        "api_request",
	}).Info("API request processed")
}

// LogDatabaseQuery logs a database query with performance metrics
func (l *Logger) LogDatabaseQuery(query, operation string, duration int64, rowsAffected int64) {
	l.WithFields(map[string]interface{}{
		"operation":     operation,
		"duration_ms":   duration,
		"rows_affected": rowsAffected,
		"type":          "database_query",
	}).Debug("Database query executed")
}

// LogCacheOperation logs cache operations
func (l *Logger) LogCacheOperation(operation, key string, hit bool, duration int64) {
	l.WithFields(map[string]interface{}{
		"operation":   operation,
		"cache_key":   key,
		"cache_hit":   hit,
		"duration_ms": duration,
		"type":        "cache_operation",
	}).Debug("Cache operation")
}

// LogWebSocketEvent logs WebSocket events
func (l *Logger) LogWebSocketEvent(event, userID string, connectionCount int) {
	l.WithFields(map[string]interface{}{
		"event":            event,
		"user_id":          userID,
		"connection_count": connectionCount,
		"type":             "websocket_event",
	}).Info("WebSocket event")
}

// LogTestCompletion logs test completion events
func (l *Logger) LogTestCompletion(userID, testID string, score float64, duration int64) {
	l.WithFields(map[string]interface{}{
		"user_id":     userID,
		"test_id":     testID,
		"score":       score,
		"duration_ms": duration,
		"type":        "test_completion",
	}).Info("Test completed")
}

// LogAIGeneration logs AI question generation events
func (l *Logger) LogAIGeneration(provider string, questionCount int, duration int64, success bool) {
	l.WithFields(map[string]interface{}{
		"provider":       provider,
		"question_count": questionCount,
		"duration_ms":    duration,
		"success":        success,
		"type":           "ai_generation",
	}).Info("AI question generation")
}

// LogSecurityEvent logs security-related events
func (l *Logger) LogSecurityEvent(event, userID, ipAddress string, details map[string]interface{}) {
	fields := map[string]interface{}{
		"event":      event,
		"user_id":    userID,
		"ip_address": ipAddress,
		"type":       "security_event",
	}

	// Merge additional details
	for k, v := range details {
		fields[k] = v
	}

	l.WithFields(fields).Warn("Security event")
}

// LogError logs errors with context
func (l *Logger) LogError(err error, component string, context map[string]interface{}) {
	fields := map[string]interface{}{
		"component": component,
		"type":      "error",
	}

	// Merge context
	for k, v := range context {
		fields[k] = v
	}

	l.WithError(err).WithFields(fields).Error("Error occurred")
}

// LogPerformanceMetric logs performance metrics
func (l *Logger) LogPerformanceMetric(metric string, value float64, unit string, tags map[string]string) {
	fields := map[string]interface{}{
		"metric": metric,
		"value":  value,
		"unit":   unit,
		"type":   "performance_metric",
	}

	// Add tags
	for k, v := range tags {
		fields["tag_"+k] = v
	}

	l.WithFields(fields).Info("Performance metric")
}

// LogBusinessEvent logs business logic events
func (l *Logger) LogBusinessEvent(event string, userID string, data map[string]interface{}) {
	fields := map[string]interface{}{
		"event":   event,
		"user_id": userID,
		"type":    "business_event",
	}

	// Merge data
	for k, v := range data {
		fields[k] = v
	}

	l.WithFields(fields).Info("Business event")
}

// Global logger instance
var globalLogger *Logger

// InitGlobalLogger initializes the global logger
func InitGlobalLogger(config *LoggerConfig) {
	globalLogger = NewLogger(config)
}

// GetLogger returns the global logger instance
func GetLogger() *Logger {
	if globalLogger == nil {
		// Initialize with default config if not already initialized
		InitGlobalLogger(nil)
	}
	return globalLogger
}

// Helper function to parse log level from environment
func ParseLogLevel(level string) string {
	level = strings.ToLower(level)
	switch level {
	case "debug", "info", "warn", "warning", "error", "fatal", "panic":
		return level
	default:
		return "info"
	}
}
