// Package services provides Redis-based cached analytics functionality for improved performance.
// This service wraps the standard analytics service with Redis caching capabilities
// to reduce database load and improve response times for frequently accessed data.
package services

import (
	"context"
	"log"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
)

// RedisCachedAnalyticsService wraps the analytics service with Redis caching capabilities
type RedisCachedAnalyticsService struct {
	store             database.Store
	redisCacheService *RedisCacheService
}

// NewRedisCachedAnalyticsService creates a new Redis cached analytics service
func NewRedisCachedAnalyticsService(store database.Store, redisCacheService *RedisCacheService) *RedisCachedAnalyticsService {
	return &RedisCachedAnalyticsService{
		store:             store,
		redisCacheService: redisCacheService,
	}
}

// GetDashboardSummary retrieves dashboard summary with Redis caching
func (rcas *RedisCachedAnalyticsService) GetDashboardSummary(ctx context.Context, userID string) (*models.DashboardSummary, error) {
	// Try to get from Redis cache first
	if cached, found := rcas.redisCacheService.GetCachedDashboard(userID); found {
		log.Printf("Dashboard summary Redis cache hit for user: %s", userID)
		return cached, nil
	}

	// Cache miss - fetch from database
	log.Printf("Dashboard summary Redis cache miss for user: %s, fetching from database", userID)
	summary, err := rcas.store.GetDashboardSummary(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Store in Redis cache for future requests
	rcas.redisCacheService.SetCachedDashboard(userID, summary)
	return summary, nil
}

// GetPerformanceTrends retrieves performance trends with Redis caching
func (rcas *RedisCachedAnalyticsService) GetPerformanceTrends(ctx context.Context, userID string, timeFilter models.TimeFilter) ([]models.PerformanceTrend, error) {
	// Try to get from Redis cache first
	if cached, found := rcas.redisCacheService.GetCachedPerformanceTrends(userID, timeFilter); found {
		log.Printf("Performance trends Redis cache hit for user: %s, filter: %s", userID, timeFilter)
		return cached, nil
	}

	// Cache miss - fetch from database
	log.Printf("Performance trends Redis cache miss for user: %s, filter: %s, fetching from database", userID, timeFilter)
	trends, err := rcas.store.GetPerformanceTrends(ctx, userID, timeFilter)
	if err != nil {
		return nil, err
	}

	// Store in Redis cache for future requests
	rcas.redisCacheService.SetCachedPerformanceTrends(userID, timeFilter, trends)
	return trends, nil
}

// GetStudyPatterns retrieves study patterns (no caching for now as it's less frequently accessed)
func (rcas *RedisCachedAnalyticsService) GetStudyPatterns(ctx context.Context, userID string) (*models.StudyPattern, error) {
	return rcas.store.GetStudyPatterns(ctx, userID)
}

// GetUserTopicPerformances retrieves topic performances with Redis caching
func (rcas *RedisCachedAnalyticsService) GetUserTopicPerformances(ctx context.Context, userID string) ([]models.TopicPerformanceWithName, error) {
	// Try to get from Redis cache first
	if cached, found := rcas.redisCacheService.GetCachedTopicPerformance(userID); found {
		log.Printf("Topic performance Redis cache hit for user: %s", userID)
		return cached, nil
	}

	// Cache miss - fetch from database
	log.Printf("Topic performance Redis cache miss for user: %s, fetching from database", userID)
	performance, err := rcas.store.GetUserTopicPerformances(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Store in Redis cache for future requests
	rcas.redisCacheService.SetCachedTopicPerformance(userID, performance)
	return performance, nil
}

// GetHeatmapData retrieves heatmap data with Redis caching
func (rcas *RedisCachedAnalyticsService) GetHeatmapData(ctx context.Context, userID string) ([]models.HeatmapData, error) {
	// Try to get from Redis cache first
	if cached, found := rcas.redisCacheService.GetCachedHeatmapData(userID); found {
		log.Printf("Heatmap data Redis cache hit for user: %s", userID)
		return cached, nil
	}

	// Cache miss - fetch from database
	log.Printf("Heatmap data Redis cache miss for user: %s, fetching from database", userID)
	heatmap, err := rcas.store.GetHeatmapData(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Store in Redis cache for future requests
	rcas.redisCacheService.SetCachedHeatmapData(userID, heatmap)
	return heatmap, nil
}

// GetSubjectComparisons retrieves subject comparisons (no caching for now)
func (rcas *RedisCachedAnalyticsService) GetSubjectComparisons(ctx context.Context, userID string) ([]models.SubjectComparison, error) {
	return rcas.store.GetSubjectComparisons(ctx, userID)
}

// GetUserTestHistory retrieves user test history (no caching for now)
func (rcas *RedisCachedAnalyticsService) GetUserTestHistory(ctx context.Context, userID string, limit int) ([]models.UserTestHistory, error) {
	return rcas.store.GetUserTestHistory(ctx, userID, limit)
}

// GetTimeFilteredTestHistory retrieves time-filtered test history (no caching for now)
func (rcas *RedisCachedAnalyticsService) GetTimeFilteredTestHistory(ctx context.Context, userID string, timeFilter models.TimeFilter) ([]models.UserTestHistory, error) {
	return rcas.store.GetTimeFilteredTestHistory(ctx, userID, timeFilter)
}

// GetTimeFilteredTestHistoryWithDateRange retrieves test history with date range (no caching for now)
func (rcas *RedisCachedAnalyticsService) GetTimeFilteredTestHistoryWithDateRange(ctx context.Context, userID string, timeFilter models.TimeFilter, dateRange *models.DateRange) ([]models.UserTestHistory, error) {
	return rcas.store.GetTimeFilteredTestHistoryWithDateRange(ctx, userID, timeFilter, dateRange)
}

// GetPerformanceTrendsWithDateRange retrieves performance trends with date range (no caching for now)
func (rcas *RedisCachedAnalyticsService) GetPerformanceTrendsWithDateRange(ctx context.Context, userID string, timeFilter models.TimeFilter, dateRange *models.DateRange) ([]models.PerformanceTrend, error) {
	return rcas.store.GetPerformanceTrendsWithDateRange(ctx, userID, timeFilter, dateRange)
}

// GetUserInsights retrieves user insights (no caching for now)
func (rcas *RedisCachedAnalyticsService) GetUserInsights(ctx context.Context, userID string, limit int) ([]models.AnalyticsInsight, error) {
	return rcas.store.GetUserInsights(ctx, userID, limit)
}

// CreateInsight creates a new insight (no caching)
func (rcas *RedisCachedAnalyticsService) CreateInsight(ctx context.Context, insight *models.AnalyticsInsight) error {
	return rcas.store.CreateInsight(ctx, insight)
}

// GetWeakestTopics retrieves weakest topics (no caching for now)
func (rcas *RedisCachedAnalyticsService) GetWeakestTopics(ctx context.Context, userID string, limit int) ([]models.TopicPerformanceWithName, error) {
	return rcas.store.GetWeakestTopics(ctx, userID, limit)
}

// GetTopicByID retrieves topic by ID (no caching for now)
func (rcas *RedisCachedAnalyticsService) GetTopicByID(ctx context.Context, topicID int) (*models.Topic, error) {
	return rcas.store.GetTopicByID(ctx, topicID)
}

// GetTopicPerformance retrieves topic performance (no caching for now)
func (rcas *RedisCachedAnalyticsService) GetTopicPerformance(ctx context.Context, userID string, topicID int) (*models.TopicPerformanceSummary, error) {
	return rcas.store.GetTopicPerformance(ctx, userID, topicID)
}

// GetCacheStats returns Redis cache statistics for monitoring
func (rcas *RedisCachedAnalyticsService) GetCacheStats() map[string]interface{} {
	return rcas.redisCacheService.GetStats()
}

// GetCacheHealth returns Redis cache health information
func (rcas *RedisCachedAnalyticsService) GetCacheHealth() map[string]interface{} {
	err := rcas.redisCacheService.Ping()
	if err != nil {
		return map[string]interface{}{
			"status": "unhealthy",
			"error":  err.Error(),
			"type":   "redis",
		}
	}

	return map[string]interface{}{
		"status": "healthy",
		"type":   "redis",
	}
}

// Passthrough methods for non-cached operations

// CreateTestResult creates a test result and invalidates cache
func (rcas *RedisCachedAnalyticsService) CreateTestResult(ctx context.Context, result *models.TestResult) error {
	err := rcas.store.CreateTestResult(ctx, result)
	if err != nil {
		return err
	}

	// Invalidate Redis cache for the user since their data has changed
	rcas.InvalidateUserCache(result.UserID.String())
	return nil
}

// InvalidateUserCache removes all cached data for a specific user from Redis
func (rcas *RedisCachedAnalyticsService) InvalidateUserCache(userID string) {
	log.Printf("Invalidating Redis cache for user: %s", userID)
	rcas.redisCacheService.InvalidateUserCache(userID)
}

// Close closes the Redis connection
func (rcas *RedisCachedAnalyticsService) Close() error {
	return rcas.redisCacheService.Close()
}
