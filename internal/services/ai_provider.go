package services

import (
	"context"
	"log"

	"test-spark-backend/internal/models"
	"test-spark-backend/internal/types"
)

// AIProvider defines the interface for AI question generation providers
type AIProvider interface {
	// GenerateQuestions generates multiple choice questions based on the given context
	GenerateQuestions(ctx context.Context, genCtx *QuestionGenerationContext) ([]models.Question, error)

	// GetProviderName returns the name of the AI provider
	GetProviderName() string

	// IsAvailable checks if the provider is currently available
	IsAvailable(ctx context.Context) bool

	// GetCapabilities returns the capabilities of this provider
	GetCapabilities() types.ProviderCapabilities
}

// AIProviderManager manages multiple AI providers with fallback support
type AIProviderManager struct {
	providers map[string]AIProvider
	config    *types.AIProviderConfig
	primary   AIProvider
	fallback  AIProvider
}

// NewAIProviderManager creates a new AI provider manager
func NewAIProviderManager(config *types.AIProviderConfig) *AIProviderManager {
	manager := &AIProviderManager{
		providers: make(map[string]AIProvider),
		config:    config,
	}

	return manager
}

// RegisterProvider registers an AI provider with the manager
func (apm *AIProviderManager) RegisterProvider(name string, provider AIProvider) {
	apm.providers[name] = provider

	// Set primary and fallback providers based on configuration
	if name == apm.config.PrimaryProvider {
		apm.primary = provider
	}
	if name == apm.config.FallbackProvider {
		apm.fallback = provider
	}
}

// GenerateQuestions generates questions using the configured provider with multi-level fallback
func (apm *AIProviderManager) GenerateQuestions(ctx context.Context, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	// Define the fallback chain: Gemini -> Cerebras -> Groq -> Mock
	fallbackChain := []string{"gemini", "cerebras", "groq", "mock"}

	// Try each provider in the fallback chain
	for _, providerName := range fallbackChain {
		provider := apm.providers[providerName]
		if provider == nil {
			continue // Skip if provider not registered
		}

		if provider.IsAvailable(ctx) {
			log.Printf("Trying AI provider: %s", providerName)
			questions, err := provider.GenerateQuestions(ctx, genCtx)
			if err == nil {
				log.Printf("Successfully generated %d questions using %s", len(questions), providerName)
				return questions, nil
			}
			// Log error but continue to next provider
			log.Printf("AI provider (%s) failed: %v, trying next provider", providerName, err)
		} else {
			log.Printf("AI provider (%s) is not available, trying next provider", providerName)
		}
	}

	// If all providers fail, return a basic error
	return nil, &types.AIProviderError{
		Message:  "all configured AI providers are unavailable",
		Provider: "manager",
		Code:     "PROVIDERS_UNAVAILABLE",
	}
}

// GetProvider returns a specific provider by name
func (apm *AIProviderManager) GetProvider(name string) AIProvider {
	return apm.providers[name]
}

// GetAvailableProviders returns a list of all registered provider names
func (apm *AIProviderManager) GetAvailableProviders() []string {
	var providers []string
	for name := range apm.providers {
		providers = append(providers, name)
	}
	return providers
}

// GetActiveProvider returns the currently active provider based on priority order
func (apm *AIProviderManager) GetActiveProvider(ctx context.Context) AIProvider {
	// Check providers in priority order: Gemini -> Cerebras -> Groq -> Mock
	fallbackChain := []string{"gemini", "cerebras", "groq", "mock"}

	for _, providerName := range fallbackChain {
		provider := apm.providers[providerName]
		if provider != nil && provider.IsAvailable(ctx) {
			return provider
		}
	}
	return nil
}

// GetProviderStatus returns the status of all registered providers
func (apm *AIProviderManager) GetProviderStatus(ctx context.Context) map[string]bool {
	status := make(map[string]bool)
	for name, provider := range apm.providers {
		status[name] = provider.IsAvailable(ctx)
	}
	return status
}

// QuestionGenerationRequest represents a standardized request for question generation
type QuestionGenerationRequest struct {
	Context      *QuestionGenerationContext `json:"context"`
	ProviderHint string                     `json:"provider_hint,omitempty"` // Hint for which provider to use
}

// QuestionGenerationResponse represents a standardized response from question generation
type QuestionGenerationResponse struct {
	Questions    []models.Question `json:"questions"`
	Provider     string            `json:"provider"`
	GeneratedAt  string            `json:"generated_at"`
	TokensUsed   int               `json:"tokens_used,omitempty"`
	ProcessingMs int64             `json:"processing_ms"`
}
