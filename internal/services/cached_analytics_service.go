// Package services provides cached analytics functionality for improved performance.
// This service wraps the standard analytics service with caching capabilities
// to reduce database load and improve response times for frequently accessed data.
package services

import (
	"context"
	"log"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
)

// CachedAnalyticsService wraps the analytics service with caching capabilities
type CachedAnalyticsService struct {
	store        database.Store
	cacheService *CacheService
}

// NewCachedAnalyticsService creates a new cached analytics service
func NewCachedAnalyticsService(store database.Store, cacheService *CacheService) *CachedAnalyticsService {
	return &CachedAnalyticsService{
		store:        store,
		cacheService: cacheService,
	}
}

// GetDashboardSummary retrieves dashboard summary with caching
func (cas *CachedAnalyticsService) GetDashboardSummary(ctx context.Context, userID string) (*models.DashboardSummary, error) {
	// Try to get from cache first
	if cached, found := cas.cacheService.GetCachedDashboard(userID); found {
		log.Printf("Dashboard summary cache hit for user: %s", userID)
		return cached, nil
	}

	// Cache miss - fetch from database
	log.Printf("Dashboard summary cache miss for user: %s, fetching from database", userID)
	summary, err := cas.store.GetDashboardSummary(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Store in cache for future requests
	cas.cacheService.SetCachedDashboard(userID, summary)
	return summary, nil
}

// GetPerformanceTrends retrieves performance trends with caching
func (cas *CachedAnalyticsService) GetPerformanceTrends(ctx context.Context, userID string, timeFilter models.TimeFilter) ([]models.PerformanceTrend, error) {
	// Try to get from cache first
	if cached, found := cas.cacheService.GetCachedPerformanceTrends(userID, timeFilter); found {
		log.Printf("Performance trends cache hit for user: %s, filter: %s", userID, timeFilter)
		return cached, nil
	}

	// Cache miss - fetch from database
	log.Printf("Performance trends cache miss for user: %s, filter: %s, fetching from database", userID, timeFilter)
	trends, err := cas.store.GetPerformanceTrends(ctx, userID, timeFilter)
	if err != nil {
		return nil, err
	}

	// Store in cache for future requests
	cas.cacheService.SetCachedPerformanceTrends(userID, timeFilter, trends)
	return trends, nil
}

// GetPerformanceTrendsWithDateRange retrieves performance trends with date range and caching
func (cas *CachedAnalyticsService) GetPerformanceTrendsWithDateRange(ctx context.Context, userID string, timeFilter models.TimeFilter, dateRange *models.DateRange) ([]models.PerformanceTrend, error) {
	// For custom date ranges, we don't cache as they are likely unique
	if dateRange != nil {
		log.Printf("Custom date range request for user: %s, skipping cache", userID)
		return cas.store.GetPerformanceTrendsWithDateRange(ctx, userID, timeFilter, dateRange)
	}

	// Use the cached version for standard time filters
	return cas.GetPerformanceTrends(ctx, userID, timeFilter)
}

// GetUserTopicPerformances retrieves topic performances with caching
func (cas *CachedAnalyticsService) GetUserTopicPerformances(ctx context.Context, userID string) ([]models.TopicPerformanceWithName, error) {
	// Try to get from cache first
	if cached, found := cas.cacheService.GetCachedTopicPerformance(userID); found {
		log.Printf("Topic performance cache hit for user: %s", userID)
		return cached, nil
	}

	// Cache miss - fetch from database
	log.Printf("Topic performance cache miss for user: %s, fetching from database", userID)
	performance, err := cas.store.GetUserTopicPerformances(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Store in cache for future requests
	cas.cacheService.SetCachedTopicPerformance(userID, performance)
	return performance, nil
}

// GetHeatmapData retrieves heatmap data with caching
func (cas *CachedAnalyticsService) GetHeatmapData(ctx context.Context, userID string) ([]models.HeatmapData, error) {
	// Try to get from cache first
	if cached, found := cas.cacheService.GetCachedHeatmapData(userID); found {
		log.Printf("Heatmap data cache hit for user: %s", userID)
		return cached, nil
	}

	// Cache miss - fetch from database
	log.Printf("Heatmap data cache miss for user: %s, fetching from database", userID)
	heatmap, err := cas.store.GetHeatmapData(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Store in cache for future requests
	cas.cacheService.SetCachedHeatmapData(userID, heatmap)
	return heatmap, nil
}

// GetWeakestTopics retrieves weakest topics (not cached as it's less frequently accessed)
func (cas *CachedAnalyticsService) GetWeakestTopics(ctx context.Context, userID string, limit int) ([]models.TopicPerformanceWithName, error) {
	return cas.store.GetWeakestTopics(ctx, userID, limit)
}

// GetSubjectComparisons retrieves subject comparisons (not cached as it's less frequently accessed)
func (cas *CachedAnalyticsService) GetSubjectComparisons(ctx context.Context, userID string) ([]models.SubjectComparison, error) {
	return cas.store.GetSubjectComparisons(ctx, userID)
}

// GetStudyPatterns retrieves study patterns (not cached as it's less frequently accessed)
func (cas *CachedAnalyticsService) GetStudyPatterns(ctx context.Context, userID string) (*models.StudyPattern, error) {
	return cas.store.GetStudyPatterns(ctx, userID)
}

// GetUserInsights retrieves user insights (not cached as they should be fresh)
func (cas *CachedAnalyticsService) GetUserInsights(ctx context.Context, userID string, limit int) ([]models.AnalyticsInsight, error) {
	return cas.store.GetUserInsights(ctx, userID, limit)
}

// GetUserTestHistory retrieves test history (not cached as it's less frequently accessed)
func (cas *CachedAnalyticsService) GetUserTestHistory(ctx context.Context, userID string, limit int) ([]models.UserTestHistory, error) {
	return cas.store.GetUserTestHistory(ctx, userID, limit)
}

// GetTimeFilteredTestHistory retrieves filtered test history (not cached due to variability)
func (cas *CachedAnalyticsService) GetTimeFilteredTestHistory(ctx context.Context, userID string, timeFilter models.TimeFilter) ([]models.UserTestHistory, error) {
	return cas.store.GetTimeFilteredTestHistory(ctx, userID, timeFilter)
}

// GetTimeFilteredTestHistoryWithDateRange retrieves filtered test history with date range (not cached)
func (cas *CachedAnalyticsService) GetTimeFilteredTestHistoryWithDateRange(ctx context.Context, userID string, timeFilter models.TimeFilter, dateRange *models.DateRange) ([]models.UserTestHistory, error) {
	return cas.store.GetTimeFilteredTestHistoryWithDateRange(ctx, userID, timeFilter, dateRange)
}

// InvalidateUserCache invalidates all cached data for a user
// This should be called when user completes a test or when data changes
func (cas *CachedAnalyticsService) InvalidateUserCache(userID string) {
	log.Printf("Invalidating cache for user: %s", userID)
	cas.cacheService.InvalidateUserCache(userID)
}

// GetCacheStats returns cache statistics for monitoring
func (cas *CachedAnalyticsService) GetCacheStats() map[string]interface{} {
	return cas.cacheService.GetStats()
}

// GetCacheHealth returns cache health information
func (cas *CachedAnalyticsService) GetCacheHealth() map[string]interface{} {
	return cas.cacheService.GetCacheHealth()
}

// Passthrough methods for non-cached operations

// CreateTestResult creates a test result and invalidates cache
func (cas *CachedAnalyticsService) CreateTestResult(ctx context.Context, result *models.TestResult) error {
	err := cas.store.CreateTestResult(ctx, result)
	if err != nil {
		return err
	}

	// Invalidate cache for the user since their data has changed
	cas.InvalidateUserCache(result.UserID.String())
	return nil
}

// GetTestResult retrieves a test result (not cached as it's accessed infrequently)
func (cas *CachedAnalyticsService) GetTestResult(ctx context.Context, testID string) (*models.TestResult, error) {
	return cas.store.GetTestResult(ctx, testID)
}

// GetTopicPerformance retrieves specific topic performance (not cached due to specificity)
func (cas *CachedAnalyticsService) GetTopicPerformance(ctx context.Context, userID string, topicID int) (*models.TopicPerformanceSummary, error) {
	return cas.store.GetTopicPerformance(ctx, userID, topicID)
}

// CreateInsight creates a new insight (no caching)
func (cas *CachedAnalyticsService) CreateInsight(ctx context.Context, insight *models.AnalyticsInsight) error {
	return cas.store.CreateInsight(ctx, insight)
}

// GetTopicByID retrieves topic by ID (no caching for now)
func (cas *CachedAnalyticsService) GetTopicByID(ctx context.Context, topicID int) (*models.Topic, error) {
	return cas.store.GetTopicByID(ctx, topicID)
}

// Performance monitoring methods

// WarmupCache pre-loads frequently accessed data for a user
func (cas *CachedAnalyticsService) WarmupCache(ctx context.Context, userID string) error {
	log.Printf("Warming up cache for user: %s", userID)

	// Pre-load dashboard summary
	if _, err := cas.GetDashboardSummary(ctx, userID); err != nil {
		log.Printf("Failed to warm up dashboard cache for user %s: %v", userID, err)
	}

	// Pre-load topic performance
	if _, err := cas.GetUserTopicPerformances(ctx, userID); err != nil {
		log.Printf("Failed to warm up topic performance cache for user %s: %v", userID, err)
	}

	// Pre-load heatmap data
	if _, err := cas.GetHeatmapData(ctx, userID); err != nil {
		log.Printf("Failed to warm up heatmap cache for user %s: %v", userID, err)
	}

	// Pre-load performance trends for common time filters
	commonFilters := []models.TimeFilter{
		models.TimeFilterWeek,
		models.TimeFilterMonth,
		models.TimeFilterQuarter,
	}

	for _, filter := range commonFilters {
		if _, err := cas.GetPerformanceTrends(ctx, userID, filter); err != nil {
			log.Printf("Failed to warm up performance trends cache for user %s, filter %s: %v", userID, filter, err)
		}
	}

	log.Printf("Cache warmup completed for user: %s", userID)
	return nil
}
