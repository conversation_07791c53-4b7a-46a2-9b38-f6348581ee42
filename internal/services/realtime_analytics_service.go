// Package services provides real-time analytics functionality with WebSocket integration.
// This service wraps cached analytics services and adds real-time update capabilities
// through WebSocket connections for live dashboard updates.
package services

import (
	"context"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
)

// RealtimeAnalyticsService wraps cached analytics with real-time WebSocket updates
type RealtimeAnalyticsService struct {
	cachedAnalyticsService CachedAnalyticsInterface
	websocketService       *WebSocketService
	store                  database.Store
}

// NewRealtimeAnalyticsService creates a new real-time analytics service
func NewRealtimeAnalyticsService(
	cachedAnalyticsService CachedAnalyticsInterface,
	websocketService *WebSocketService,
	store database.Store,
) *RealtimeAnalyticsService {
	return &RealtimeAnalyticsService{
		cachedAnalyticsService: cachedAnalyticsService,
		websocketService:       websocketService,
		store:                  store,
	}
}

// All methods delegate to the cached analytics service but add real-time updates

// GetDashboardSummary retrieves dashboard summary and sends real-time update
func (ras *RealtimeAnalyticsService) GetDashboardSummary(ctx context.Context, userID string) (*models.DashboardSummary, error) {
	dashboard, err := ras.cachedAnalyticsService.GetDashboardSummary(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Send real-time update to connected clients
	ras.websocketService.SendDashboardUpdate(userID, dashboard)

	return dashboard, nil
}

// GetPerformanceTrends retrieves performance trends and sends real-time update
func (ras *RealtimeAnalyticsService) GetPerformanceTrends(ctx context.Context, userID string, timeFilter models.TimeFilter) ([]models.PerformanceTrend, error) {
	trends, err := ras.cachedAnalyticsService.GetPerformanceTrends(ctx, userID, timeFilter)
	if err != nil {
		return nil, err
	}

	// Send real-time update to connected clients
	ras.websocketService.SendPerformanceUpdate(userID, trends)

	return trends, nil
}

// CreateTestResult creates a test result and sends real-time notifications
func (ras *RealtimeAnalyticsService) CreateTestResult(ctx context.Context, result *models.TestResult) error {
	err := ras.cachedAnalyticsService.CreateTestResult(ctx, result)
	if err != nil {
		return err
	}

	// Send real-time test completion notification
	ras.websocketService.SendTestCompletedNotification(result.UserID.String(), result)

	// Invalidate cache and send updated dashboard
	ras.cachedAnalyticsService.InvalidateUserCache(result.UserID.String())

	// Get fresh dashboard data and send update
	dashboard, err := ras.cachedAnalyticsService.GetDashboardSummary(ctx, result.UserID.String())
	if err == nil {
		ras.websocketService.SendDashboardUpdate(result.UserID.String(), dashboard)
	}

	return nil
}

// CreateInsight creates an insight (no real-time notification for now due to model limitations)
func (ras *RealtimeAnalyticsService) CreateInsight(ctx context.Context, insight *models.AnalyticsInsight) error {
	err := ras.cachedAnalyticsService.CreateInsight(ctx, insight)
	if err != nil {
		return err
	}

	// TODO: Add real-time insight notification when user context is available

	return nil
}

// Delegate all other methods to the cached analytics service

func (ras *RealtimeAnalyticsService) GetPerformanceTrendsWithDateRange(ctx context.Context, userID string, timeFilter models.TimeFilter, dateRange *models.DateRange) ([]models.PerformanceTrend, error) {
	return ras.cachedAnalyticsService.GetPerformanceTrendsWithDateRange(ctx, userID, timeFilter, dateRange)
}

func (ras *RealtimeAnalyticsService) GetUserTopicPerformances(ctx context.Context, userID string) ([]models.TopicPerformanceWithName, error) {
	return ras.cachedAnalyticsService.GetUserTopicPerformances(ctx, userID)
}

func (ras *RealtimeAnalyticsService) GetTopicPerformance(ctx context.Context, userID string, topicID int) (*models.TopicPerformanceSummary, error) {
	return ras.cachedAnalyticsService.GetTopicPerformance(ctx, userID, topicID)
}

func (ras *RealtimeAnalyticsService) GetHeatmapData(ctx context.Context, userID string) ([]models.HeatmapData, error) {
	return ras.cachedAnalyticsService.GetHeatmapData(ctx, userID)
}

func (ras *RealtimeAnalyticsService) GetStudyPatterns(ctx context.Context, userID string) (*models.StudyPattern, error) {
	return ras.cachedAnalyticsService.GetStudyPatterns(ctx, userID)
}

func (ras *RealtimeAnalyticsService) GetSubjectComparisons(ctx context.Context, userID string) ([]models.SubjectComparison, error) {
	return ras.cachedAnalyticsService.GetSubjectComparisons(ctx, userID)
}

func (ras *RealtimeAnalyticsService) GetUserTestHistory(ctx context.Context, userID string, limit int) ([]models.UserTestHistory, error) {
	return ras.cachedAnalyticsService.GetUserTestHistory(ctx, userID, limit)
}

func (ras *RealtimeAnalyticsService) GetTimeFilteredTestHistory(ctx context.Context, userID string, timeFilter models.TimeFilter) ([]models.UserTestHistory, error) {
	return ras.cachedAnalyticsService.GetTimeFilteredTestHistory(ctx, userID, timeFilter)
}

func (ras *RealtimeAnalyticsService) GetTimeFilteredTestHistoryWithDateRange(ctx context.Context, userID string, timeFilter models.TimeFilter, dateRange *models.DateRange) ([]models.UserTestHistory, error) {
	return ras.cachedAnalyticsService.GetTimeFilteredTestHistoryWithDateRange(ctx, userID, timeFilter, dateRange)
}

func (ras *RealtimeAnalyticsService) GetUserInsights(ctx context.Context, userID string, limit int) ([]models.AnalyticsInsight, error) {
	return ras.cachedAnalyticsService.GetUserInsights(ctx, userID, limit)
}

func (ras *RealtimeAnalyticsService) GetWeakestTopics(ctx context.Context, userID string, limit int) ([]models.TopicPerformanceWithName, error) {
	return ras.cachedAnalyticsService.GetWeakestTopics(ctx, userID, limit)
}

func (ras *RealtimeAnalyticsService) GetTopicByID(ctx context.Context, topicID int) (*models.Topic, error) {
	return ras.cachedAnalyticsService.GetTopicByID(ctx, topicID)
}

func (ras *RealtimeAnalyticsService) InvalidateUserCache(userID string) {
	ras.cachedAnalyticsService.InvalidateUserCache(userID)
}

func (ras *RealtimeAnalyticsService) GetCacheStats() map[string]interface{} {
	stats := ras.cachedAnalyticsService.GetCacheStats()

	// Add WebSocket stats
	stats["websocket_connections"] = ras.websocketService.GetConnectionCount()
	stats["connected_users"] = len(ras.websocketService.GetConnectedUsers())

	return stats
}

func (ras *RealtimeAnalyticsService) GetCacheHealth() map[string]interface{} {
	health := ras.cachedAnalyticsService.GetCacheHealth()

	// Add WebSocket health info
	health["websocket_status"] = "active"
	health["websocket_connections"] = ras.websocketService.GetConnectionCount()

	return health
}
