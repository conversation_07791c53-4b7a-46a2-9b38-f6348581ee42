package services

import (
	"context"
	"testing"

	"test-spark-backend/internal/models"
	"test-spark-backend/internal/types"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockAIProvider implements the AIProvider interface for testing
type MockAIProvider struct {
	mock.Mock
}

func (m *MockAIProvider) GenerateQuestions(ctx context.Context, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	args := m.Called(ctx, genCtx)
	return args.Get(0).([]models.Question), args.Error(1)
}

func (m *MockAIProvider) GetProviderName() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockAIProvider) IsAvailable(ctx context.Context) bool {
	args := m.Called(ctx)
	return args.Bool(0)
}

func (m *MockAIProvider) GetCapabilities() types.ProviderCapabilities {
	args := m.Called()
	return args.Get(0).(types.ProviderCapabilities)
}

// MockAIProviderManager implements a simple provider manager for testing
type MockAIProviderManager struct {
	providers map[string]AIProvider
}

func NewMockAIProviderManager() *MockAIProviderManager {
	return &MockAIProviderManager{
		providers: make(map[string]AIProvider),
	}
}

func (m *MockAIProviderManager) RegisterProvider(name string, provider AIProvider) {
	m.providers[name] = provider
}

func (m *MockAIProviderManager) GetProvider(name string) AIProvider {
	return m.providers[name]
}

func (m *MockAIProviderManager) GetAvailableProviders() []string {
	var providers []string
	for name := range m.providers {
		providers = append(providers, name)
	}
	return providers
}

func (m *MockAIProviderManager) GenerateQuestions(ctx context.Context, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	// Simple fallback implementation for testing
	for _, provider := range m.providers {
		if provider.IsAvailable(ctx) {
			return provider.GenerateQuestions(ctx, genCtx)
		}
	}
	return nil, nil
}

func TestMixedGenerationService_CalculateProviderAllocations(t *testing.T) {
	tests := []struct {
		name           string
		config         *MixedGenerationConfig
		totalQuestions int
		expected       []ProviderAllocation
		expectError    bool
	}{
		{
			name: "basic allocation",
			config: &MixedGenerationConfig{
				ProviderRatios: map[string]float64{
					"cerebras": 0.6,
					"groq":     0.4,
				},
				MinQuestionsPerProvider: 1,
			},
			totalQuestions: 5,
			expected: []ProviderAllocation{
				{ProviderName: "cerebras", NumQuestions: 3},
				{ProviderName: "groq", NumQuestions: 2},
			},
		},
		{
			name: "equal split",
			config: &MixedGenerationConfig{
				ProviderRatios: map[string]float64{
					"cerebras": 0.5,
					"groq":     0.5,
				},
				MinQuestionsPerProvider: 1,
			},
			totalQuestions: 10,
			expected: []ProviderAllocation{
				{ProviderName: "cerebras", NumQuestions: 5},
				{ProviderName: "groq", NumQuestions: 5},
			},
		},
		{
			name: "minimum questions enforcement",
			config: &MixedGenerationConfig{
				ProviderRatios: map[string]float64{
					"cerebras": 0.8,
					"groq":     0.2,
				},
				MinQuestionsPerProvider: 1,
			},
			totalQuestions: 10,
			expected: []ProviderAllocation{
				{ProviderName: "cerebras", NumQuestions: 8},
				{ProviderName: "groq", NumQuestions: 2},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock providers
			mockProviderMgr := NewMockAIProviderManager()

			// Register mock providers for each provider in the config
			for providerName := range tt.config.ProviderRatios {
				mockProvider := &MockAIProvider{}
				mockProvider.On("IsAvailable", mock.Anything).Return(true)
				mockProviderMgr.RegisterProvider(providerName, mockProvider)
			}

			mgs := NewMixedGenerationService(mockProviderMgr, tt.config)

			allocations, err := mgs.calculateProviderAllocations(tt.totalQuestions)

			if tt.expectError {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Len(t, allocations, len(tt.expected))

			// Convert to map for easier comparison
			actualMap := make(map[string]int)
			for _, alloc := range allocations {
				actualMap[alloc.ProviderName] = alloc.NumQuestions
			}

			expectedMap := make(map[string]int)
			for _, alloc := range tt.expected {
				expectedMap[alloc.ProviderName] = alloc.NumQuestions
			}

			assert.Equal(t, expectedMap, actualMap)

			// Verify total questions match
			totalAllocated := 0
			for _, alloc := range allocations {
				totalAllocated += alloc.NumQuestions
			}
			assert.Equal(t, tt.totalQuestions, totalAllocated)
		})
	}
}

func TestMixedGenerationService_GenerateQuestionsMixed(t *testing.T) {
	// Create mock providers
	mockCerebras := &MockAIProvider{}
	mockGroq := &MockAIProvider{}

	// Set up mock expectations
	mockCerebras.On("IsAvailable", mock.Anything).Return(true)
	mockGroq.On("IsAvailable", mock.Anything).Return(true)

	// Mock question generation
	cerebrasQuestions := []models.Question{
		{ID: uuid.New(), TopicID: 1},
		{ID: uuid.New(), TopicID: 2},
		{ID: uuid.New(), TopicID: 3},
	}
	groqQuestions := []models.Question{
		{ID: uuid.New(), TopicID: 4},
		{ID: uuid.New(), TopicID: 5},
	}

	mockCerebras.On("GenerateQuestions", mock.Anything, mock.MatchedBy(func(ctx *QuestionGenerationContext) bool {
		return ctx.NumQuestions == 3
	})).Return(cerebrasQuestions, nil)

	mockGroq.On("GenerateQuestions", mock.Anything, mock.MatchedBy(func(ctx *QuestionGenerationContext) bool {
		return ctx.NumQuestions == 2
	})).Return(groqQuestions, nil)

	// Create provider manager and register providers
	mockProviderMgr := NewMockAIProviderManager()
	mockProviderMgr.RegisterProvider("cerebras", mockCerebras)
	mockProviderMgr.RegisterProvider("groq", mockGroq)

	// Create mixed generation service
	config := &MixedGenerationConfig{
		ProviderRatios: map[string]float64{
			"cerebras": 0.6,
			"groq":     0.4,
		},
		MinQuestionsPerProvider: 1,
		EnableRandomization:     false, // Disable for predictable testing
		EnableFallback:          true,
	}

	mgs := NewMixedGenerationService(mockProviderMgr, config)

	// Test generation
	genCtx := &QuestionGenerationContext{
		TopicIDs:     []int{1, 2},
		TopicNames:   []string{"Math", "Science"},
		Difficulties: []models.DifficultyLevel{models.DifficultyEasy, models.DifficultyMedium},
		NumQuestions: 5,
		SubjectName:  "Mathematics",
		ExamName:     "Test Exam",
	}

	questions, err := mgs.GenerateQuestionsMixed(context.Background(), genCtx)

	// Assertions
	assert.NoError(t, err)
	assert.Len(t, questions, 5)

	// Verify provider attribution
	cerebrasCount := 0
	groqCount := 0
	for _, q := range questions {
		if q.AuthorAIModel != nil {
			if *q.AuthorAIModel == "cerebras-mixed" {
				cerebrasCount++
			} else if *q.AuthorAIModel == "groq-mixed" {
				groqCount++
			}
		}
	}

	assert.Equal(t, 3, cerebrasCount, "Should have 3 questions from Cerebras")
	assert.Equal(t, 2, groqCount, "Should have 2 questions from Groq")

	// Verify all mocks were called as expected
	mockCerebras.AssertExpectations(t)
	mockGroq.AssertExpectations(t)
}

func TestMixedGenerationService_ContainsProvider(t *testing.T) {
	tests := []struct {
		name     string
		slice    []string
		item     string
		expected bool
	}{
		{
			name:     "item exists",
			slice:    []string{"cerebras", "groq", "openai"},
			item:     "groq",
			expected: true,
		},
		{
			name:     "item does not exist",
			slice:    []string{"cerebras", "groq", "openai"},
			item:     "gemini",
			expected: false,
		},
		{
			name:     "empty slice",
			slice:    []string{},
			item:     "groq",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := containsProvider(tt.slice, tt.item)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDefaultMixedGenerationConfig(t *testing.T) {
	config := DefaultMixedGenerationConfig()

	assert.NotNil(t, config)
	assert.Equal(t, 0.6, config.ProviderRatios["cerebras"])
	assert.Equal(t, 0.4, config.ProviderRatios["groq"])
	assert.Equal(t, 1, config.MinQuestionsPerProvider)
	assert.True(t, config.EnableRandomization)
	assert.True(t, config.EnableFallback)

	// Verify ratios sum to 1.0
	sum := 0.0
	for _, ratio := range config.ProviderRatios {
		sum += ratio
	}
	assert.InDelta(t, 1.0, sum, 0.001)
}
