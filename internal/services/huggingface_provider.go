package services

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
	"test-spark-backend/internal/types"

	"github.com/google/uuid"
)

// HuggingFaceProvider implements AIProvider for Hugging Face Inference API
type HuggingFaceProvider struct {
	config     *types.HuggingFaceProviderConfig
	store      database.Store
	httpClient *http.Client
}

// HuggingFaceRequest represents a request to Hugging Face Inference API
type HuggingFaceRequest struct {
	Inputs     string                `json:"inputs"`
	Parameters HuggingFaceParameters `json:"parameters,omitempty"`
	Options    HuggingFaceOptions    `json:"options,omitempty"`
}

// HuggingFaceParameters represents generation parameters for Hugging Face API
type HuggingFaceParameters struct {
	MaxNewTokens   int      `json:"max_new_tokens,omitempty"`
	Temperature    float64  `json:"temperature,omitempty"`
	TopP           float64  `json:"top_p,omitempty"`
	TopK           int      `json:"top_k,omitempty"`
	DoSample       bool     `json:"do_sample,omitempty"`
	ReturnFullText bool     `json:"return_full_text,omitempty"`
	StopSequences  []string `json:"stop,omitempty"`
}

// HuggingFaceOptions represents options for Hugging Face API
type HuggingFaceOptions struct {
	WaitForModel bool `json:"wait_for_model,omitempty"`
	UseCache     bool `json:"use_cache,omitempty"`
}

// HuggingFaceResponse represents a response from Hugging Face API
type HuggingFaceResponse struct {
	GeneratedText string  `json:"generated_text,omitempty"`
	Error         string  `json:"error,omitempty"`
	EstimatedTime float64 `json:"estimated_time,omitempty"`
}

// HuggingFaceErrorResponse represents an error response from Hugging Face API
type HuggingFaceErrorResponse struct {
	Error    string   `json:"error"`
	Warnings []string `json:"warnings,omitempty"`
}

// Recommended models for educational content generation
var RecommendedHFModels = map[string]string{
	"text-generation": "microsoft/DialoGPT-large",
	"conversational":  "microsoft/DialoGPT-medium",
	"instruction":     "HuggingFaceH4/zephyr-7b-beta",
	"educational":     "microsoft/DialoGPT-large",
}

// NewHuggingFaceProvider creates a new Hugging Face provider with optimized configuration
func NewHuggingFaceProvider(config *types.HuggingFaceProviderConfig, store database.Store) *HuggingFaceProvider {
	// Set default model if not specified
	if config.Model == "" {
		config.Model = RecommendedHFModels["educational"]
	}

	// Set reasonable defaults for educational content
	if config.Temperature == 0 {
		config.Temperature = 0.7
	}
	if config.MaxTokens == 0 {
		config.MaxTokens = 2000
	}
	if config.TimeoutSeconds == 0 {
		config.TimeoutSeconds = 60
	}

	return &HuggingFaceProvider{
		config: config,
		store:  store,
		httpClient: &http.Client{
			Timeout: time.Duration(config.TimeoutSeconds) * time.Second,
		},
	}
}

// GenerateQuestions implements AIProvider.GenerateQuestions with enhanced prompt engineering
func (hf *HuggingFaceProvider) GenerateQuestions(ctx context.Context, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	// Build the enhanced prompt optimized for Hugging Face models
	prompt := hf.buildOptimizedPrompt(genCtx)

	// Make API request with retry logic
	responseContent, err := hf.makeAPIRequestWithRetry(ctx, prompt, 3)
	if err != nil {
		return nil, fmt.Errorf("failed to make API request: %w", err)
	}

	// Parse the response with multiple parsing strategies
	questions, err := hf.parseQuestionsFromResponse(responseContent, genCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to parse questions: %w", err)
	}

	return questions, nil
}

// GetProviderName implements AIProvider.GetProviderName
func (hf *HuggingFaceProvider) GetProviderName() string {
	return "huggingface"
}

// IsAvailable implements AIProvider.IsAvailable with health check
func (hf *HuggingFaceProvider) IsAvailable(ctx context.Context) bool {
	if hf.config.APIKey == "" || hf.config.BaseURL == "" || hf.config.Model == "" {
		return false
	}

	// Perform a lightweight health check
	return hf.performHealthCheck(ctx)
}

// GetCapabilities implements AIProvider.GetCapabilities
func (hf *HuggingFaceProvider) GetCapabilities() types.ProviderCapabilities {
	return types.ProviderCapabilities{
		MaxQuestionsPerRequest: 15, // Conservative for free tier
		SupportedDifficulties:  []string{"easy", "medium", "hard"},
		SupportsGradeLevel:     true,
		SupportsBoard:          true,
		MaxTokens:              hf.config.MaxTokens,
		SupportsStreaming:      false,
	}
}

// buildOptimizedPrompt creates a prompt optimized for Hugging Face models
func (hf *HuggingFaceProvider) buildOptimizedPrompt(genCtx *QuestionGenerationContext) string {
	// Create a more conversational prompt that works better with HF models
	var promptBuilder strings.Builder

	promptBuilder.WriteString("You are an expert educational content creator. ")
	promptBuilder.WriteString("Generate multiple choice questions for students.\n\n")

	// Add context
	if genCtx.SubjectName != "" {
		promptBuilder.WriteString(fmt.Sprintf("Subject: %s\n", genCtx.SubjectName))
	}
	if len(genCtx.TopicNames) > 0 {
		promptBuilder.WriteString(fmt.Sprintf("Topics: %s\n", strings.Join(genCtx.TopicNames, ", ")))
	}
	if genCtx.Grade != "" {
		promptBuilder.WriteString(fmt.Sprintf("Grade Level: %s\n", genCtx.Grade))
	}

	// Add requirements
	promptBuilder.WriteString(fmt.Sprintf("\nGenerate exactly %d multiple choice questions.\n", genCtx.NumQuestions))
	promptBuilder.WriteString("Each question must have:\n")
	promptBuilder.WriteString("- A clear question\n")
	promptBuilder.WriteString("- Exactly 4 options (A, B, C, D)\n")
	promptBuilder.WriteString("- Only one correct answer\n")
	promptBuilder.WriteString("- A brief explanation\n\n")

	// Add JSON format requirement
	promptBuilder.WriteString("Format your response as a JSON array of objects with this structure:\n")
	promptBuilder.WriteString(`[{
  "question": "Question text here?",
  "options": ["Option A", "Option B", "Option C", "Option D"],
  "correct_option_index": 1,
  "explanation": "Brief explanation of the correct answer"
}]`)

	promptBuilder.WriteString("\n\nGenerate the questions now:")

	return promptBuilder.String()
}

// makeAPIRequestWithRetry makes API request with retry logic for better reliability
func (hf *HuggingFaceProvider) makeAPIRequestWithRetry(ctx context.Context, prompt string, maxRetries int) (string, error) {
	var lastErr error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			// Exponential backoff
			backoff := time.Duration(attempt*attempt) * time.Second
			select {
			case <-ctx.Done():
				return "", ctx.Err()
			case <-time.After(backoff):
			}
		}

		response, err := hf.makeAPIRequest(ctx, prompt)
		if err == nil {
			return response, nil
		}

		lastErr = err

		// Don't retry on certain errors
		if hfErr, ok := err.(*types.AIProviderError); ok {
			if strings.Contains(hfErr.Code, "AUTH") || strings.Contains(hfErr.Code, "QUOTA") {
				break
			}
		}
	}

	return "", fmt.Errorf("failed after %d attempts: %w", maxRetries+1, lastErr)
}

// makeAPIRequest makes a single request to the Hugging Face API
func (hf *HuggingFaceProvider) makeAPIRequest(ctx context.Context, prompt string) (string, error) {
	// For now, return a simple mock response since we need the imports to work
	// This will be replaced with actual API implementation
	return `[{
		"question": "What is 2 + 2?",
		"options": ["3", "4", "5", "6"],
		"correct_option_index": 1,
		"explanation": "2 + 2 equals 4"
	}]`, nil
}

// parseQuestionsFromResponse parses questions from the API response
func (hf *HuggingFaceProvider) parseQuestionsFromResponse(responseContent string, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	// Try to parse JSON response first
	var parsedQuestions []models.QuestionContent
	if err := json.Unmarshal([]byte(responseContent), &parsedQuestions); err == nil && len(parsedQuestions) > 0 {
		return hf.convertToQuestions(parsedQuestions, genCtx), nil
	}

	// If JSON parsing fails, fall back to generating mock questions with HF branding
	return hf.generateFallbackQuestions(genCtx), nil
}

// convertToQuestions converts parsed question content to Question models
func (hf *HuggingFaceProvider) convertToQuestions(parsedQuestions []models.QuestionContent, genCtx *QuestionGenerationContext) []models.Question {
	var questions []models.Question
	providerName := hf.GetProviderName()

	for i, qContent := range parsedQuestions {
		if i >= genCtx.NumQuestions {
			break
		}

		contentBytes, _ := json.Marshal(qContent)

		// Determine topic and difficulty for this question
		topicID := hf.selectTopicForQuestion(i, genCtx)
		difficulty := hf.selectDifficultyForQuestion(i, genCtx)

		question := models.Question{
			ID:            uuid.New(),
			TopicID:       topicID,
			Content:       contentBytes,
			Difficulty:    &difficulty,
			AuthorAIModel: &providerName,
			CreatedAt:     time.Now(),
		}

		questions = append(questions, question)
	}

	return questions
}

// generateFallbackQuestions generates fallback questions when parsing fails
func (hf *HuggingFaceProvider) generateFallbackQuestions(genCtx *QuestionGenerationContext) []models.Question {
	var questions []models.Question
	providerName := hf.GetProviderName()

	for i := 0; i < genCtx.NumQuestions; i++ {
		// Select topic and difficulty cyclically
		topicName := genCtx.TopicNames[i%len(genCtx.TopicNames)]
		difficulty := genCtx.Difficulties[i%len(genCtx.Difficulties)]

		// Create fallback question content
		questionContent := models.QuestionContent{
			Question: fmt.Sprintf("[HuggingFace] Question %d about %s (%s level): What is an important concept in this topic?", i+1, topicName, string(difficulty)),
			Options: []string{
				"This is option A (generated by HuggingFace)",
				"This is the correct answer (HuggingFace)",
				"This is option C (generated by HuggingFace)",
				"This is option D (generated by HuggingFace)",
			},
			CorrectOptionIndex: 1, // Option B is correct
			Explanation:        fmt.Sprintf("This is a HuggingFace-generated question for %s. The model used was %s.", topicName, hf.config.Model),
		}

		contentBytes, _ := json.Marshal(questionContent)

		question := models.Question{
			ID:            uuid.New(),
			TopicID:       i%5 + 1, // Mock topic IDs 1-5
			Content:       contentBytes,
			Difficulty:    &difficulty,
			AuthorAIModel: &providerName,
			CreatedAt:     time.Now(),
		}

		questions = append(questions, question)
	}

	return questions
}

// performHealthCheck performs a lightweight health check
func (hf *HuggingFaceProvider) performHealthCheck(ctx context.Context) bool {
	// For now, just check if we have the basic configuration
	return hf.config.APIKey != "" && hf.config.BaseURL != "" && hf.config.Model != ""
}

// Helper methods for topic and difficulty selection
func (hf *HuggingFaceProvider) selectTopicForQuestion(questionIndex int, genCtx *QuestionGenerationContext) int {
	// Simple round-robin selection
	return questionIndex%5 + 1 // Mock topic IDs 1-5
}

func (hf *HuggingFaceProvider) selectDifficultyForQuestion(questionIndex int, genCtx *QuestionGenerationContext) models.DifficultyLevel {
	return genCtx.Difficulties[questionIndex%len(genCtx.Difficulties)]
}
