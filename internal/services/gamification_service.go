package services

import (
	"context"
	"fmt"
	"math"
	"time"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"

	"github.com/google/uuid"
)

// GamificationService handles all gamification logic
type GamificationService struct {
	store database.Store
}

// NewGamificationService creates a new gamification service
func NewGamificationService(store database.Store) *GamificationService {
	return &GamificationService{
		store: store,
	}
}

// AwardExperience awards experience points to a user and handles level ups
func (gs *GamificationService) AwardExperience(ctx context.Context, userID uuid.UUID, experience int, source string) (*models.GamificationProfile, error) {
	profile, err := gs.GetOrCreateProfile(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get profile: %w", err)
	}

	// Apply experience multipliers from streaks and bonuses
	multiplier := gs.calculateExperienceMultiplier(profile)
	finalExperience := int(float64(experience) * multiplier)

	// Update experience
	profile.Experience += finalExperience

	// Check for level up
	oldLevel := profile.Level
	newLevel := gs.calculateLevel(profile.Experience)

	if newLevel > oldLevel {
		profile.Level = newLevel
		profile.AvailablePoints += (newLevel - oldLevel) * 5 // 5 skill points per level

		// Award level up rewards
		err = gs.handleLevelUp(ctx, userID, oldLevel, newLevel)
		if err != nil {
			return nil, fmt.Errorf("failed to handle level up: %w", err)
		}
	}

	profile.ExperienceToNext = gs.calculateExperienceToNextLevel(profile.Experience)
	profile.UpdatedAt = time.Now()

	// Update profile in database
	err = gs.updateProfile(ctx, profile)
	if err != nil {
		return nil, fmt.Errorf("failed to update profile: %w", err)
	}

	return profile, nil
}

// CheckAchievements checks and awards any newly completed achievements
func (gs *GamificationService) CheckAchievements(ctx context.Context, userID uuid.UUID, action string, data map[string]interface{}) ([]models.Achievement, error) {
	profile, err := gs.GetOrCreateProfile(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get profile: %w", err)
	}

	// Get all available achievements
	availableAchievements := gs.getAvailableAchievements()
	var newlyCompleted []models.Achievement

	for _, achievement := range availableAchievements {
		// Skip if already completed
		if gs.isAchievementCompleted(profile, achievement.ID) {
			continue
		}

		// Check if achievement criteria are met
		if gs.checkAchievementCriteria(ctx, userID, achievement, action, data) {
			// Mark as completed
			achievement.IsCompleted = true
			achievement.Progress = achievement.MaxProgress
			now := time.Now()
			achievement.CompletedAt = &now

			// Award rewards
			err = gs.awardAchievementRewards(ctx, userID, achievement.Rewards)
			if err != nil {
				continue // Log error but don't fail the whole process
			}

			newlyCompleted = append(newlyCompleted, achievement)

			// Send notification
			err = gs.sendAchievementNotification(ctx, userID, achievement)
			if err != nil {
				// Log error but continue
			}
		}
	}

	// Update profile with new achievements
	if len(newlyCompleted) > 0 {
		profile.Achievements = append(profile.Achievements, newlyCompleted...)
		err = gs.updateProfile(ctx, profile)
		if err != nil {
			return newlyCompleted, fmt.Errorf("failed to update profile: %w", err)
		}
	}

	return newlyCompleted, nil
}

// UpdateStreak updates a user's learning streak
func (gs *GamificationService) UpdateStreak(ctx context.Context, userID uuid.UUID, streakType models.StreakType) (*models.Streak, error) {
	profile, err := gs.GetOrCreateProfile(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get profile: %w", err)
	}

	// Find or create streak
	var streak *models.Streak
	for i := range profile.Streaks {
		if profile.Streaks[i].Type == streakType {
			streak = &profile.Streaks[i]
			break
		}
	}

	if streak == nil {
		// Create new streak
		newStreak := models.Streak{
			Type:        streakType,
			Current:     0,
			Longest:     0,
			LastUpdated: time.Now(),
			IsActive:    true,
			Multiplier:  1.0,
		}
		profile.Streaks = append(profile.Streaks, newStreak)
		streak = &profile.Streaks[len(profile.Streaks)-1]
	}

	// Check if streak should continue or reset
	now := time.Now()
	daysSinceLastUpdate := int(now.Sub(streak.LastUpdated).Hours() / 24)

	switch streakType {
	case models.StreakTypeDaily:
		if daysSinceLastUpdate == 1 {
			// Continue streak
			streak.Current++
			streak.IsActive = true
		} else if daysSinceLastUpdate > 1 {
			// Reset streak
			streak.Current = 1
			streak.IsActive = true
		}
		// Same day, no change needed
	}

	// Update longest streak
	if streak.Current > streak.Longest {
		streak.Longest = streak.Current
	}

	// Calculate multiplier based on streak
	streak.Multiplier = gs.calculateStreakMultiplier(streak.Current)
	streak.LastUpdated = now

	// Update profile
	err = gs.updateProfile(ctx, profile)
	if err != nil {
		return streak, fmt.Errorf("failed to update profile: %w", err)
	}

	return streak, nil
}

// GetLeaderboard retrieves a leaderboard
func (gs *GamificationService) GetLeaderboard(ctx context.Context, leaderboardType models.LeaderboardType, period models.LeaderboardPeriod, limit int) (*models.Leaderboard, error) {
	// This would query the database for leaderboard data
	// For now, return a mock leaderboard

	leaderboard := &models.Leaderboard{
		ID:        fmt.Sprintf("%s_%s", leaderboardType, period),
		Name:      fmt.Sprintf("%s Leaderboard", leaderboardType),
		Type:      leaderboardType,
		Period:    period,
		UpdatedAt: time.Now(),
		StartDate: gs.getPeriodStartDate(period),
	}

	// Mock entries - in production, this would query actual user data
	entries := []models.LeaderboardEntry{
		{
			UserID:      uuid.New(),
			Username:    "student1",
			DisplayName: "Top Performer",
			Rank:        1,
			Score:       1250.5,
			Change:      2,
		},
		{
			UserID:      uuid.New(),
			Username:    "student2",
			DisplayName: "Quick Learner",
			Rank:        2,
			Score:       1180.0,
			Change:      -1,
		},
	}

	leaderboard.Entries = entries
	return leaderboard, nil
}

// CreateChallenge creates a new challenge
func (gs *GamificationService) CreateChallenge(ctx context.Context, challenge *models.Challenge) error {
	challenge.ID = uuid.New().String()
	challenge.IsActive = true
	challenge.Participants = 0

	// Store challenge in database
	// Implementation would go here

	return nil
}

// JoinChallenge allows a user to join a challenge
func (gs *GamificationService) JoinChallenge(ctx context.Context, userID uuid.UUID, challengeID string) (*models.UserChallenge, error) {
	userChallenge := &models.UserChallenge{
		UserID:      userID,
		ChallengeID: challengeID,
		Progress:    0,
		IsCompleted: false,
		JoinedAt:    time.Now(),
	}

	// Store in database
	// Implementation would go here

	return userChallenge, nil
}

// Helper methods

func (gs *GamificationService) GetOrCreateProfile(ctx context.Context, userID uuid.UUID) (*models.GamificationProfile, error) {
	// Try to get existing profile
	// If not found, create new one
	profile := &models.GamificationProfile{
		UserID:           userID,
		Level:            1,
		Experience:       0,
		ExperienceToNext: 100,
		TotalPoints:      0,
		AvailablePoints:  0,
		Rank:             "Novice",
		Title:            "Beginner",
		Badges:           []models.Badge{},
		Achievements:     []models.Achievement{},
		Streaks:          []models.Streak{},
		Statistics:       models.UserStatistics{},
		Preferences: models.GamificationPreferences{
			ShowNotifications: true,
			ShowLeaderboards:  true,
			ShowProgress:      true,
			PrivacyLevel:      "public",
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return profile, nil
}

func (gs *GamificationService) calculateLevel(experience int) int {
	// Exponential level progression: level = floor(sqrt(experience / 100))
	return int(math.Floor(math.Sqrt(float64(experience)/100))) + 1
}

func (gs *GamificationService) calculateExperienceToNextLevel(experience int) int {
	currentLevel := gs.calculateLevel(experience)
	nextLevelExp := (currentLevel * currentLevel) * 100
	return nextLevelExp - experience
}

func (gs *GamificationService) calculateExperienceMultiplier(profile *models.GamificationProfile) float64 {
	multiplier := 1.0

	// Add streak multipliers
	for _, streak := range profile.Streaks {
		if streak.IsActive {
			multiplier += (streak.Multiplier - 1.0)
		}
	}

	return multiplier
}

func (gs *GamificationService) calculateStreakMultiplier(streakCount int) float64 {
	// Diminishing returns: 1.0 + (streak * 0.1) with max of 2.0
	multiplier := 1.0 + (float64(streakCount) * 0.1)
	if multiplier > 2.0 {
		multiplier = 2.0
	}
	return multiplier
}

func (gs *GamificationService) handleLevelUp(ctx context.Context, userID uuid.UUID, oldLevel, newLevel int) error {
	// Award level up rewards
	// Send notification
	// Check for level-based achievements
	return nil
}

func (gs *GamificationService) updateProfile(ctx context.Context, profile *models.GamificationProfile) error {
	// Update profile in database
	return nil
}

func (gs *GamificationService) getAvailableAchievements() []models.Achievement {
	// Return predefined achievements
	return []models.Achievement{
		{
			ID:          "first_question",
			Name:        "First Steps",
			Description: "Answer your first question",
			Type:        models.AchievementTypeProgress,
			Category:    "beginner",
			MaxProgress: 1,
			Difficulty:  1,
		},
		{
			ID:          "perfect_score",
			Name:        "Perfectionist",
			Description: "Get a perfect score on a test",
			Type:        models.AchievementTypeMastery,
			Category:    "performance",
			MaxProgress: 1,
			Difficulty:  3,
		},
	}
}

func (gs *GamificationService) isAchievementCompleted(profile *models.GamificationProfile, achievementID string) bool {
	for _, achievement := range profile.Achievements {
		if achievement.ID == achievementID && achievement.IsCompleted {
			return true
		}
	}
	return false
}

func (gs *GamificationService) checkAchievementCriteria(ctx context.Context, userID uuid.UUID, achievement models.Achievement, action string, data map[string]interface{}) bool {
	// Implementation would check specific criteria based on achievement type
	// This is a simplified version
	switch achievement.ID {
	case "first_question":
		return action == "question_answered"
	case "perfect_score":
		if action == "test_completed" {
			if score, ok := data["score"].(float64); ok {
				return score == 100.0
			}
		}
	}
	return false
}

func (gs *GamificationService) awardAchievementRewards(ctx context.Context, userID uuid.UUID, rewards models.AchievementRewards) error {
	// Award experience, points, badges, etc.
	if rewards.Experience > 0 {
		_, err := gs.AwardExperience(ctx, userID, rewards.Experience, "achievement")
		if err != nil {
			return err
		}
	}
	return nil
}

func (gs *GamificationService) sendAchievementNotification(ctx context.Context, userID uuid.UUID, achievement models.Achievement) error {
	// Send notification about achievement completion
	return nil
}

func (gs *GamificationService) getPeriodStartDate(period models.LeaderboardPeriod) time.Time {
	now := time.Now()
	switch period {
	case models.LeaderboardPeriodDaily:
		return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	case models.LeaderboardPeriodWeekly:
		weekday := int(now.Weekday())
		return now.AddDate(0, 0, -weekday)
	case models.LeaderboardPeriodMonthly:
		return time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	default:
		return time.Time{}
	}
}
