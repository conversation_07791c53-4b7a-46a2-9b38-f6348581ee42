package services

import (
	"bytes"
	"context"
	"fmt"
	"html/template"
	"os"
	"path/filepath"
	"time"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"

	"github.com/google/uuid"
	"github.com/jung-kurt/gofpdf"
)

// ReportService handles report generation
type ReportService struct {
	store            database.Store
	analyticsService *AnalyticsService
	templateDir      string
	outputDir        string
}

// NewReportService creates a new report service
func NewReportService(store database.Store, analyticsService *AnalyticsService) *ReportService {
	return &ReportService{
		store:            store,
		analyticsService: analyticsService,
		templateDir:      "templates/reports",
		outputDir:        "generated_reports",
	}
}

// GenerateReport generates a report based on the request
func (rs *ReportService) GenerateReport(ctx context.Context, req *models.ReportRequest) (*models.ReportResponse, error) {
	// Validate request
	if err := rs.validateReportRequest(req); err != nil {
		return nil, fmt.Errorf("invalid report request: %w", err)
	}

	// Gather report data
	reportData, err := rs.gatherReportData(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to gather report data: %w", err)
	}

	// Create report ID first
	reportID := uuid.New()

	// Generate report based on format
	var reportPath string
	var size int64

	switch req.Format {
	case models.ReportFormatHTML:
		reportPath, size, err = rs.generateHTMLReport(reportData, reportID.String())
	case models.ReportFormatPDF:
		reportPath, size, err = rs.generatePDFReport(reportData, reportID.String())
	default:
		return nil, fmt.Errorf("unsupported report format: %s", req.Format)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to generate %s report: %w", req.Format, err)
	}

	// Create response
	response := &models.ReportResponse{
		ReportID:    reportID,
		DownloadURL: fmt.Sprintf("/api/v1/reports/download/%s", reportID),
		Format:      req.Format,
		Size:        size,
		GeneratedAt: time.Now(),
		ExpiresAt:   time.Now().Add(24 * time.Hour), // Reports expire after 24 hours
	}

	// Store report path for future reference (in production, you'd store this in database)
	_ = reportPath

	// Store report metadata (you might want to implement this in database)
	// For now, we'll use the file system

	return response, nil
}

// GatherReportData collects all necessary data for the report (exported for handler)
func (rs *ReportService) GatherReportData(ctx context.Context, req *models.ReportRequest) (*models.ReportData, error) {
	return rs.gatherReportData(ctx, req)
}

// gatherReportData collects all necessary data for the report
func (rs *ReportService) gatherReportData(ctx context.Context, req *models.ReportRequest) (*models.ReportData, error) {
	data := &models.ReportData{}

	// Get user information from database
	userInfo, err := rs.getUserInfo(ctx, req.UserID)
	if err != nil {
		// Fallback to basic info if user lookup fails
		userInfo = models.UserReportInfo{
			Name:        "Student User",
			Email:       "<EMAIL>",
			GeneratedAt: time.Now(),
		}
	}
	data.UserInfo = userInfo

	// Set report metadata
	data.ReportMetadata = models.ReportMetadata{
		Title:       rs.generateReportTitle(req),
		Type:        req.ReportType,
		Format:      req.Format,
		TimeFilter:  req.TimeFilter,
		DateRange:   req.DateRange,
		GeneratedAt: time.Now(),
		Version:     "1.0",
	}

	// Get dashboard data
	dashboard, err := rs.store.GetDashboardSummary(ctx, req.UserID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get dashboard data: %w", err)
	}
	data.Dashboard = dashboard

	// Get advanced analytics
	analytics, err := rs.analyticsService.GetAdvancedAnalytics(ctx, req.UserID, req.TimeFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to get advanced analytics: %w", err)
	}
	data.Analytics = analytics

	// Get performance trends
	trends, err := rs.store.GetPerformanceTrends(ctx, req.UserID.String(), req.TimeFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to get performance trends: %w", err)
	}
	data.Trends = trends

	// Get insights
	insights, err := rs.analyticsService.generateInsights(ctx, req.UserID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get insights: %w", err)
	}
	data.Insights = insights

	// Get study patterns
	studyPatterns, err := rs.store.GetStudyPatterns(ctx, req.UserID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get study patterns: %w", err)
	}
	data.StudyPatterns = studyPatterns

	// Get recommendations
	recommendations, err := rs.analyticsService.GenerateRecommendations(ctx, req.UserID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get recommendations: %w", err)
	}
	data.Recommendations = recommendations

	// Get heatmap data
	heatmapData, err := rs.store.GetHeatmapData(ctx, req.UserID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get heatmap data: %w", err)
	}
	data.HeatmapData = heatmapData

	// Process topic analysis
	data.TopicAnalysis = rs.processTopicAnalysis(dashboard.TopicPerformance)

	// Process test history
	data.TestHistory = rs.processTestHistory(dashboard.RecentTests)

	// Calculate performance metrics
	data.PerformanceMetrics = rs.calculatePerformanceMetrics(dashboard, analytics)

	// Generate charts (placeholder - you'll need to implement chart generation)
	data.Charts = models.ReportCharts{
		PerformanceTrend:   "placeholder_chart_data",
		SubjectRadar:       "placeholder_chart_data",
		TopicHeatmap:       "placeholder_chart_data",
		ProgressChart:      "placeholder_chart_data",
		DifficultyAnalysis: "placeholder_chart_data",
	}

	return data, nil
}

// processTopicAnalysis converts topic performance data to report format
func (rs *ReportService) processTopicAnalysis(topics []models.TopicPerformanceWithName) []models.TopicReportData {
	var analysis []models.TopicReportData

	for _, topic := range topics {
		analysis = append(analysis, models.TopicReportData{
			TopicID:            topic.TopicID,
			TopicName:          topic.TopicName,
			SubjectName:        topic.SubjectName,
			ProficiencyScore:   topic.ProficiencyScore,
			QuestionsAttempted: topic.TotalAttempted,
			CorrectAnswers:     topic.TotalCorrect,
			AverageTime:        0, // TODO: Add average time field to TopicPerformanceWithName
			Difficulty:         rs.getDifficultyLevel(topic.ProficiencyScore),
			Improvement:        0, // TODO: Calculate improvement
			Recommendations:    rs.generateTopicRecommendations(topic),
		})
	}

	return analysis
}

// processTestHistory converts test history to report format
func (rs *ReportService) processTestHistory(tests []models.UserTestHistory) []models.TestReportData {
	var history []models.TestReportData

	for _, test := range tests {
		// Handle nullable fields safely
		score := 0.0
		if test.Score != nil {
			score = *test.Score
		}

		totalQuestions := 0
		if test.TotalQuestions != nil {
			totalQuestions = *test.TotalQuestions
		}

		correctAnswers := 0
		if test.CorrectAnswers != nil {
			correctAnswers = *test.CorrectAnswers
		}

		timeTaken := 0
		if test.TimeTakenSeconds != nil {
			timeTaken = *test.TimeTakenSeconds
		}

		subject := "Unknown"
		if test.SubjectName != nil {
			subject = *test.SubjectName
		}

		history = append(history, models.TestReportData{
			TestID:          test.TestID,
			TestDate:        test.CreatedAt,
			Subject:         subject,
			Difficulty:      "Medium", // TODO: Add difficulty to UserTestHistory
			Score:           score,
			TotalQuestions:  totalQuestions,
			CorrectAnswers:  correctAnswers,
			TimeTaken:       timeTaken,
			TopicsCount:     1, // TODO: Add topics count to UserTestHistory
			PerformanceRank: rs.getPerformanceRank(score),
		})
	}

	return history
}

// calculatePerformanceMetrics calculates comprehensive performance metrics
func (rs *ReportService) calculatePerformanceMetrics(dashboard *models.DashboardSummary, analytics *models.AdvancedAnalytics) models.PerformanceMetrics {
	// Handle nullable string fields
	bestSubject := "N/A"
	if dashboard.StrongestSubject != nil {
		bestSubject = *dashboard.StrongestSubject
	}

	weakestSubject := "N/A"
	if dashboard.WeakestSubject != nil {
		weakestSubject = *dashboard.WeakestSubject
	}

	return models.PerformanceMetrics{
		OverallScore:       dashboard.AverageScore,
		ImprovementRate:    analytics.OverallImprovement,
		ConsistencyScore:   75.0, // TODO: Calculate actual consistency
		StrengthsCount:     rs.countStrengths(dashboard.TopicPerformance),
		WeaknessesCount:    rs.countWeaknesses(dashboard.TopicPerformance),
		TestsCompleted:     dashboard.TotalTestsTaken,
		StudyTimeHours:     0.0,  // TODO: Add study time field to DashboardSummary
		AverageSessionTime: 45.0, // TODO: Calculate from actual data
		BestSubject:        bestSubject,
		WeakestSubject:     weakestSubject,
		RecommendedFocus:   []string{"Practice weak topics", "Maintain strong areas"},
	}
}

// Helper functions
func (rs *ReportService) validateReportRequest(req *models.ReportRequest) error {
	if req.UserID == uuid.Nil {
		return fmt.Errorf("user ID is required")
	}
	if req.ReportType == "" {
		return fmt.Errorf("report type is required")
	}
	if req.Format == "" {
		return fmt.Errorf("report format is required")
	}
	return nil
}

func (rs *ReportService) generateReportTitle(req *models.ReportRequest) string {
	if req.CustomTitle != "" {
		return req.CustomTitle
	}

	switch req.ReportType {
	case models.ReportTypePerformance:
		return "Performance Analysis Report"
	case models.ReportTypeProgress:
		return "Learning Progress Report"
	case models.ReportTypeDetailed:
		return "Comprehensive Learning Analytics Report"
	case models.ReportTypeComparative:
		return "Comparative Performance Report"
	default:
		return "Learning Analytics Report"
	}
}

func (rs *ReportService) getDifficultyLevel(score float64) string {
	if score >= 80 {
		return "Easy"
	} else if score >= 60 {
		return "Medium"
	}
	return "Hard"
}

func (rs *ReportService) getPerformanceRank(score float64) string {
	if score >= 90 {
		return "Excellent"
	} else if score >= 80 {
		return "Good"
	} else if score >= 70 {
		return "Average"
	} else if score >= 60 {
		return "Below Average"
	}
	return "Needs Improvement"
}

func (rs *ReportService) countStrengths(topics []models.TopicPerformanceWithName) int {
	count := 0
	for _, topic := range topics {
		if topic.ProficiencyScore > 70 {
			count++
		}
	}
	return count
}

func (rs *ReportService) countWeaknesses(topics []models.TopicPerformanceWithName) int {
	count := 0
	for _, topic := range topics {
		if topic.ProficiencyScore <= 70 {
			count++
		}
	}
	return count
}

func (rs *ReportService) generateTopicRecommendations(topic models.TopicPerformanceWithName) []string {
	var recommendations []string

	if topic.ProficiencyScore < 50 {
		recommendations = append(recommendations, "Focus on fundamental concepts")
		recommendations = append(recommendations, "Practice basic problems daily")
	} else if topic.ProficiencyScore < 70 {
		recommendations = append(recommendations, "Review key concepts")
		recommendations = append(recommendations, "Practice medium difficulty problems")
	} else {
		recommendations = append(recommendations, "Maintain current level")
		recommendations = append(recommendations, "Try advanced problems")
	}

	return recommendations
}

// generateHTMLReport generates an HTML report
func (rs *ReportService) generateHTMLReport(data *models.ReportData, reportID string) (string, int64, error) {
	// Ensure output directory exists
	if err := os.MkdirAll(rs.outputDir, 0755); err != nil {
		return "", 0, fmt.Errorf("failed to create output directory: %w", err)
	}

	// Load HTML template
	templatePath := filepath.Join(rs.templateDir, "report_template.html")
	tmpl, err := template.ParseFiles(templatePath)
	if err != nil {
		// If template file doesn't exist, use embedded template
		tmpl, err = template.New("report").Parse(rs.getDefaultHTMLTemplate())
		if err != nil {
			return "", 0, fmt.Errorf("failed to parse template: %w", err)
		}
	}

	// Generate HTML content
	var htmlBuffer bytes.Buffer
	if err := tmpl.Execute(&htmlBuffer, data); err != nil {
		return "", 0, fmt.Errorf("failed to execute template: %w", err)
	}

	// Write to file
	filename := fmt.Sprintf("report_%s.html", reportID)
	filePath := filepath.Join(rs.outputDir, filename)

	if err := os.WriteFile(filePath, htmlBuffer.Bytes(), 0644); err != nil {
		return "", 0, fmt.Errorf("failed to write HTML file: %w", err)
	}

	// Get file size
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return "", 0, fmt.Errorf("failed to get file info: %w", err)
	}

	return filePath, fileInfo.Size(), nil
}

// generatePDFReport generates a PDF report using gofpdf library
func (rs *ReportService) generatePDFReport(data *models.ReportData, reportID string) (string, int64, error) {
	pdf := gofpdf.New("P", "mm", "A4", "")

	// Add a page
	pdf.AddPage()

	// Set font
	pdf.SetFont("Arial", "B", 16)

	// Title
	pdf.Cell(0, 10, data.ReportMetadata.Title)
	pdf.Ln(15)

	// Report metadata
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(0, 8, fmt.Sprintf("Generated: %s", data.ReportMetadata.GeneratedAt.Format("January 2, 2006 at 3:04 PM")))
	pdf.Ln(8)
	pdf.Cell(0, 8, fmt.Sprintf("Report Type: %s", string(data.ReportMetadata.Type)))
	pdf.Ln(8)
	pdf.Cell(0, 8, fmt.Sprintf("Time Filter: %s", string(data.ReportMetadata.TimeFilter)))
	pdf.Ln(15)

	// Performance Metrics Section
	pdf.SetFont("Arial", "B", 14)
	pdf.Cell(0, 10, "Performance Overview")
	pdf.Ln(12)

	pdf.SetFont("Arial", "", 11)
	pdf.Cell(0, 6, fmt.Sprintf("Overall Score: %.1f%%", data.PerformanceMetrics.OverallScore))
	pdf.Ln(6)
	pdf.Cell(0, 6, fmt.Sprintf("Tests Completed: %d", data.PerformanceMetrics.TestsCompleted))
	pdf.Ln(6)
	pdf.Cell(0, 6, fmt.Sprintf("Consistency Score: %.1f%%", data.PerformanceMetrics.ConsistencyScore))
	pdf.Ln(6)
	pdf.Cell(0, 6, fmt.Sprintf("Study Time: %.1f hours", data.PerformanceMetrics.StudyTimeHours))
	pdf.Ln(6)
	pdf.Cell(0, 6, fmt.Sprintf("Improvement Rate: %.1f%%", data.PerformanceMetrics.ImprovementRate))
	pdf.Ln(6)
	pdf.Cell(0, 6, fmt.Sprintf("Best Subject: %s", data.PerformanceMetrics.BestSubject))
	pdf.Ln(6)
	pdf.Cell(0, 6, fmt.Sprintf("Weakest Subject: %s", data.PerformanceMetrics.WeakestSubject))
	pdf.Ln(15)

	// Topic Analysis Section
	if len(data.TopicAnalysis) > 0 {
		pdf.SetFont("Arial", "B", 14)
		pdf.Cell(0, 10, "Topic Performance")
		pdf.Ln(12)

		pdf.SetFont("Arial", "", 11)
		for _, topic := range data.TopicAnalysis {
			pdf.Cell(0, 6, fmt.Sprintf("%s (%s): %.1f%% proficiency", topic.TopicName, topic.SubjectName, topic.ProficiencyScore))
			pdf.Ln(6)
		}
		pdf.Ln(10)
	}

	// Test History Section
	if len(data.TestHistory) > 0 {
		pdf.SetFont("Arial", "B", 14)
		pdf.Cell(0, 10, "Recent Test History")
		pdf.Ln(12)

		pdf.SetFont("Arial", "", 10)
		for i, test := range data.TestHistory {
			if i >= 5 { // Limit to 5 most recent tests
				break
			}
			pdf.Cell(0, 5, fmt.Sprintf("%s - %s: %.1f%% (%d/%d correct)",
				test.TestDate.Format("Jan 2"), test.Subject, test.Score, test.CorrectAnswers, test.TotalQuestions))
			pdf.Ln(5)
		}
	}

	// Save PDF to file
	pdfFilename := fmt.Sprintf("report_%s.pdf", reportID)
	pdfPath := filepath.Join(rs.outputDir, pdfFilename)

	err := pdf.OutputFileAndClose(pdfPath)
	if err != nil {
		return "", 0, fmt.Errorf("failed to save PDF file: %w", err)
	}

	// Get file size
	fileInfo, err := os.Stat(pdfPath)
	if err != nil {
		return "", 0, fmt.Errorf("failed to get PDF file info: %w", err)
	}

	return pdfPath, fileInfo.Size(), nil
}

// GenerateHTMLPreview generates an HTML preview of the report data
func (rs *ReportService) GenerateHTMLPreview(data *models.ReportData) (string, error) {
	// Use the same HTML template as the full report but with preview styling
	tmpl, err := template.New("preview").Parse(rs.getPreviewHTMLTemplate())
	if err != nil {
		return "", fmt.Errorf("failed to parse preview template: %w", err)
	}

	var htmlBuffer bytes.Buffer
	if err := tmpl.Execute(&htmlBuffer, data); err != nil {
		return "", fmt.Errorf("failed to execute preview template: %w", err)
	}

	return htmlBuffer.String(), nil
}

// getUserInfo retrieves user information for the report
func (rs *ReportService) getUserInfo(ctx context.Context, userID uuid.UUID) (models.UserReportInfo, error) {
	// Get user with profile information
	userWithProfile, err := rs.store.GetUserWithProfile(ctx, userID.String())
	if err != nil {
		// Fallback to basic user info if profile doesn't exist
		user, err := rs.store.GetUserByID(ctx, userID.String())
		if err != nil {
			return models.UserReportInfo{}, fmt.Errorf("failed to get user info: %w", err)
		}

		return models.UserReportInfo{
			Name:        user.Email, // Use email as name if no profile
			Email:       user.Email,
			GeneratedAt: time.Now(),
		}, nil
	}

	// Use full name from profile if available, otherwise use email
	name := userWithProfile.Email
	if userWithProfile.FullName != nil && *userWithProfile.FullName != "" {
		name = *userWithProfile.FullName
	}

	// Add grade/class info if available
	grade := ""
	if userWithProfile.Class != nil && *userWithProfile.Class != "" {
		grade = *userWithProfile.Class
	}

	return models.UserReportInfo{
		Name:        name,
		Email:       userWithProfile.Email,
		Grade:       grade,
		GeneratedAt: time.Now(),
	}, nil
}

// getPreviewHTMLTemplate returns a simplified HTML template for preview
func (rs *ReportService) getPreviewHTMLTemplate() string {
	return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.ReportMetadata.Title}} - Preview</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .preview-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 2.2em;
        }
        .metadata {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 25px;
            border-left: 4px solid #007bff;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 8px;
            margin-bottom: 15px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .metric-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        .topic-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }
        .topic-item {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            border-left: 4px solid #28a745;
        }
        .topic-item.weak {
            border-left-color: #dc3545;
        }
        .topic-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .topic-score {
            color: #007bff;
            font-weight: bold;
        }
        .test-history {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
        }
        .test-item {
            padding: 10px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-score {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="preview-banner">
        <h3>📊 Report Preview</h3>
        <p>This is a preview of your report. Use the "Generate Report" button to create the final version.</p>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>{{.ReportMetadata.Title}}</h1>
            <p>Generated for {{.UserInfo.Name}} ({{.UserInfo.Email}})</p>
        </div>

        <!-- Report Metadata -->
        <div class="metadata">
            <strong>Report Details:</strong><br>
            Type: {{.ReportMetadata.Type}} | Format: {{.ReportMetadata.Format}} | Period: {{.ReportMetadata.TimeFilter}}<br>
            Generated: {{.ReportMetadata.GeneratedAt.Format "January 2, 2006 at 3:04 PM"}}
        </div>

        <!-- Performance Overview -->
        <div class="section">
            <h2>📈 Performance Overview</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">{{printf "%.1f%%" .PerformanceMetrics.OverallScore}}</div>
                    <div class="metric-label">Overall Score</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{{.PerformanceMetrics.TestsCompleted}}</div>
                    <div class="metric-label">Tests Completed</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{{printf "%.1f%%" .PerformanceMetrics.ConsistencyScore}}</div>
                    <div class="metric-label">Consistency</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{{printf "%.1f%%" .PerformanceMetrics.ImprovementRate}}</div>
                    <div class="metric-label">Improvement Rate</div>
                </div>
            </div>
            <p><strong>Best Subject:</strong> {{.PerformanceMetrics.BestSubject}} | <strong>Needs Focus:</strong> {{.PerformanceMetrics.WeakestSubject}}</p>
        </div>

        <!-- Topic Analysis -->
        {{if .TopicAnalysis}}
        <div class="section">
            <h2>🎯 Topic Performance</h2>
            <div class="topic-list">
                {{range .TopicAnalysis}}
                <div class="topic-item {{if lt .ProficiencyScore 70.0}}weak{{end}}">
                    <div class="topic-name">{{.TopicName}}</div>
                    <div class="topic-score">{{printf "%.1f%%" .ProficiencyScore}} proficiency</div>
                    <div style="font-size: 0.9em; color: #666;">{{.SubjectName}}</div>
                </div>
                {{end}}
            </div>
        </div>
        {{end}}

        <!-- Test History -->
        {{if .TestHistory}}
        <div class="section">
            <h2>📚 Recent Test History</h2>
            <div class="test-history">
                {{range $index, $test := .TestHistory}}
                {{if lt $index 5}}
                <div class="test-item">
                    <div>
                        <strong>{{$test.Subject}}</strong><br>
                        <small>{{$test.TestDate.Format "Jan 2, 2006"}}</small>
                    </div>
                    <div class="test-score">{{printf "%.1f%%" $test.Score}}</div>
                </div>
                {{end}}
                {{end}}
            </div>
        </div>
        {{end}}

        <!-- Study Insights -->
        <div class="section">
            <h2>💡 Key Insights</h2>
            <ul>
                <li>You've completed {{.PerformanceMetrics.TestsCompleted}} tests with an overall score of {{printf "%.1f%%" .PerformanceMetrics.OverallScore}}</li>
                <li>Your strongest area is {{.PerformanceMetrics.BestSubject}}</li>
                <li>Focus on improving {{.PerformanceMetrics.WeakestSubject}} for better overall performance</li>
                <li>Your improvement rate is {{printf "%.1f%%" .PerformanceMetrics.ImprovementRate}} - keep up the good work!</li>
            </ul>
        </div>
    </div>
</body>
</html>`
}

// getDefaultHTMLTemplate returns a default HTML template for reports
func (rs *ReportService) getDefaultHTMLTemplate() string {
	return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.ReportMetadata.Title}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header .subtitle {
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .section {
            background: white;
            margin-bottom: 30px;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        .topic-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .topic-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .topic-item.weak {
            border-left-color: #dc3545;
        }
        .topic-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .topic-score {
            font-size: 1.2em;
            font-weight: bold;
        }
        .recommendations {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        .recommendations ul {
            margin: 0;
            padding-left: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
        }
        @media print {
            body { background-color: white; }
            .section { box-shadow: none; border: 1px solid #ddd; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{.ReportMetadata.Title}}</h1>
        <div class="subtitle">
            Generated for {{.UserInfo.Name}} on {{.ReportMetadata.GeneratedAt.Format "January 2, 2006"}}
        </div>
    </div>

    <!-- Performance Overview -->
    <div class="section">
        <h2>Performance Overview</h2>
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">{{printf "%.1f%%" .PerformanceMetrics.OverallScore}}</div>
                <div class="metric-label">Overall Score</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{.PerformanceMetrics.TestsCompleted}}</div>
                <div class="metric-label">Tests Completed</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{printf "%.1f%%" .PerformanceMetrics.ImprovementRate}}</div>
                <div class="metric-label">Improvement Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{printf "%.1fh" .PerformanceMetrics.StudyTimeHours}}</div>
                <div class="metric-label">Study Time</div>
            </div>
        </div>
    </div>

    <!-- Strengths and Weaknesses -->
    <div class="section">
        <h2>Topic Performance Analysis</h2>
        <div class="topic-list">
            {{range .TopicAnalysis}}
            <div class="topic-item {{if lt .ProficiencyScore 70.0}}weak{{end}}">
                <div class="topic-name">{{.TopicName}}</div>
                <div class="topic-score">{{printf "%.1f%%" .ProficiencyScore}}</div>
                <div style="font-size: 0.9em; color: #666;">{{.SubjectName}}</div>
            </div>
            {{end}}
        </div>
    </div>

    <!-- Recommendations -->
    {{if .Recommendations}}
    <div class="section">
        <h2>Personalized Recommendations</h2>
        <div class="recommendations">
            <ul>
                {{range .Recommendations}}
                <li><strong>{{.Title}}:</strong> {{.Description}}</li>
                {{end}}
            </ul>
        </div>
    </div>
    {{end}}

    <!-- Study Patterns -->
    {{if .StudyPatterns}}
    <div class="section">
        <h2>Study Behavior Analysis</h2>
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">{{.StudyPatterns.WeeklyTestCount}}</div>
                <div class="metric-label">Tests per Week</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{.StudyPatterns.AverageSessionTime}}min</div>
                <div class="metric-label">Avg Session Time</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{.StudyPatterns.BestPerformanceDay}}</div>
                <div class="metric-label">Best Study Day</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{printf "%.0f%%" .StudyPatterns.ConsistencyScore}}</div>
                <div class="metric-label">Consistency Score</div>
            </div>
        </div>
    </div>
    {{end}}

    <div class="footer">
        <p>Report generated by Test-Spark Analytics Engine</p>
        <p>{{.ReportMetadata.GeneratedAt.Format "Monday, January 2, 2006 at 3:04 PM"}}</p>
    </div>
</body>
</html>`
}
