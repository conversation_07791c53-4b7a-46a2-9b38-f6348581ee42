// Package services provides caching functionality for improved performance.
// This cache service implements an in-memory cache with TTL (Time To Live)
// for frequently accessed data like analytics results and user dashboards.
package services

import (
	"fmt"
	"sync"
	"time"

	"test-spark-backend/internal/models"
)

// CacheItem represents a cached item with expiration
type CacheItem struct {
	Data      interface{}
	ExpiresAt time.Time
}

// IsExpired checks if the cache item has expired
func (ci *CacheItem) IsExpired() bool {
	return time.Now().After(ci.ExpiresAt)
}

// CacheService provides in-memory caching with TTL support
type CacheService struct {
	cache map[string]*CacheItem
	mutex sync.RWMutex

	// Default TTL values for different data types
	defaultTTL     time.Duration
	dashboardTTL   time.Duration
	analyticsTTL   time.Duration
	performanceTTL time.Duration
}

// CacheConfig holds configuration for the cache service
type CacheConfig struct {
	DefaultTTL      time.Duration
	DashboardTTL    time.Duration
	AnalyticsTTL    time.Duration
	PerformanceTTL  time.Duration
	CleanupInterval time.Duration
}

// NewCacheService creates a new cache service with the given configuration
func NewCacheService(config *CacheConfig) *CacheService {
	if config == nil {
		config = &CacheConfig{
			DefaultTTL:      5 * time.Minute,
			DashboardTTL:    2 * time.Minute,
			AnalyticsTTL:    10 * time.Minute,
			PerformanceTTL:  5 * time.Minute,
			CleanupInterval: 10 * time.Minute,
		}
	}

	cs := &CacheService{
		cache:          make(map[string]*CacheItem),
		defaultTTL:     config.DefaultTTL,
		dashboardTTL:   config.DashboardTTL,
		analyticsTTL:   config.AnalyticsTTL,
		performanceTTL: config.PerformanceTTL,
	}

	// Start cleanup goroutine
	go cs.startCleanup(config.CleanupInterval)

	return cs
}

// Set stores a value in the cache with the specified TTL
func (cs *CacheService) Set(key string, value interface{}, ttl time.Duration) {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	cs.cache[key] = &CacheItem{
		Data:      value,
		ExpiresAt: time.Now().Add(ttl),
	}
}

// Get retrieves a value from the cache
func (cs *CacheService) Get(key string) (interface{}, bool) {
	cs.mutex.RLock()
	defer cs.mutex.RUnlock()

	item, exists := cs.cache[key]
	if !exists || item.IsExpired() {
		return nil, false
	}

	return item.Data, true
}

// Delete removes a value from the cache
func (cs *CacheService) Delete(key string) {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	delete(cs.cache, key)
}

// Clear removes all items from the cache
func (cs *CacheService) Clear() {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	cs.cache = make(map[string]*CacheItem)
}

// GetStats returns cache statistics
func (cs *CacheService) GetStats() map[string]interface{} {
	cs.mutex.RLock()
	defer cs.mutex.RUnlock()

	expired := 0
	for _, item := range cs.cache {
		if item.IsExpired() {
			expired++
		}
	}

	return map[string]interface{}{
		"total_items":   len(cs.cache),
		"expired_items": expired,
		"active_items":  len(cs.cache) - expired,
	}
}

// startCleanup runs a background goroutine to clean up expired items
func (cs *CacheService) startCleanup(interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for range ticker.C {
		cs.cleanup()
	}
}

// cleanup removes expired items from the cache
func (cs *CacheService) cleanup() {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	for key, item := range cs.cache {
		if item.IsExpired() {
			delete(cs.cache, key)
		}
	}
}

// Cache key generators for different data types

// DashboardCacheKey generates a cache key for dashboard data
func (cs *CacheService) DashboardCacheKey(userID string) string {
	return fmt.Sprintf("dashboard:%s", userID)
}

// PerformanceTrendsCacheKey generates a cache key for performance trends
func (cs *CacheService) PerformanceTrendsCacheKey(userID string, timeFilter models.TimeFilter) string {
	return fmt.Sprintf("performance_trends:%s:%s", userID, timeFilter)
}

// TopicPerformanceCacheKey generates a cache key for topic performance
func (cs *CacheService) TopicPerformanceCacheKey(userID string) string {
	return fmt.Sprintf("topic_performance:%s", userID)
}

// HeatmapDataCacheKey generates a cache key for heatmap data
func (cs *CacheService) HeatmapDataCacheKey(userID string) string {
	return fmt.Sprintf("heatmap:%s", userID)
}

// Cached data retrieval methods

// GetCachedDashboard retrieves dashboard data from cache or returns nil if not found
func (cs *CacheService) GetCachedDashboard(userID string) (*models.DashboardSummary, bool) {
	key := cs.DashboardCacheKey(userID)
	data, found := cs.Get(key)
	if !found {
		return nil, false
	}

	dashboard, ok := data.(*models.DashboardSummary)
	return dashboard, ok
}

// SetCachedDashboard stores dashboard data in cache
func (cs *CacheService) SetCachedDashboard(userID string, dashboard *models.DashboardSummary) {
	key := cs.DashboardCacheKey(userID)
	cs.Set(key, dashboard, cs.dashboardTTL)
}

// GetCachedPerformanceTrends retrieves performance trends from cache
func (cs *CacheService) GetCachedPerformanceTrends(userID string, timeFilter models.TimeFilter) ([]models.PerformanceTrend, bool) {
	key := cs.PerformanceTrendsCacheKey(userID, timeFilter)
	data, found := cs.Get(key)
	if !found {
		return nil, false
	}

	trends, ok := data.([]models.PerformanceTrend)
	return trends, ok
}

// SetCachedPerformanceTrends stores performance trends in cache
func (cs *CacheService) SetCachedPerformanceTrends(userID string, timeFilter models.TimeFilter, trends []models.PerformanceTrend) {
	key := cs.PerformanceTrendsCacheKey(userID, timeFilter)
	cs.Set(key, trends, cs.performanceTTL)
}

// GetCachedTopicPerformance retrieves topic performance from cache
func (cs *CacheService) GetCachedTopicPerformance(userID string) ([]models.TopicPerformanceWithName, bool) {
	key := cs.TopicPerformanceCacheKey(userID)
	data, found := cs.Get(key)
	if !found {
		return nil, false
	}

	performance, ok := data.([]models.TopicPerformanceWithName)
	return performance, ok
}

// SetCachedTopicPerformance stores topic performance in cache
func (cs *CacheService) SetCachedTopicPerformance(userID string, performance []models.TopicPerformanceWithName) {
	key := cs.TopicPerformanceCacheKey(userID)
	cs.Set(key, performance, cs.analyticsTTL)
}

// GetCachedHeatmapData retrieves heatmap data from cache
func (cs *CacheService) GetCachedHeatmapData(userID string) ([]models.HeatmapData, bool) {
	key := cs.HeatmapDataCacheKey(userID)
	data, found := cs.Get(key)
	if !found {
		return nil, false
	}

	heatmap, ok := data.([]models.HeatmapData)
	return heatmap, ok
}

// SetCachedHeatmapData stores heatmap data in cache
func (cs *CacheService) SetCachedHeatmapData(userID string, heatmap []models.HeatmapData) {
	key := cs.HeatmapDataCacheKey(userID)
	cs.Set(key, heatmap, cs.analyticsTTL)
}

// InvalidateUserCache removes all cached data for a specific user
// This should be called when user data changes (e.g., after completing a test)
func (cs *CacheService) InvalidateUserCache(userID string) {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	// Remove all cache entries for this user
	keysToDelete := []string{}
	for key := range cs.cache {
		if cs.isUserCacheKey(key, userID) {
			keysToDelete = append(keysToDelete, key)
		}
	}

	for _, key := range keysToDelete {
		delete(cs.cache, key)
	}
}

// isUserCacheKey checks if a cache key belongs to a specific user
func (cs *CacheService) isUserCacheKey(key, userID string) bool {
	userPrefix := fmt.Sprintf(":%s", userID)
	return len(key) > len(userPrefix) &&
		(key[len(key)-len(userPrefix):] == userPrefix ||
			key[len(key)-len(userPrefix)-1:len(key)-1] == userPrefix)
}

// GetCacheHealth returns health information about the cache
func (cs *CacheService) GetCacheHealth() map[string]interface{} {
	stats := cs.GetStats()

	health := map[string]interface{}{
		"status": "healthy",
		"stats":  stats,
		"config": map[string]interface{}{
			"default_ttl":     cs.defaultTTL.String(),
			"dashboard_ttl":   cs.dashboardTTL.String(),
			"analytics_ttl":   cs.analyticsTTL.String(),
			"performance_ttl": cs.performanceTTL.String(),
		},
	}

	// Mark as unhealthy if too many expired items
	if stats["expired_items"].(int) > 100 {
		health["status"] = "degraded"
		health["warning"] = "High number of expired items, consider reducing TTL"
	}

	return health
}
