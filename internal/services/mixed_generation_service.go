package services

import (
	"context"
	"fmt"
	"log"
	"math"

	"test-spark-backend/internal/models"
)

// ProviderManager interface for dependency injection and testing
type ProviderManager interface {
	GetProvider(name string) AIProvider
	GenerateQuestions(ctx context.Context, genCtx *QuestionGenerationContext) ([]models.Question, error)
}

// MixedGenerationService handles question generation using multiple AI providers
type MixedGenerationService struct {
	aiProviderMgr ProviderManager
	config        *MixedGenerationConfig
}

// MixedGenerationConfig defines how questions should be distributed among providers
type MixedGenerationConfig struct {
	// Provider distribution ratios (should sum to 1.0)
	ProviderRatios map[string]float64 `json:"provider_ratios"`

	// Minimum questions per provider (to ensure variety)
	MinQuestionsPerProvider int `json:"min_questions_per_provider"`

	// Enable randomization of provider selection
	EnableRandomization bool `json:"enable_randomization"`

	// Fallback to single provider if mixed generation fails
	EnableFallback bool `json:"enable_fallback"`
}

// ProviderAllocation represents how many questions each provider should generate
type ProviderAllocation struct {
	ProviderName string
	NumQuestions int
}

// NewMixedGenerationService creates a new mixed generation service
func NewMixedGenerationService(aiProviderMgr ProviderManager, config *MixedGenerationConfig) *MixedGenerationService {
	if config == nil {
		config = DefaultMixedGenerationConfig()
	}

	return &MixedGenerationService{
		aiProviderMgr: aiProviderMgr,
		config:        config,
	}
}

// DefaultMixedGenerationConfig returns a default configuration for mixed generation
func DefaultMixedGenerationConfig() *MixedGenerationConfig {
	return &MixedGenerationConfig{
		ProviderRatios: map[string]float64{
			"cerebras": 0.6, // 60% from Cerebras
			"groq":     0.4, // 40% from Groq
		},
		MinQuestionsPerProvider: 1,
		EnableRandomization:     true,
		EnableFallback:          true,
	}
}

// GenerateQuestionsMixed generates questions using multiple providers based on configuration
func (mgs *MixedGenerationService) GenerateQuestionsMixed(ctx context.Context, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	if mgs.aiProviderMgr == nil {
		return nil, fmt.Errorf("AI provider manager is not initialized")
	}

	// Calculate allocation for each provider
	allocations, err := mgs.calculateProviderAllocations(genCtx.NumQuestions)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate provider allocations: %w", err)
	}

	log.Printf("Mixed generation allocations: %+v", allocations)

	var allQuestions []models.Question
	var errors []error

	// Generate questions from each provider
	for _, allocation := range allocations {
		if allocation.NumQuestions <= 0 {
			continue
		}

		// Create a new context for this provider with adjusted question count
		providerGenCtx := *genCtx
		providerGenCtx.NumQuestions = allocation.NumQuestions

		// Get the specific provider
		provider := mgs.aiProviderMgr.GetProvider(allocation.ProviderName)
		if provider == nil {
			log.Printf("Provider %s not found, skipping", allocation.ProviderName)
			errors = append(errors, fmt.Errorf("provider %s not found", allocation.ProviderName))
			continue
		}

		// Check if provider is available
		if !provider.IsAvailable(ctx) {
			log.Printf("Provider %s not available, skipping", allocation.ProviderName)
			errors = append(errors, fmt.Errorf("provider %s not available", allocation.ProviderName))
			continue
		}

		// Generate questions from this provider
		questions, err := provider.GenerateQuestions(ctx, &providerGenCtx)
		if err != nil {
			log.Printf("Failed to generate questions from provider %s: %v", allocation.ProviderName, err)
			errors = append(errors, fmt.Errorf("provider %s failed: %w", allocation.ProviderName, err))
			continue
		}

		// Add provider metadata to questions
		for i := range questions {
			providerInfo := fmt.Sprintf("%s-mixed", allocation.ProviderName)
			questions[i].AuthorAIModel = &providerInfo
		}

		allQuestions = append(allQuestions, questions...)
		log.Printf("Generated %d questions from provider %s", len(questions), allocation.ProviderName)
	}

	// Check if we have enough questions
	if len(allQuestions) == 0 {
		if mgs.config.EnableFallback {
			log.Printf("Mixed generation failed, falling back to primary provider")
			return mgs.aiProviderMgr.GenerateQuestions(ctx, genCtx)
		}
		return nil, fmt.Errorf("failed to generate questions from any provider: %v", errors)
	}

	// If we don't have enough questions, try to fill the gap
	if len(allQuestions) < genCtx.NumQuestions && mgs.config.EnableFallback {
		needed := genCtx.NumQuestions - len(allQuestions)
		log.Printf("Need %d more questions, using fallback generation", needed)

		fallbackGenCtx := *genCtx
		fallbackGenCtx.NumQuestions = needed

		fallbackQuestions, err := mgs.aiProviderMgr.GenerateQuestions(ctx, &fallbackGenCtx)
		if err == nil {
			// Add fallback metadata
			for i := range fallbackQuestions {
				fallbackInfo := "fallback-mixed"
				fallbackQuestions[i].AuthorAIModel = &fallbackInfo
			}
			allQuestions = append(allQuestions, fallbackQuestions...)
		}
	}

	// Shuffle questions if randomization is enabled
	if mgs.config.EnableRandomization {
		allQuestions = mgs.shuffleQuestions(allQuestions)
	}

	// Trim to requested number if we have too many
	if len(allQuestions) > genCtx.NumQuestions {
		allQuestions = allQuestions[:genCtx.NumQuestions]
	}

	log.Printf("Mixed generation completed: %d questions from %d providers", len(allQuestions), len(allocations))
	return allQuestions, nil
}

// calculateProviderAllocations calculates how many questions each provider should generate
func (mgs *MixedGenerationService) calculateProviderAllocations(totalQuestions int) ([]ProviderAllocation, error) {
	var allocations []ProviderAllocation

	// Get available providers
	availableProviders := mgs.getAvailableProviders()
	if len(availableProviders) == 0 {
		return nil, fmt.Errorf("no providers available for mixed generation")
	}

	// Calculate base allocation based on ratios
	remainingQuestions := totalQuestions

	for providerName, ratio := range mgs.config.ProviderRatios {
		// Check if provider is available
		if !containsProvider(availableProviders, providerName) {
			log.Printf("Provider %s not available, skipping from allocation", providerName)
			continue
		}

		// Calculate questions for this provider
		questionsForProvider := int(math.Round(float64(totalQuestions) * ratio))

		// Ensure minimum questions per provider
		if questionsForProvider < mgs.config.MinQuestionsPerProvider && remainingQuestions >= mgs.config.MinQuestionsPerProvider {
			questionsForProvider = mgs.config.MinQuestionsPerProvider
		}

		// Don't exceed remaining questions
		if questionsForProvider > remainingQuestions {
			questionsForProvider = remainingQuestions
		}

		if questionsForProvider > 0 {
			allocations = append(allocations, ProviderAllocation{
				ProviderName: providerName,
				NumQuestions: questionsForProvider,
			})
			remainingQuestions -= questionsForProvider
		}
	}

	// Distribute any remaining questions to the first available provider
	if remainingQuestions > 0 && len(allocations) > 0 {
		allocations[0].NumQuestions += remainingQuestions
	}

	return allocations, nil
}

// getAvailableProviders returns a list of available provider names
func (mgs *MixedGenerationService) getAvailableProviders() []string {
	var available []string

	for providerName := range mgs.config.ProviderRatios {
		provider := mgs.aiProviderMgr.GetProvider(providerName)
		if provider != nil && provider.IsAvailable(context.Background()) {
			available = append(available, providerName)
		}
	}

	return available
}

// shuffleQuestions randomly shuffles the questions array
func (mgs *MixedGenerationService) shuffleQuestions(questions []models.Question) []models.Question {
	// Simple shuffle implementation
	for i := len(questions) - 1; i > 0; i-- {
		j := i % (i + 1) // Simple pseudo-random
		questions[i], questions[j] = questions[j], questions[i]
	}
	return questions
}

// containsProvider checks if a string slice contains a specific string
func containsProvider(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// UpdateConfig updates the mixed generation configuration
func (mgs *MixedGenerationService) UpdateConfig(config *MixedGenerationConfig) {
	mgs.config = config
}

// GetConfig returns the current configuration
func (mgs *MixedGenerationService) GetConfig() *MixedGenerationConfig {
	return mgs.config
}
