// Package services provides Redis-based caching functionality for improved performance.
// This service provides distributed caching capabilities using Redis as the backend,
// making it suitable for production environments with multiple application instances.
package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"test-spark-backend/internal/models"

	"github.com/redis/go-redis/v9"
)

// RedisCacheService provides distributed caching using Redis
type RedisCacheService struct {
	client *redis.Client

	// Default TTL values for different data types
	defaultTTL     time.Duration
	dashboardTTL   time.Duration
	analyticsTTL   time.Duration
	performanceTTL time.Duration
}

// RedisConfig holds configuration for the Redis cache service
type RedisConfig struct {
	Addr           string
	Password       string
	DB             int
	DefaultTTL     time.Duration
	DashboardTTL   time.Duration
	AnalyticsTTL   time.Duration
	PerformanceTTL time.Duration
}

// NewRedisCacheService creates a new Redis cache service with the given configuration
func NewRedisCacheService(config *RedisConfig) (*RedisCacheService, error) {
	if config == nil {
		config = &RedisConfig{
			Addr:           "localhost:6379",
			Password:       "",
			DB:             0,
			DefaultTTL:     5 * time.Minute,
			DashboardTTL:   2 * time.Minute,
			AnalyticsTTL:   10 * time.Minute,
			PerformanceTTL: 5 * time.Minute,
		}
	}

	// Create Redis client
	rdb := redis.NewClient(&redis.Options{
		Addr:     config.Addr,
		Password: config.Password,
		DB:       config.DB,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	log.Printf("Redis cache service connected successfully to %s", config.Addr)

	return &RedisCacheService{
		client:         rdb,
		defaultTTL:     config.DefaultTTL,
		dashboardTTL:   config.DashboardTTL,
		analyticsTTL:   config.AnalyticsTTL,
		performanceTTL: config.PerformanceTTL,
	}, nil
}

// Set stores a value in Redis with the specified TTL
func (rcs *RedisCacheService) Set(key string, value interface{}, ttl time.Duration) error {
	ctx := context.Background()

	// Serialize the value to JSON
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	// Store in Redis
	err = rcs.client.Set(ctx, key, data, ttl).Err()
	if err != nil {
		return fmt.Errorf("failed to set value in Redis: %w", err)
	}

	return nil
}

// Get retrieves a value from Redis
func (rcs *RedisCacheService) Get(key string, dest interface{}) (bool, error) {
	ctx := context.Background()

	// Get from Redis
	data, err := rcs.client.Get(ctx, key).Result()
	if err == redis.Nil {
		return false, nil // Key does not exist
	}
	if err != nil {
		return false, fmt.Errorf("failed to get value from Redis: %w", err)
	}

	// Deserialize the JSON data
	err = json.Unmarshal([]byte(data), dest)
	if err != nil {
		return false, fmt.Errorf("failed to unmarshal value: %w", err)
	}

	return true, nil
}

// Delete removes a value from Redis
func (rcs *RedisCacheService) Delete(key string) error {
	ctx := context.Background()
	return rcs.client.Del(ctx, key).Err()
}

// Clear removes all keys matching a pattern
func (rcs *RedisCacheService) Clear(pattern string) error {
	ctx := context.Background()

	// Get all keys matching the pattern
	keys, err := rcs.client.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to get keys: %w", err)
	}

	if len(keys) == 0 {
		return nil
	}

	// Delete all matching keys
	return rcs.client.Del(ctx, keys...).Err()
}

// Close closes the Redis connection
func (rcs *RedisCacheService) Close() error {
	return rcs.client.Close()
}

// Health check methods
func (rcs *RedisCacheService) Ping() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return rcs.client.Ping(ctx).Err()
}

// GetStats returns Redis cache statistics
func (rcs *RedisCacheService) GetStats() map[string]interface{} {
	ctx := context.Background()

	info, err := rcs.client.Info(ctx, "memory", "stats").Result()
	if err != nil {
		log.Printf("Failed to get Redis stats: %v", err)
		return map[string]interface{}{
			"status": "error",
			"error":  err.Error(),
		}
	}

	return map[string]interface{}{
		"status":     "connected",
		"redis_info": info,
		"type":       "redis",
	}
}

// Cache key generation methods (same as in-memory cache for compatibility)

// DashboardCacheKey generates a cache key for dashboard data
func (rcs *RedisCacheService) DashboardCacheKey(userID string) string {
	return fmt.Sprintf("dashboard:%s", userID)
}

// PerformanceTrendsCacheKey generates a cache key for performance trends
func (rcs *RedisCacheService) PerformanceTrendsCacheKey(userID string, timeFilter models.TimeFilter) string {
	return fmt.Sprintf("performance_trends:%s:%s", userID, timeFilter)
}

// TopicPerformanceCacheKey generates a cache key for topic performance
func (rcs *RedisCacheService) TopicPerformanceCacheKey(userID string) string {
	return fmt.Sprintf("topic_performance:%s", userID)
}

// HeatmapDataCacheKey generates a cache key for heatmap data
func (rcs *RedisCacheService) HeatmapDataCacheKey(userID string) string {
	return fmt.Sprintf("heatmap_data:%s", userID)
}

// Analytics-specific cache methods

// GetCachedDashboard retrieves dashboard data from Redis cache
func (rcs *RedisCacheService) GetCachedDashboard(userID string) (*models.DashboardSummary, bool) {
	key := rcs.DashboardCacheKey(userID)
	var dashboard models.DashboardSummary

	found, err := rcs.Get(key, &dashboard)
	if err != nil {
		log.Printf("Error getting cached dashboard for user %s: %v", userID, err)
		return nil, false
	}

	if found {
		log.Printf("Dashboard cache hit for user: %s", userID)
	}

	return &dashboard, found
}

// SetCachedDashboard stores dashboard data in Redis cache
func (rcs *RedisCacheService) SetCachedDashboard(userID string, dashboard *models.DashboardSummary) {
	key := rcs.DashboardCacheKey(userID)
	err := rcs.Set(key, dashboard, rcs.dashboardTTL)
	if err != nil {
		log.Printf("Error setting cached dashboard for user %s: %v", userID, err)
	}
}

// GetCachedPerformanceTrends retrieves performance trends from Redis cache
func (rcs *RedisCacheService) GetCachedPerformanceTrends(userID string, timeFilter models.TimeFilter) ([]models.PerformanceTrend, bool) {
	key := rcs.PerformanceTrendsCacheKey(userID, timeFilter)
	var trends []models.PerformanceTrend

	found, err := rcs.Get(key, &trends)
	if err != nil {
		log.Printf("Error getting cached performance trends for user %s: %v", userID, err)
		return nil, false
	}

	if found {
		log.Printf("Performance trends cache hit for user: %s, filter: %s", userID, timeFilter)
	}

	return trends, found
}

// SetCachedPerformanceTrends stores performance trends in Redis cache
func (rcs *RedisCacheService) SetCachedPerformanceTrends(userID string, timeFilter models.TimeFilter, trends []models.PerformanceTrend) {
	key := rcs.PerformanceTrendsCacheKey(userID, timeFilter)
	err := rcs.Set(key, trends, rcs.performanceTTL)
	if err != nil {
		log.Printf("Error setting cached performance trends for user %s: %v", userID, err)
	}
}

// GetCachedTopicPerformance retrieves topic performance from Redis cache
func (rcs *RedisCacheService) GetCachedTopicPerformance(userID string) ([]models.TopicPerformanceWithName, bool) {
	key := rcs.TopicPerformanceCacheKey(userID)
	var performance []models.TopicPerformanceWithName

	found, err := rcs.Get(key, &performance)
	if err != nil {
		log.Printf("Error getting cached topic performance for user %s: %v", userID, err)
		return nil, false
	}

	if found {
		log.Printf("Topic performance cache hit for user: %s", userID)
	}

	return performance, found
}

// SetCachedTopicPerformance stores topic performance in Redis cache
func (rcs *RedisCacheService) SetCachedTopicPerformance(userID string, performance []models.TopicPerformanceWithName) {
	key := rcs.TopicPerformanceCacheKey(userID)
	err := rcs.Set(key, performance, rcs.analyticsTTL)
	if err != nil {
		log.Printf("Error setting cached topic performance for user %s: %v", userID, err)
	}
}

// GetCachedHeatmapData retrieves heatmap data from Redis cache
func (rcs *RedisCacheService) GetCachedHeatmapData(userID string) ([]models.HeatmapData, bool) {
	key := rcs.HeatmapDataCacheKey(userID)
	var heatmap []models.HeatmapData

	found, err := rcs.Get(key, &heatmap)
	if err != nil {
		log.Printf("Error getting cached heatmap data for user %s: %v", userID, err)
		return nil, false
	}

	if found {
		log.Printf("Heatmap data cache hit for user: %s", userID)
	}

	return heatmap, found
}

// SetCachedHeatmapData stores heatmap data in Redis cache
func (rcs *RedisCacheService) SetCachedHeatmapData(userID string, heatmap []models.HeatmapData) {
	key := rcs.HeatmapDataCacheKey(userID)
	err := rcs.Set(key, heatmap, rcs.analyticsTTL)
	if err != nil {
		log.Printf("Error setting cached heatmap data for user %s: %v", userID, err)
	}
}

// InvalidateUserCache removes all cached data for a specific user
func (rcs *RedisCacheService) InvalidateUserCache(userID string) {
	patterns := []string{
		fmt.Sprintf("dashboard:%s", userID),
		fmt.Sprintf("performance_trends:%s:*", userID),
		fmt.Sprintf("topic_performance:%s", userID),
		fmt.Sprintf("heatmap_data:%s", userID),
	}

	for _, pattern := range patterns {
		err := rcs.Clear(pattern)
		if err != nil {
			log.Printf("Error invalidating cache pattern %s for user %s: %v", pattern, userID, err)
		}
	}
}
