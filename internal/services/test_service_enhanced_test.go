package services

import (
	"context"
	"testing"
	"time"

	"test-spark-backend/internal/models"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockEnhancedStore extends the basic mock to include enhanced question methods
type MockEnhancedStore struct {
	mock.Mock
}

// Basic Store methods
func (m *MockEnhancedStore) CreateQuestion(ctx context.Context, question *models.Question) error {
	args := m.Called(ctx, question)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetRandomQuestions(ctx context.Context, topicIDs []int, difficulties []models.DifficultyLevel, limit int) ([]models.Question, error) {
	args := m.Called(ctx, topicIDs, difficulties, limit)
	return args.Get(0).([]models.Question), args.Error(1)
}

// Enhanced question methods
func (m *MockEnhancedStore) GetSmartRandomQuestions(ctx context.Context, userID string, topicIDs []int, difficulties []models.DifficultyLevel, limit int, avoidRecentDays int) ([]models.Question, error) {
	args := m.Called(ctx, userID, topicIDs, difficulties, limit, avoidRecentDays)
	return args.Get(0).([]models.Question), args.Error(1)
}

func (m *MockEnhancedStore) GetDiverseQuestions(ctx context.Context, userID string, topicIDs []int, difficulties []models.DifficultyLevel, limit int) ([]models.Question, error) {
	args := m.Called(ctx, userID, topicIDs, difficulties, limit)
	return args.Get(0).([]models.Question), args.Error(1)
}

func (m *MockEnhancedStore) GetQuestionPoolSize(ctx context.Context, topicIDs []int, difficulties []models.DifficultyLevel) (int, error) {
	args := m.Called(ctx, topicIDs, difficulties)
	return args.Int(0), args.Error(1)
}

func (m *MockEnhancedStore) RecordQuestionSeen(ctx context.Context, userID uuid.UUID, questionID uuid.UUID, responseTimeMs *int, isCorrect *bool) error {
	args := m.Called(ctx, userID, questionID, responseTimeMs, isCorrect)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetUserQuestionHistory(ctx context.Context, userID uuid.UUID, limit int) ([]models.UserQuestionHistory, error) {
	args := m.Called(ctx, userID, limit)
	return args.Get(0).([]models.UserQuestionHistory), args.Error(1)
}

func (m *MockEnhancedStore) GetUserSeenQuestionIDs(ctx context.Context, userID uuid.UUID, topicIDs []int, difficulties []models.DifficultyLevel, daysSince int) ([]uuid.UUID, error) {
	args := m.Called(ctx, userID, topicIDs, difficulties, daysSince)
	return args.Get(0).([]uuid.UUID), args.Error(1)
}

func (m *MockEnhancedStore) GetSubjects(ctx context.Context) ([]models.Subject, error) {
	args := m.Called(ctx)
	return args.Get(0).([]models.Subject), args.Error(1)
}

func (m *MockEnhancedStore) GetTopicsBySubject(ctx context.Context, subjectID int) ([]models.Topic, error) {
	args := m.Called(ctx, subjectID)
	return args.Get(0).([]models.Topic), args.Error(1)
}

// Additional Store interface methods (minimal implementation for testing)
func (m *MockEnhancedStore) CreateUser(ctx context.Context, user *models.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetUserByID(ctx context.Context, id string) (*models.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockEnhancedStore) GetUserByEmail(ctx context.Context, email string) (*models.User, error) {
	args := m.Called(ctx, email)
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockEnhancedStore) UpdateUser(ctx context.Context, user *models.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockEnhancedStore) CreateUserProfile(ctx context.Context, profile *models.UserProfile) error {
	args := m.Called(ctx, profile)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetUserProfile(ctx context.Context, userID string) (*models.UserProfile, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*models.UserProfile), args.Error(1)
}

func (m *MockEnhancedStore) UpdateUserProfile(ctx context.Context, profile *models.UserProfile) error {
	args := m.Called(ctx, profile)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetUserWithProfile(ctx context.Context, userID string) (*models.UserWithProfile, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*models.UserWithProfile), args.Error(1)
}

func (m *MockEnhancedStore) CreateRefreshToken(ctx context.Context, token *models.RefreshToken) error {
	args := m.Called(ctx, token)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetRefreshToken(ctx context.Context, tokenHash string) (*models.RefreshToken, error) {
	args := m.Called(ctx, tokenHash)
	return args.Get(0).(*models.RefreshToken), args.Error(1)
}

func (m *MockEnhancedStore) RevokeRefreshToken(ctx context.Context, tokenHash string) error {
	args := m.Called(ctx, tokenHash)
	return args.Error(0)
}

func (m *MockEnhancedStore) RevokeAllUserRefreshTokens(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetExams(ctx context.Context) ([]models.Exam, error) {
	args := m.Called(ctx)
	return args.Get(0).([]models.Exam), args.Error(1)
}

func (m *MockEnhancedStore) GetExamByID(ctx context.Context, id int) (*models.Exam, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Exam), args.Error(1)
}

func (m *MockEnhancedStore) GetTopics(ctx context.Context) ([]models.Topic, error) {
	args := m.Called(ctx)
	return args.Get(0).([]models.Topic), args.Error(1)
}

func (m *MockEnhancedStore) GetTopicByID(ctx context.Context, id int) (*models.Topic, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Topic), args.Error(1)
}

func (m *MockEnhancedStore) CreateUserPreference(ctx context.Context, preference *models.UserPreference) error {
	args := m.Called(ctx, preference)
	return args.Error(0)
}

func (m *MockEnhancedStore) CreateTest(ctx context.Context, test *models.Test) error {
	args := m.Called(ctx, test)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetTestByID(ctx context.Context, id string) (*models.Test, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Test), args.Error(1)
}

func (m *MockEnhancedStore) GetTestsByUserID(ctx context.Context, userID string) ([]models.Test, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.Test), args.Error(1)
}

func (m *MockEnhancedStore) UpdateTestStatus(ctx context.Context, testID string, status models.TestStatus) error {
	args := m.Called(ctx, testID, status)
	return args.Error(0)
}

func (m *MockEnhancedStore) CompleteTest(ctx context.Context, testID string) error {
	args := m.Called(ctx, testID)
	return args.Error(0)
}

func (m *MockEnhancedStore) CreateTestQuestion(ctx context.Context, testQuestion *models.TestQuestion) error {
	args := m.Called(ctx, testQuestion)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetTestQuestions(ctx context.Context, testID string) ([]models.TestQuestion, error) {
	args := m.Called(ctx, testID)
	return args.Get(0).([]models.TestQuestion), args.Error(1)
}

func (m *MockEnhancedStore) GetTestQuestionByOrder(ctx context.Context, testID string, questionOrder int) (*models.TestQuestionWithDetails, error) {
	args := m.Called(ctx, testID, questionOrder)
	return args.Get(0).(*models.TestQuestionWithDetails), args.Error(1)
}

func (m *MockEnhancedStore) GetQuestionByID(ctx context.Context, id string) (*models.Question, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Question), args.Error(1)
}

func (m *MockEnhancedStore) GetQuestionsByTopicID(ctx context.Context, topicID int) ([]models.Question, error) {
	args := m.Called(ctx, topicID)
	return args.Get(0).([]models.Question), args.Error(1)
}

func (m *MockEnhancedStore) GetQuestionsByDifficulty(ctx context.Context, difficulty models.DifficultyLevel) ([]models.Question, error) {
	args := m.Called(ctx, difficulty)
	return args.Get(0).([]models.Question), args.Error(1)
}

func (m *MockEnhancedStore) CreateUserAnswer(ctx context.Context, answer *models.UserAnswer) error {
	args := m.Called(ctx, answer)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetUserAnswer(ctx context.Context, testQuestionID int) (*models.UserAnswer, error) {
	args := m.Called(ctx, testQuestionID)
	return args.Get(0).(*models.UserAnswer), args.Error(1)
}

func (m *MockEnhancedStore) GetUserAnswersByTestID(ctx context.Context, testID string) ([]models.UserAnswer, error) {
	args := m.Called(ctx, testID)
	return args.Get(0).([]models.UserAnswer), args.Error(1)
}

func (m *MockEnhancedStore) GetTestQuestionID(ctx context.Context, testID string, questionID string) (int, error) {
	args := m.Called(ctx, testID, questionID)
	return args.Int(0), args.Error(1)
}

func (m *MockEnhancedStore) Ping(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

// AdaptiveLearningRepository methods
func (m *MockEnhancedStore) GetAdaptiveLearningEngine(ctx context.Context, userID uuid.UUID) (*models.AdaptiveLearningEngine, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*models.AdaptiveLearningEngine), args.Error(1)
}

func (m *MockEnhancedStore) UpdateAdaptiveLearningEngine(ctx context.Context, engine *models.AdaptiveLearningEngine) error {
	args := m.Called(ctx, engine)
	return args.Error(0)
}

func (m *MockEnhancedStore) CreateAdaptiveLearningEngine(ctx context.Context, engine *models.AdaptiveLearningEngine) error {
	args := m.Called(ctx, engine)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetMasteryLevel(ctx context.Context, userID uuid.UUID, topicID int) (*models.MasteryLevel, error) {
	args := m.Called(ctx, userID, topicID)
	return args.Get(0).(*models.MasteryLevel), args.Error(1)
}

func (m *MockEnhancedStore) UpdateMasteryLevel(ctx context.Context, userID uuid.UUID, mastery *models.MasteryLevel) error {
	args := m.Called(ctx, userID, mastery)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetMasteryLevelsByUser(ctx context.Context, userID uuid.UUID) ([]models.MasteryLevel, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.MasteryLevel), args.Error(1)
}

func (m *MockEnhancedStore) GetUserResponsesByTopic(ctx context.Context, userID uuid.UUID, topicID int, days int) ([]models.AdaptiveResponse, error) {
	args := m.Called(ctx, userID, topicID, days)
	return args.Get(0).([]models.AdaptiveResponse), args.Error(1)
}

func (m *MockEnhancedStore) CreateAdaptiveResponse(ctx context.Context, response *models.AdaptiveResponse) error {
	args := m.Called(ctx, response)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetLearningPath(ctx context.Context, pathID uuid.UUID) (*models.LearningPath, error) {
	args := m.Called(ctx, pathID)
	return args.Get(0).(*models.LearningPath), args.Error(1)
}

func (m *MockEnhancedStore) CreateLearningPath(ctx context.Context, path *models.LearningPath) error {
	args := m.Called(ctx, path)
	return args.Error(0)
}

func (m *MockEnhancedStore) UpdateLearningPath(ctx context.Context, path *models.LearningPath) error {
	args := m.Called(ctx, path)
	return args.Error(0)
}

// AnalyticsRepository methods
func (m *MockEnhancedStore) CreateTestResult(ctx context.Context, result *models.TestResult) error {
	args := m.Called(ctx, result)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetTestResult(ctx context.Context, testID string) (*models.TestResult, error) {
	args := m.Called(ctx, testID)
	return args.Get(0).(*models.TestResult), args.Error(1)
}

func (m *MockEnhancedStore) GetUserTestHistory(ctx context.Context, userID string, limit int) ([]models.UserTestHistory, error) {
	args := m.Called(ctx, userID, limit)
	return args.Get(0).([]models.UserTestHistory), args.Error(1)
}

func (m *MockEnhancedStore) GetTopicPerformance(ctx context.Context, userID string, topicID int) (*models.TopicPerformanceSummary, error) {
	args := m.Called(ctx, userID, topicID)
	return args.Get(0).(*models.TopicPerformanceSummary), args.Error(1)
}

func (m *MockEnhancedStore) GetUserTopicPerformances(ctx context.Context, userID string) ([]models.TopicPerformanceWithName, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.TopicPerformanceWithName), args.Error(1)
}

func (m *MockEnhancedStore) GetWeakestTopics(ctx context.Context, userID string, limit int) ([]models.TopicPerformanceWithName, error) {
	args := m.Called(ctx, userID, limit)
	return args.Get(0).([]models.TopicPerformanceWithName), args.Error(1)
}

func (m *MockEnhancedStore) GetDashboardSummary(ctx context.Context, userID string) (*models.DashboardSummary, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*models.DashboardSummary), args.Error(1)
}

func (m *MockEnhancedStore) GetPerformanceTrends(ctx context.Context, userID string, timeFilter models.TimeFilter) ([]models.PerformanceTrend, error) {
	args := m.Called(ctx, userID, timeFilter)
	return args.Get(0).([]models.PerformanceTrend), args.Error(1)
}

func (m *MockEnhancedStore) GetPerformanceTrendsWithDateRange(ctx context.Context, userID string, timeFilter models.TimeFilter, dateRange *models.DateRange) ([]models.PerformanceTrend, error) {
	args := m.Called(ctx, userID, timeFilter, dateRange)
	return args.Get(0).([]models.PerformanceTrend), args.Error(1)
}

func (m *MockEnhancedStore) GetHeatmapData(ctx context.Context, userID string) ([]models.HeatmapData, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.HeatmapData), args.Error(1)
}

func (m *MockEnhancedStore) GetSubjectComparisons(ctx context.Context, userID string) ([]models.SubjectComparison, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.SubjectComparison), args.Error(1)
}

func (m *MockEnhancedStore) GetStudyPatterns(ctx context.Context, userID string) (*models.StudyPattern, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*models.StudyPattern), args.Error(1)
}

func (m *MockEnhancedStore) GetUserInsights(ctx context.Context, userID string, limit int) ([]models.AnalyticsInsight, error) {
	args := m.Called(ctx, userID, limit)
	return args.Get(0).([]models.AnalyticsInsight), args.Error(1)
}

func (m *MockEnhancedStore) CreateInsight(ctx context.Context, insight *models.AnalyticsInsight) error {
	args := m.Called(ctx, insight)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetTimeFilteredTestHistory(ctx context.Context, userID string, timeFilter models.TimeFilter) ([]models.UserTestHistory, error) {
	args := m.Called(ctx, userID, timeFilter)
	return args.Get(0).([]models.UserTestHistory), args.Error(1)
}

func (m *MockEnhancedStore) GetTimeFilteredTestHistoryWithDateRange(ctx context.Context, userID string, timeFilter models.TimeFilter, dateRange *models.DateRange) ([]models.UserTestHistory, error) {
	args := m.Called(ctx, userID, timeFilter, dateRange)
	return args.Get(0).([]models.UserTestHistory), args.Error(1)
}

// Spaced Repetition methods
func (m *MockEnhancedStore) CreateSpacedRepetitionSchedule(ctx context.Context, schedule *models.SpacedRepetitionSchedule) error {
	args := m.Called(ctx, schedule)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetSpacedRepetitionSchedule(ctx context.Context, userID uuid.UUID, topicID int) (*models.SpacedRepetitionSchedule, error) {
	args := m.Called(ctx, userID, topicID)
	return args.Get(0).(*models.SpacedRepetitionSchedule), args.Error(1)
}

func (m *MockEnhancedStore) UpdateSpacedRepetitionSchedule(ctx context.Context, schedule *models.SpacedRepetitionSchedule) error {
	args := m.Called(ctx, schedule)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetDueReviews(ctx context.Context, userID uuid.UUID) ([]models.SpacedRepetitionSchedule, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.SpacedRepetitionSchedule), args.Error(1)
}

// IRT Parameters methods
func (m *MockEnhancedStore) GetQuestionIRTParameters(ctx context.Context, questionID uuid.UUID) (*models.IRTParameters, error) {
	args := m.Called(ctx, questionID)
	return args.Get(0).(*models.IRTParameters), args.Error(1)
}

func (m *MockEnhancedStore) UpdateQuestionIRTParameters(ctx context.Context, questionID uuid.UUID, params *models.IRTParameters) error {
	args := m.Called(ctx, questionID, params)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetUserAbilityEstimate(ctx context.Context, userID uuid.UUID, subjectID int) (float64, float64, error) {
	args := m.Called(ctx, userID, subjectID)
	return args.Get(0).(float64), args.Get(1).(float64), args.Error(2)
}

func (m *MockEnhancedStore) UpdateUserAbilityEstimate(ctx context.Context, userID uuid.UUID, subjectID int, ability, confidence float64) error {
	args := m.Called(ctx, userID, subjectID, ability, confidence)
	return args.Error(0)
}

// Additional missing methods
func (m *MockEnhancedStore) DeleteLearningPath(ctx context.Context, pathID uuid.UUID) error {
	args := m.Called(ctx, pathID)
	return args.Error(0)
}

func (m *MockEnhancedStore) GetUserResponsesByTimeRange(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) ([]models.AdaptiveResponse, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Get(0).([]models.AdaptiveResponse), args.Error(1)
}

func (m *MockEnhancedStore) GetRecentResponses(ctx context.Context, userID uuid.UUID, limit int) ([]models.AdaptiveResponse, error) {
	args := m.Called(ctx, userID, limit)
	return args.Get(0).([]models.AdaptiveResponse), args.Error(1)
}

func (m *MockEnhancedStore) GetLearningPathsByUser(ctx context.Context, userID uuid.UUID) ([]models.LearningPath, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.LearningPath), args.Error(1)
}

func (m *MockEnhancedStore) GetMasteryLevelsBySubject(ctx context.Context, userID uuid.UUID, subjectID int) ([]models.MasteryLevel, error) {
	args := m.Called(ctx, userID, subjectID)
	return args.Get(0).([]models.MasteryLevel), args.Error(1)
}

func (m *MockEnhancedStore) GetSubjectByID(ctx context.Context, id int) (*models.Subject, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Subject), args.Error(1)
}

func (m *MockEnhancedStore) GetUserPreferences(ctx context.Context, userID string) ([]models.UserPreference, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.UserPreference), args.Error(1)
}

// Helper function to create test questions
func createTestQuestions(count int) []models.Question {
	questions := make([]models.Question, count)
	for i := 0; i < count; i++ {
		questions[i] = models.Question{
			ID:      uuid.New(),
			TopicID: 1,
			Content: []byte(`{"question":"Test question","options":["A","B","C","D"],"correct_option_index":0,"explanation":"Test explanation"}`),
			Difficulty: func() *models.DifficultyLevel {
				d := models.DifficultyMedium
				return &d
			}(),
			CreatedAt: time.Now(),
		}
	}
	return questions
}

func TestGetQuestionsFromDatabase_SmartSelection(t *testing.T) {
	mockStore := new(MockEnhancedStore)
	service := &TestService{store: mockStore}

	ctx := context.Background()
	userID := uuid.New()
	topicIDs := []int{1, 2}
	req := &models.CreateTestRequest{
		NumQuestions:     5,
		DifficultyLevels: []models.DifficultyLevel{models.DifficultyMedium},
	}

	expectedQuestions := createTestQuestions(5)

	// Mock the enhanced methods
	mockStore.On("GetQuestionPoolSize", ctx, topicIDs, req.DifficultyLevels).Return(10, nil)
	mockStore.On("GetDiverseQuestions", ctx, userID.String(), topicIDs, req.DifficultyLevels, 5).Return(expectedQuestions, nil)

	questions, err := service.getQuestionsFromDatabase(ctx, userID, topicIDs, req)

	assert.NoError(t, err)
	assert.Len(t, questions, 5)
	mockStore.AssertExpectations(t)
}

func TestGetQuestionsFromDatabase_FallbackToSmartRandom(t *testing.T) {
	mockStore := new(MockEnhancedStore)
	service := &TestService{store: mockStore}

	ctx := context.Background()
	userID := uuid.New()
	topicIDs := []int{1, 2}
	req := &models.CreateTestRequest{
		NumQuestions:     5,
		DifficultyLevels: []models.DifficultyLevel{models.DifficultyMedium},
	}

	expectedQuestions := createTestQuestions(5)

	// Mock diverse questions to fail, then smart random to succeed
	mockStore.On("GetQuestionPoolSize", ctx, topicIDs, req.DifficultyLevels).Return(10, nil)
	mockStore.On("GetDiverseQuestions", ctx, userID.String(), topicIDs, req.DifficultyLevels, 5).Return([]models.Question{}, assert.AnError)
	mockStore.On("GetSmartRandomQuestions", ctx, userID.String(), topicIDs, req.DifficultyLevels, 5, 7).Return(expectedQuestions, nil)

	questions, err := service.getQuestionsFromDatabase(ctx, userID, topicIDs, req)

	assert.NoError(t, err)
	assert.Len(t, questions, 5)
	mockStore.AssertExpectations(t)
}
