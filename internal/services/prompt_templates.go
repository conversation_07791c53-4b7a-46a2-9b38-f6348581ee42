package services

import (
	"fmt"
	"strings"

	"test-spark-backend/internal/models"
)

// PromptTemplate represents a structured prompt template for question generation
type PromptTemplate struct {
	SystemPrompt        string
	ContextSection      string
	RequirementsSection string
	AccuracySection     string
	GradeSection        string
	ValidationSection   string
	OutputFormat        string
	FinalInstructions   string
}

// QuestionGenerationContext contains all context needed for question generation
type QuestionGenerationContext struct {
	TopicIDs         []int // Actual topic IDs from database
	TopicNames       []string
	Difficulties     []models.DifficultyLevel
	NumQuestions     int
	SubjectName      string
	ExamName         string
	Grade            string // Student's grade/class
	Board            string // Educational board (CBSE, ICSE, etc.)
	EducationalLevel string // Primary, Secondary, Higher Secondary
	SessionID        string // Unique session identifier for variety
}

// PromptTemplateBuilder builds sophisticated prompts with accuracy guarantees
type PromptTemplateBuilder struct {
	template *PromptTemplate
}

// NewPromptTemplateBuilder creates a new prompt template builder
func NewPromptTemplateBuilder() *PromptTemplateBuilder {
	return &PromptTemplateBuilder{
		template: &PromptTemplate{},
	}
}

// BuildEnhancedPrompt creates a comprehensive prompt with accuracy focus
func (ptb *PromptTemplateBuilder) BuildEnhancedPrompt(ctx *QuestionGenerationContext) string {
	ptb.buildSystemPrompt()
	ptb.buildContextSection(ctx)
	ptb.buildRequirementsSection(ctx)
	ptb.buildAccuracySection()
	ptb.buildGradeSpecificSection(ctx)
	ptb.buildValidationSection()
	ptb.buildOutputFormat()
	ptb.buildFinalInstructions(ctx)
	prompt := ptb.assemblePrompt()
	fmt.Println(prompt)
	return ptb.assemblePrompt()
}

// buildSystemPrompt creates the core system prompt with expertise definition
func (ptb *PromptTemplateBuilder) buildSystemPrompt() {
	ptb.template.SystemPrompt = `You are an expert educational content creator and question generator with the following qualifications:
- PhD in Education with specialization in assessment design
- 15+ years experience in creating standardized test questions
- Expert knowledge in cognitive learning theory and Bloom's taxonomy
- Certified in educational measurement and psychometrics
- Deep understanding of age-appropriate learning standards

Your primary responsibility is to generate educationally sound, 100% accurate multiple choice questions that test genuine understanding rather than rote memorization.`
}

// buildContextSection creates the context section with subject and exam details
func (ptb *PromptTemplateBuilder) buildContextSection(ctx *QuestionGenerationContext) {
	var contextParts []string

	if ctx.SubjectName != "" {
		contextParts = append(contextParts, fmt.Sprintf("Subject: %s", ctx.SubjectName))
	}
	if ctx.ExamName != "" {
		contextParts = append(contextParts, fmt.Sprintf("Exam/Assessment Type: %s", ctx.ExamName))
	}
	if ctx.Grade != "" {
		contextParts = append(contextParts, fmt.Sprintf("Student Grade/Class: %s", ctx.Grade))
	}
	if ctx.Board != "" {
		contextParts = append(contextParts, fmt.Sprintf("Educational Board: %s", ctx.Board))
	}
	if ctx.EducationalLevel != "" {
		contextParts = append(contextParts, fmt.Sprintf("Educational Level: %s", ctx.EducationalLevel))
	}

	topicsStr := strings.Join(ctx.TopicNames, ", ")
	contextParts = append(contextParts, fmt.Sprintf("Topics: %s", topicsStr))

	difficultyStr := formatDifficulties(ctx.Difficulties)
	contextParts = append(contextParts, fmt.Sprintf("Difficulty Levels: %s", difficultyStr))

	if ctx.SessionID != "" {
		contextParts = append(contextParts, fmt.Sprintf("Generation Session: %s", ctx.SessionID))
	}

	if len(contextParts) > 0 {
		ptb.template.ContextSection = "EDUCATIONAL CONTEXT:\n" + strings.Join(contextParts, "\n") + "\n"
	}
}

// buildRequirementsSection creates detailed requirements for question generation
func (ptb *PromptTemplateBuilder) buildRequirementsSection(ctx *QuestionGenerationContext) {
	ptb.template.RequirementsSection = fmt.Sprintf(`GENERATION REQUIREMENTS:
- Generate exactly %d multiple choice questions
- Each question must have exactly 4 options (A, B, C, D)
- Only ONE option should be correct per question
- Questions must test conceptual understanding, not just recall
- Use age-appropriate language and complexity for the specified grade
- Align with educational standards for the given board and level
- Ensure questions are culturally neutral and inclusive
- Vary the position of correct answers across questions (don't always make A or B correct)
- Include real-world applications where appropriate for the grade level

UNIQUENESS AND RANDOMIZATION REQUIREMENTS:
- Generate completely unique questions that haven't been asked before
- Use diverse question formats (direct questions, scenario-based, calculation-based, analytical, comparative, etc.)
- Vary the approach to testing the same concept across different questions
- Ensure each question explores different aspects or applications of the topics
- Use different contexts, examples, and scenarios for similar concepts
- Avoid repetitive question patterns or similar wording structures
- Create fresh, original questions for each generation session
- Use varied numerical values, names, scenarios, and contexts to ensure uniqueness
- Incorporate randomization elements:
  * Random numerical values within realistic ranges
  * Different character names, company names, location names
  * Varied scenarios (real-world applications, case studies, hypothetical situations)
  * Different question stems and phrasings for the same concept
  * Mixed difficulty progression within the same difficulty level
  * Randomized order of answer options
- Session ID for this generation: %s (use this to ensure variety from previous sessions)
- Generate questions that test understanding from multiple angles:
  * Conceptual understanding
  * Practical application
  * Problem-solving
  * Analysis and synthesis
  * Critical thinking`, ctx.NumQuestions, ctx.SessionID)
}

// buildAccuracySection creates strict accuracy requirements with enhanced validation
func (ptb *PromptTemplateBuilder) buildAccuracySection() {
	ptb.template.AccuracySection = `CRITICAL ACCURACY REQUIREMENTS:
🎯 ANSWER ACCURACY IS PARAMOUNT - 100% correctness is mandatory

MULTI-LAYER VALIDATION PROCESS:
1. FACTUAL VERIFICATION:
   - Verify each correct answer through multiple authoritative sources
   - Cross-check all numerical values, formulas, and calculations
   - Ensure all scientific facts align with current accepted knowledge
   - Validate historical dates, events, and figures
   - Confirm geographical information and statistical data

2. LOGICAL CONSISTENCY:
   - Ensure the correct answer logically follows from the question
   - Verify that incorrect options are definitively wrong, not just "less correct"
   - Check that explanations provide sound reasoning
   - Ensure no circular logic or unsupported claims

3. EDUCATIONAL APPROPRIATENESS:
   - Confirm content aligns with specified grade-level standards
   - Verify terminology is age-appropriate and clearly defined
   - Ensure concepts build on prerequisite knowledge for the grade
   - Check that complexity matches cognitive development expectations

4. DISTRACTOR QUALITY (Incorrect Options):
   - Make distractors plausible but clearly incorrect
   - Avoid options that are partially correct or could be debated
   - Ensure distractors test common misconceptions or errors
   - Verify no distractor could be considered correct under any interpretation

5. EXPLANATION VALIDATION:
   - Provide clear, step-by-step reasoning for the correct answer
   - Explain why each incorrect option is wrong
   - Use appropriate educational terminology for the grade level
   - Include relevant concepts, principles, or formulas where applicable

MANDATORY QUALITY ASSURANCE CHECKLIST:
Before finalizing each question, verify:
✓ Correct answer is 100% accurate and unambiguous
✓ All three incorrect options are definitively wrong
✓ Question tests meaningful understanding, not just memorization
✓ Language and complexity appropriate for specified grade
✓ No grammatical, spelling, or factual errors
✓ Explanation provides clear educational value
✓ Content aligns with curriculum standards
✓ Question promotes learning and understanding

ACCURACY VERIFICATION METHODS:
- Cross-reference with multiple authoritative educational sources
- Apply the "expert review" test: would a subject matter expert agree?
- Use the "teaching moment" test: does this question create learning value?
- Apply the "no ambiguity" test: is there only one defensible correct answer?

ANTI-REPETITION AND DIVERSITY MEASURES:
- Ensure no two questions test the exact same knowledge point in the same way
- Use different question formats even when covering similar topics
- Vary the complexity and depth of questions within the same difficulty level
- Create questions that complement each other rather than duplicate content
- Use different examples, scenarios, and contexts for each question
- Avoid using the same keywords or phrases repeatedly across questions
- Implement variety in question stems, answer choices, and explanations
- Test different cognitive levels (recall, comprehension, application, analysis)
- Use diverse real-world applications and contemporary examples
- Ensure each question adds unique educational value to the set`
}

// buildGradeSpecificSection creates grade-appropriate content guidelines
func (ptb *PromptTemplateBuilder) buildGradeSpecificSection(ctx *QuestionGenerationContext) {
	if ctx.Grade == "" {
		return
	}

	gradeGuidelines := ptb.getGradeSpecificGuidelines(ctx.Grade, ctx.EducationalLevel)
	boardGuidelines := ptb.getBoardSpecificGuidelines(ctx.Board)

	var sectionContent strings.Builder
	sectionContent.WriteString("EDUCATIONAL STANDARDS & GUIDELINES:\n")

	if gradeGuidelines != "" {
		sectionContent.WriteString(fmt.Sprintf("\nGRADE-SPECIFIC GUIDELINES (%s):\n%s\n", ctx.Grade, gradeGuidelines))
	}

	if boardGuidelines != "" {
		sectionContent.WriteString(fmt.Sprintf("\nBOARD-SPECIFIC GUIDELINES (%s):\n%s\n", ctx.Board, boardGuidelines))
	}

	// Add cognitive development guidelines
	cognitiveGuidelines := ptb.getCognitiveGuidelines(ctx.EducationalLevel)
	if cognitiveGuidelines != "" {
		sectionContent.WriteString(fmt.Sprintf("\nCOGNITIVE DEVELOPMENT GUIDELINES (%s):\n%s\n", ctx.EducationalLevel, cognitiveGuidelines))
	}

	ptb.template.GradeSection = sectionContent.String()
}

// getGradeSpecificGuidelines returns guidelines based on grade level
func (ptb *PromptTemplateBuilder) getGradeSpecificGuidelines(grade, level string) string {
	// Parse grade to determine appropriate guidelines
	gradeNum := ptb.parseGradeNumber(grade)

	switch {
	case gradeNum >= 1 && gradeNum <= 5: // Primary
		return `- Use simple, concrete language and concepts
- Focus on fundamental concepts and basic applications
- Include visual or hands-on thinking scenarios
- Avoid abstract or complex theoretical concepts
- Use familiar, everyday examples and contexts`

	case gradeNum >= 6 && gradeNum <= 8: // Middle School
		return `- Introduce more abstract thinking while maintaining clarity
- Build on fundamental concepts with moderate complexity
- Include some analytical and application-based questions
- Use age-appropriate examples and scenarios
- Begin introducing multi-step reasoning`

	case gradeNum >= 9 && gradeNum <= 10: // Secondary
		return `- Use grade-appropriate academic vocabulary
- Include analytical and synthesis-level questions
- Focus on conceptual understanding and application
- Introduce complex problem-solving scenarios
- Align with board-specific curriculum standards`

	case gradeNum >= 11 && gradeNum <= 12: // Higher Secondary
		return `- Use advanced academic language and concepts
- Include high-level analytical and evaluative questions
- Focus on critical thinking and complex applications
- Prepare for competitive exams and higher education
- Include interdisciplinary connections where appropriate`

	default:
		return `- Adapt language and complexity to the specified grade level
- Ensure content aligns with age-appropriate learning objectives
- Focus on grade-relevant curriculum standards`
	}
}

// parseGradeNumber extracts numeric grade from grade string
func (ptb *PromptTemplateBuilder) parseGradeNumber(grade string) int {
	// Handle common grade formats: "5", "Class 5", "Grade 5", "5th", etc.
	grade = strings.ToLower(strings.TrimSpace(grade))

	// Extract number from various formats
	for i := 1; i <= 12; i++ {
		gradeStr := fmt.Sprintf("%d", i)
		if strings.Contains(grade, gradeStr) {
			return i
		}
	}

	return 0 // Unknown grade
}

// buildValidationSection creates comprehensive validation requirements
func (ptb *PromptTemplateBuilder) buildValidationSection() {
	ptb.template.ValidationSection = `COMPREHENSIVE VALIDATION PROTOCOL:

PHASE 1 - CONTENT VALIDATION:
1. FACTUAL ACCURACY: Is every fact, figure, and statement verifiably correct?
2. ANSWER CERTAINTY: Is the correct answer indisputably the only right choice?
3. DISTRACTOR VALIDITY: Are all incorrect options clearly and definitively wrong?
4. SOURCE VERIFICATION: Can the answer be confirmed by authoritative sources?

PHASE 2 - EDUCATIONAL VALIDATION:
5. GRADE APPROPRIATENESS: Does the content match the specified grade level?
6. CURRICULUM ALIGNMENT: Does the question align with educational standards?
7. LEARNING OBJECTIVE: Does the question test meaningful understanding?
8. COGNITIVE LEVEL: Is the thinking level appropriate for the grade?

PHASE 3 - LINGUISTIC VALIDATION:
9. CLARITY: Is the question clearly worded without ambiguity?
10. VOCABULARY: Is the language appropriate for the target grade level?
11. GRAMMAR: Are there any grammatical or spelling errors?
12. READABILITY: Can students at this grade level understand the question?

PHASE 4 - STRUCTURAL VALIDATION:
13. FORMAT COMPLIANCE: Does the question follow the required JSON structure?
14. OPTION BALANCE: Are all four options of similar length and complexity?
15. ANSWER DISTRIBUTION: Is the correct answer position varied across questions?
16. EXPLANATION QUALITY: Does the explanation clearly teach the concept?

CRITICAL FAILURE POINTS - REJECT QUESTION IF:
❌ Any factual information is incorrect or questionable
❌ The correct answer could be debated or has multiple interpretations
❌ Any incorrect option could be considered partially correct
❌ The question tests only memorization without understanding
❌ The language is too advanced or too simple for the grade level
❌ The explanation doesn't clearly justify the correct answer

VALIDATION DECISION TREE:
For each question, ask: "Would I stake my professional reputation on this question's accuracy?"
- If YES: Proceed to next validation phase
- If NO or UNCERTAIN: Revise or reject the question

Remember: It's better to generate fewer, perfectly accurate questions than many questionable ones.`
}

// buildOutputFormat creates the JSON output format specification
func (ptb *PromptTemplateBuilder) buildOutputFormat() {
	ptb.template.OutputFormat = `OUTPUT FORMAT:
You must respond with ONLY a valid JSON array. No additional text, explanations, or formatting outside the JSON.

Required JSON structure:
[
  {
    "question": "Clear, well-formed question text ending with a question mark?",
    "options": [
      "First choice text only",
      "Second choice text only",
      "Third choice text only",
      "Fourth choice text only"
    ],
    "correct_option_index": 0,
    "explanation": "Comprehensive explanation that clearly explains why the correct answer is right and why the other options are wrong. Include relevant concepts and reasoning."
  }
]

CRITICAL: The correct_option_index must correspond exactly to the position of the correct answer in the options array (0=A, 1=B, 2=C, 3=D).`
}

// buildFinalInstructions creates the final generation instructions
func (ptb *PromptTemplateBuilder) buildFinalInstructions(ctx *QuestionGenerationContext) {
	varietyPrompt := ""
	if ctx.SessionID != "" {
		varietyPrompt = fmt.Sprintf("\nIMPORTANT: This is generation session %s. Ensure these questions are completely different from any previous generations. Use creative approaches, varied contexts, and diverse question styles.", ctx.SessionID[:8])
	}

	ptb.template.FinalInstructions = fmt.Sprintf(`FINAL INSTRUCTIONS:
Generate exactly %d high-quality, educationally sound multiple choice questions following all the above requirements.%s

Remember: ACCURACY IS NON-NEGOTIABLE. Every answer must be 100%% correct and verifiable.

Begin generation now:`, ctx.NumQuestions, varietyPrompt)
}

// assemblePrompt combines all sections into the final prompt
func (ptb *PromptTemplateBuilder) assemblePrompt() string {
	sections := []string{
		ptb.template.SystemPrompt,
		"",
		ptb.template.ContextSection,
		ptb.template.RequirementsSection,
		"",
		ptb.template.AccuracySection,
		"",
		ptb.template.GradeSection,
		ptb.template.ValidationSection,
		"",
		ptb.template.OutputFormat,
		"",
		ptb.template.FinalInstructions,
	}

	return strings.Join(sections, "\n")
}

// getBoardSpecificGuidelines returns guidelines based on educational board
func (ptb *PromptTemplateBuilder) getBoardSpecificGuidelines(board string) string {
	if board == "" {
		return ""
	}

	boardLower := strings.ToLower(board)

	switch {
	case strings.Contains(boardLower, "cbse"):
		return `- Follow CBSE curriculum framework and learning outcomes
- Align with NCERT textbook standards and concepts
- Use terminology consistent with CBSE examination patterns
- Focus on conceptual understanding and application-based learning
- Include questions that prepare for CBSE board examinations`

	case strings.Contains(boardLower, "icse"):
		return `- Follow ICSE curriculum standards and Council guidelines
- Align with prescribed ICSE textbooks and syllabi
- Use detailed explanations and comprehensive coverage
- Focus on analytical thinking and detailed understanding
- Include questions that match ICSE examination style`

	case strings.Contains(boardLower, "ib"):
		return `- Follow IB curriculum framework and assessment criteria
- Emphasize international perspectives and global contexts
- Focus on inquiry-based learning and critical thinking
- Include interdisciplinary connections where appropriate
- Use IB command terms and assessment language`

	case strings.Contains(boardLower, "state"):
		return `- Follow state board curriculum guidelines
- Use locally relevant examples and contexts
- Align with state-specific textbooks and standards
- Focus on regional educational objectives
- Include culturally appropriate content`

	default:
		return `- Follow standard educational curriculum guidelines
- Use universally accepted educational principles
- Focus on fundamental concepts and skills
- Ensure content is educationally sound and appropriate`
	}
}

// getCognitiveGuidelines returns cognitive development guidelines based on educational level
func (ptb *PromptTemplateBuilder) getCognitiveGuidelines(level string) string {
	switch strings.ToLower(level) {
	case "primary":
		return `- Use concrete examples and visual thinking
- Focus on basic skills and foundational concepts
- Encourage exploration and discovery learning
- Use simple, clear language and short sentences
- Include hands-on and experiential learning elements`

	case "middle school":
		return `- Bridge concrete and abstract thinking
- Introduce logical reasoning and problem-solving
- Encourage questioning and hypothesis formation
- Use age-appropriate complexity and vocabulary
- Include collaborative and social learning elements`

	case "secondary":
		return `- Develop abstract thinking and analytical skills
- Focus on critical thinking and evaluation
- Encourage independent reasoning and judgment
- Use academic vocabulary and complex concepts
- Include real-world applications and connections`

	case "higher secondary":
		return `- Emphasize advanced analytical and synthesis skills
- Focus on evaluation, creation, and innovation
- Encourage metacognitive thinking and reflection
- Use sophisticated academic language and concepts
- Include preparation for higher education and careers`

	default:
		return `- Use age-appropriate cognitive challenges
- Focus on meaningful learning and understanding
- Encourage active thinking and engagement
- Use clear and appropriate language for the level`
	}
}

// formatDifficulties converts difficulty levels to a readable string
func formatDifficulties(difficulties []models.DifficultyLevel) string {
	var diffStrs []string
	for _, diff := range difficulties {
		diffStrs = append(diffStrs, string(diff))
	}
	return strings.Join(diffStrs, ", ")
}
