package services

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
	"test-spark-backend/internal/types"

	"github.com/google/uuid"
)

// CerebrasProvider implements the AIProvider interface for Cerebras AI
type CerebrasProvider struct {
	config     *types.CerebrasProviderConfig
	httpClient *http.Client
	store      database.Store
}

// NewCerebrasProvider creates a new Cerebras AI provider
func NewCerebrasProvider(config *types.CerebrasProviderConfig, store database.Store) *CerebrasProvider {
	// Create HTTP client with proper TLS configuration
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			RootCAs:            nil,   // Use system default
			InsecureSkipVerify: false, // Enable certificate verification
			MinVersion:         tls.VersionTLS12,
			MaxVersion:         tls.VersionTLS13,
			ServerName:         "api.cerebras.ai", // Cerebras server name
		},
		// Connection settings for better reliability
		DisableKeepAlives:     false,
		DisableCompression:    false,
		MaxIdleConns:          10,
		MaxIdleConnsPerHost:   2,
		MaxConnsPerHost:       10,
		IdleConnTimeout:       90 * time.Second,
		ResponseHeaderTimeout: 60 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	httpClient := &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.TimeoutSeconds) * time.Second,
	}

	return &CerebrasProvider{
		config:     config,
		httpClient: httpClient,
		store:      store,
	}
}

// GenerateQuestions implements AIProvider.GenerateQuestions
func (cp *CerebrasProvider) GenerateQuestions(ctx context.Context, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	// Build the enhanced prompt
	builder := NewPromptTemplateBuilder()
	prompt := builder.BuildEnhancedPrompt(genCtx)

	// Make API request
	responseContent, err := cp.makeAPIRequest(ctx, prompt)
	if err != nil {
		return nil, fmt.Errorf("failed to make API request: %w", err)
	}

	// Parse the response
	questions, err := cp.parseQuestionsFromResponse(responseContent, genCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to parse questions: %w", err)
	}

	return questions, nil
}

// GetProviderName returns the name of this provider
func (cp *CerebrasProvider) GetProviderName() string {
	return "cerebras"
}

// IsAvailable checks if the Cerebras provider is available
func (cp *CerebrasProvider) IsAvailable(ctx context.Context) bool {
	if cp.config == nil || cp.config.APIKey == "" {
		return false
	}

	// Quick health check - try to make a minimal request
	return cp.healthCheck(ctx)
}

// GetCapabilities returns the capabilities of the Cerebras provider
func (cp *CerebrasProvider) GetCapabilities() types.ProviderCapabilities {
	return types.ProviderCapabilities{
		MaxQuestionsPerRequest: 50,
		SupportedDifficulties:  []string{"easy", "medium", "hard"},
		SupportsGradeLevel:     true,
		SupportsBoard:          true,
		MaxTokens:              cp.config.MaxTokens,
		SupportsStreaming:      false,
	}
}

// makeAPIRequest makes a request to the Cerebras API
func (cp *CerebrasProvider) makeAPIRequest(ctx context.Context, prompt string) (string, error) {
	// Prepare the request payload
	requestBody := map[string]interface{}{
		"model": cp.config.Model,
		"messages": []map[string]string{
			{
				"role":    "user",
				"content": prompt,
			},
		},
		"temperature": cp.config.Temperature,
		"max_tokens":  cp.config.MaxTokens,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create the HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", cp.config.BaseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+cp.config.APIKey)

	// Make the request
	resp, err := cp.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// Read the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	// Check for HTTP errors
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse the response
	var response struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
		Error *struct {
			Message string `json:"message"`
			Type    string `json:"type"`
		} `json:"error"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("failed to parse response: %w", err)
	}

	if response.Error != nil {
		return "", fmt.Errorf("API error: %s", response.Error.Message)
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("no response choices returned")
	}

	return response.Choices[0].Message.Content, nil
}

// healthCheck performs a quick health check
func (cp *CerebrasProvider) healthCheck(ctx context.Context) bool {
	// Create a minimal request to check if the service is available
	requestBody := map[string]interface{}{
		"model": cp.config.Model,
		"messages": []map[string]string{
			{
				"role":    "user",
				"content": "Hello",
			},
		},
		"max_tokens": 1,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return false
	}

	req, err := http.NewRequestWithContext(ctx, "POST", cp.config.BaseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return false
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+cp.config.APIKey)

	resp, err := cp.httpClient.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

// parseQuestionsFromResponse parses the AI response into Question objects
func (cp *CerebrasProvider) parseQuestionsFromResponse(responseContent string, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	// Clean the response content
	cleanedContent := strings.TrimSpace(responseContent)

	// Try to extract JSON from the response
	jsonStart := strings.Index(cleanedContent, "[")
	jsonEnd := strings.LastIndex(cleanedContent, "]")

	if jsonStart == -1 || jsonEnd == -1 || jsonStart >= jsonEnd {
		return nil, fmt.Errorf("no valid JSON array found in response")
	}

	jsonContent := cleanedContent[jsonStart : jsonEnd+1]

	// Parse the JSON
	var rawQuestions []map[string]interface{}
	if err := json.Unmarshal([]byte(jsonContent), &rawQuestions); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	var questions []models.Question
	providerName := cp.GetProviderName()

	for i, rawQuestion := range rawQuestions {
		if i >= genCtx.NumQuestions {
			break // Don't exceed requested number
		}

		// Convert to our Question format
		questionContent := map[string]interface{}{
			"question": rawQuestion["question"],
			"options":  rawQuestion["options"],
			"answer":   rawQuestion["answer"],
		}

		// Add explanation if present
		if explanation, exists := rawQuestion["explanation"]; exists {
			questionContent["explanation"] = explanation
		}

		// Determine difficulty
		difficulty := cp.selectDifficultyForQuestion(i, genCtx)

		contentBytes, _ := json.Marshal(questionContent)

		question := models.Question{
			ID:            uuid.New(),
			TopicID:       cp.selectTopicForQuestion(i, genCtx),
			Content:       contentBytes,
			Difficulty:    &difficulty,
			AuthorAIModel: &providerName,
			CreatedAt:     time.Now(),
		}

		questions = append(questions, question)
	}

	return questions, nil
}

// Helper methods for topic and difficulty selection
func (cp *CerebrasProvider) selectTopicForQuestion(questionIndex int, genCtx *QuestionGenerationContext) int {
	// Use actual topic IDs from the context
	if len(genCtx.TopicIDs) == 0 {
		return 1 // Default topic ID as fallback
	}

	// Distribute topics cyclically using actual topic IDs
	return genCtx.TopicIDs[questionIndex%len(genCtx.TopicIDs)]
}

func (cp *CerebrasProvider) selectDifficultyForQuestion(questionIndex int, genCtx *QuestionGenerationContext) models.DifficultyLevel {
	if len(genCtx.Difficulties) == 0 {
		return models.DifficultyMedium
	}
	return genCtx.Difficulties[questionIndex%len(genCtx.Difficulties)]
}
