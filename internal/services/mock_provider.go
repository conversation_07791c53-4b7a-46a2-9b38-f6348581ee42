package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"test-spark-backend/internal/models"
	"test-spark-backend/internal/types"

	"github.com/google/uuid"
)

// MockProvider implements the AIProvider interface for testing and fallback
type MockProvider struct {
	name         string
	isAvailable  bool
	shouldFail   bool
	capabilities types.ProviderCapabilities
}

// NewMockProvider creates a new mock AI provider
func NewMockProvider(name string) *MockProvider {
	return &MockProvider{
		name:        name,
		isAvailable: true,
		shouldFail:  false,
		capabilities: types.ProviderCapabilities{
			MaxQuestionsPerRequest: 20,
			SupportedDifficulties:  []string{"easy", "medium", "hard"},
			SupportsGradeLevel:     true,
			SupportsBoard:          true,
			MaxTokens:              2000,
			SupportsStreaming:      false,
		},
	}
}

// GenerateQuestions implements AIProvider.GenerateQuestions
func (mp *MockProvider) GenerateQuestions(ctx context.Context, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	if mp.shouldFail {
		return nil, &types.AIProviderError{
			Message:  "mock provider configured to fail",
			Provider: mp.name,
			Code:     "MOCK_FAILURE",
		}
	}

	var questions []models.Question

	for i := 0; i < genCtx.NumQuestions; i++ {
		// Select topic and difficulty cyclically
		topicName := "General"
		if len(genCtx.TopicNames) > 0 {
			topicName = genCtx.TopicNames[i%len(genCtx.TopicNames)]
		}

		difficulty := models.DifficultyMedium
		if len(genCtx.Difficulties) > 0 {
			difficulty = genCtx.Difficulties[i%len(genCtx.Difficulties)]
		}

		// Create mock question content based on context
		questionContent := mp.generateMockQuestionContent(i+1, topicName, difficulty, genCtx)

		// Convert to JSON
		contentBytes, err := json.Marshal(questionContent)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal mock question content: %w", err)
		}

		// Use actual topic IDs if available, otherwise fallback to mock IDs
		topicID := i%5 + 1 // Default mock topic IDs 1-5
		if len(genCtx.TopicIDs) > 0 {
			topicID = genCtx.TopicIDs[i%len(genCtx.TopicIDs)]
		}

		question := models.Question{
			ID:            uuid.New(),
			TopicID:       topicID,
			Content:       contentBytes,
			Difficulty:    &difficulty,
			AuthorAIModel: &mp.name,
			CreatedAt:     time.Now(),
		}

		questions = append(questions, question)
	}

	return questions, nil
}

// generateMockQuestionContent creates realistic mock question content
func (mp *MockProvider) generateMockQuestionContent(questionNum int, topicName string, difficulty models.DifficultyLevel, genCtx *QuestionGenerationContext) models.QuestionContent {
	// Create grade-appropriate content
	gradeLevel := mp.determineGradeLevel(genCtx.Grade)

	var question string
	var options []string
	var explanation string
	correctIndex := questionNum % 4 // Vary correct answer position

	switch difficulty {
	case models.DifficultyEasy:
		question = fmt.Sprintf("[%s] What is a fundamental concept in %s? (Question %d)",
			string(difficulty), topicName, questionNum)
		options = []string{
			"This is an incorrect option A",
			"This is an incorrect option B",
			"This is an incorrect option C",
			"This is an incorrect option D",
		}
		options[correctIndex] = fmt.Sprintf("This is the correct answer for %s", topicName)
		explanation = fmt.Sprintf("This is a mock explanation for an %s level question about %s. The correct answer demonstrates understanding of fundamental concepts appropriate for %s level students.",
			string(difficulty), topicName, gradeLevel)

	case models.DifficultyMedium:
		question = fmt.Sprintf("[%s] Which statement best describes the relationship between concepts in %s? (Question %d)",
			string(difficulty), topicName, questionNum)
		options = []string{
			"This relationship is partially correct but incomplete",
			"This relationship is incorrect due to missing context",
			"This relationship is oversimplified",
			"This relationship is fundamentally flawed",
		}
		options[correctIndex] = fmt.Sprintf("This correctly describes the relationship in %s", topicName)
		explanation = fmt.Sprintf("This is a mock explanation for a %s level question about %s. The correct answer requires analytical thinking and understanding of relationships between concepts, appropriate for %s level students.",
			string(difficulty), topicName, gradeLevel)

	case models.DifficultyHard:
		question = fmt.Sprintf("[%s] Analyze the complex scenario in %s: What would be the most appropriate approach? (Question %d)",
			string(difficulty), topicName, questionNum)
		options = []string{
			"This approach has significant limitations",
			"This approach is theoretically sound but impractical",
			"This approach addresses symptoms but not root causes",
			"This approach is outdated and no longer applicable",
		}
		options[correctIndex] = fmt.Sprintf("This is the most comprehensive approach for %s", topicName)
		explanation = fmt.Sprintf("This is a mock explanation for a %s level question about %s. The correct answer requires critical thinking, synthesis of multiple concepts, and evaluation of complex scenarios, appropriate for %s level students.",
			string(difficulty), topicName, gradeLevel)
	}

	// Add context-specific information if available
	if genCtx.SubjectName != "" {
		explanation += fmt.Sprintf(" This question aligns with %s curriculum standards.", genCtx.SubjectName)
	}
	if genCtx.Board != "" {
		explanation += fmt.Sprintf(" Content follows %s board guidelines.", genCtx.Board)
	}

	return models.QuestionContent{
		Question:           question,
		Options:            options,
		CorrectOptionIndex: correctIndex,
		Explanation:        explanation,
	}
}

// determineGradeLevel determines the grade level description
func (mp *MockProvider) determineGradeLevel(grade string) string {
	if grade == "" {
		return "general"
	}

	// Simple grade level determination
	builder := NewPromptTemplateBuilder()
	gradeNum := builder.parseGradeNumber(grade)

	switch {
	case gradeNum >= 1 && gradeNum <= 5:
		return "primary"
	case gradeNum >= 6 && gradeNum <= 8:
		return "middle school"
	case gradeNum >= 9 && gradeNum <= 10:
		return "secondary"
	case gradeNum >= 11 && gradeNum <= 12:
		return "higher secondary"
	default:
		return "general"
	}
}

// GetProviderName implements AIProvider.GetProviderName
func (mp *MockProvider) GetProviderName() string {
	return mp.name
}

// IsAvailable implements AIProvider.IsAvailable
func (mp *MockProvider) IsAvailable(ctx context.Context) bool {
	return mp.isAvailable
}

// GetCapabilities implements AIProvider.GetCapabilities
func (mp *MockProvider) GetCapabilities() types.ProviderCapabilities {
	return mp.capabilities
}

// SetAvailable sets the availability status (for testing)
func (mp *MockProvider) SetAvailable(available bool) {
	mp.isAvailable = available
}

// SetShouldFail sets whether the provider should fail (for testing)
func (mp *MockProvider) SetShouldFail(shouldFail bool) {
	mp.shouldFail = shouldFail
}

// SetCapabilities sets the provider capabilities (for testing)
func (mp *MockProvider) SetCapabilities(capabilities types.ProviderCapabilities) {
	mp.capabilities = capabilities
}

// FallbackProvider creates a mock provider specifically for fallback scenarios
func FallbackProvider() *MockProvider {
	provider := NewMockProvider("fallback-mock")
	provider.capabilities = types.ProviderCapabilities{
		MaxQuestionsPerRequest: 10,
		SupportedDifficulties:  []string{"easy", "medium"},
		SupportsGradeLevel:     true,
		SupportsBoard:          false,
		MaxTokens:              1000,
		SupportsStreaming:      false,
	}
	return provider
}
