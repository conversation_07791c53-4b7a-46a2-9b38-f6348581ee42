package services

import (
	"context"
	"fmt"
	"math"
	"time"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
)

// PatternRecognitionService analyzes user behavior patterns
type PatternRecognitionService struct {
	store database.Store
}

// NewPatternRecognitionService creates a new pattern recognition service
func NewPatternRecognitionService(store database.Store) *PatternRecognitionService {
	return &PatternRecognitionService{
		store: store,
	}
}

// UserBehaviorPattern represents comprehensive user behavior analysis
type UserBehaviorPattern struct {
	UserID                string                      `json:"user_id"`
	StudyHabits           StudyHabitPattern           `json:"study_habits"`
	PerformancePatterns   PerformancePattern          `json:"performance_patterns"`
	DifficultyPreferences DifficultyPreferencePattern `json:"difficulty_preferences"`
	LearningVelocity      LearningVelocityPattern     `json:"learning_velocity"`
	TimePatterns          TimePattern                 `json:"time_patterns"`
	SubjectAffinities     []SubjectAffinityPattern    `json:"subject_affinities"`
	PredictedOutcomes     PredictedOutcome            `json:"predicted_outcomes"`
}

// StudyHabitPattern analyzes study consistency and habits
type StudyHabitPattern struct {
	AverageSessionLength float64   `json:"average_session_length"` // minutes
	PreferredStudyTimes  []int     `json:"preferred_study_times"`  // hours of day
	StudyFrequency       float64   `json:"study_frequency"`        // sessions per week
	ConsistencyScore     float64   `json:"consistency_score"`      // 0-100
	OptimalSessionLength float64   `json:"optimal_session_length"` // minutes for best performance
	BreakPatterns        []float64 `json:"break_patterns"`         // typical break lengths
	WeeklyDistribution   [7]int    `json:"weekly_distribution"`    // tests per day of week
	MonthlyTrend         float64   `json:"monthly_trend"`          // increasing/decreasing activity
}

// PerformancePattern analyzes performance trends and patterns
type PerformancePattern struct {
	AverageScore           float64                      `json:"average_score"`
	ScoreVariability       float64                      `json:"score_variability"`        // standard deviation
	ImprovementRate        float64                      `json:"improvement_rate"`         // score improvement per week
	PerformanceStability   float64                      `json:"performance_stability"`    // consistency of scores
	PeakPerformanceTimes   []int                        `json:"peak_performance_times"`   // hours when performance is best
	SubjectPerformanceGaps map[string]float64           `json:"subject_performance_gaps"` // performance difference between subjects
	DifficultyProgression  DifficultyProgressionPattern `json:"difficulty_progression"`
	ErrorPatterns          []ErrorPattern               `json:"error_patterns"`
}

// DifficultyPreferencePattern analyzes user's difficulty preferences
type DifficultyPreferencePattern struct {
	PreferredDifficulty    string             `json:"preferred_difficulty"`    // easy, medium, hard
	DifficultyDistribution map[string]float64 `json:"difficulty_distribution"` // percentage of time spent on each
	DifficultyPerformance  map[string]float64 `json:"difficulty_performance"`  // average score by difficulty
	OptimalDifficulty      string             `json:"optimal_difficulty"`      // difficulty with best learning outcomes
	DifficultyProgression  float64            `json:"difficulty_progression"`  // rate of moving to harder questions
	ComfortZone            string             `json:"comfort_zone"`            // difficulty user tends to stay in
}

// LearningVelocityPattern analyzes how quickly user learns
type LearningVelocityPattern struct {
	TopicMasterySpeed    map[string]float64     `json:"topic_mastery_speed"`   // days to reach proficiency by topic
	ConceptRetention     float64                `json:"concept_retention"`     // how well concepts are retained over time
	LearningAcceleration float64                `json:"learning_acceleration"` // rate of learning improvement
	PlateauDetection     []PlateauPeriod        `json:"plateau_detection"`     // periods of stagnant progress
	BreakthroughMoments  []BreakthroughMoment   `json:"breakthrough_moments"`  // significant improvement events
	ForgettingCurve      ForgettingCurvePattern `json:"forgetting_curve"`      // how quickly user forgets without practice
}

// TimePattern analyzes temporal patterns in user behavior
type TimePattern struct {
	OptimalStudyHours    []int                 `json:"optimal_study_hours"`     // best hours for studying
	ProductivityCycles   []ProductivityCycle   `json:"productivity_cycles"`     // daily/weekly productivity patterns
	SeasonalPatterns     map[string]float64    `json:"seasonal_patterns"`       // performance by month/season
	WeekendVsWeekday     WeekendWeekdayPattern `json:"weekend_vs_weekday"`      // different patterns for weekends
	TimeOfDayPerformance [24]float64           `json:"time_of_day_performance"` // performance by hour
}

// Supporting types
type SubjectAffinityPattern struct {
	SubjectName     string  `json:"subject_name"`
	AffinityScore   float64 `json:"affinity_score"`   // 0-100, how much user likes/performs in subject
	LearningSpeed   float64 `json:"learning_speed"`   // relative speed compared to other subjects
	RetentionRate   float64 `json:"retention_rate"`   // how well knowledge is retained
	EngagementLevel float64 `json:"engagement_level"` // time spent relative to other subjects
}

type DifficultyProgressionPattern struct {
	StartingLevel    string  `json:"starting_level"`
	CurrentLevel     string  `json:"current_level"`
	ProgressionRate  float64 `json:"progression_rate"`   // levels per month
	ReadinessForNext float64 `json:"readiness_for_next"` // 0-100, ready for next difficulty
}

type ErrorPattern struct {
	ErrorType        string   `json:"error_type"`        // conceptual, computational, careless
	Frequency        float64  `json:"frequency"`         // how often this error occurs
	TopicsAffected   []string `json:"topics_affected"`   // which topics show this error
	ImprovementTrend float64  `json:"improvement_trend"` // getting better/worse at avoiding
}

type PlateauPeriod struct {
	StartDate          time.Time `json:"start_date"`
	EndDate            time.Time `json:"end_date"`
	Duration           int       `json:"duration"` // days
	SubjectsAffected   []string  `json:"subjects_affected"`
	BreakthroughAction string    `json:"breakthrough_action"` // what helped break through
}

type BreakthroughMoment struct {
	Date             time.Time `json:"date"`
	Subject          string    `json:"subject"`
	Topic            string    `json:"topic"`
	ScoreImprovement float64   `json:"score_improvement"`
	TriggerEvent     string    `json:"trigger_event"` // what caused the breakthrough
}

type ForgettingCurvePattern struct {
	InitialRetention      float64 `json:"initial_retention"`       // retention after 1 day
	WeeklyRetention       float64 `json:"weekly_retention"`        // retention after 1 week
	MonthlyRetention      float64 `json:"monthly_retention"`       // retention after 1 month
	OptimalReviewInterval int     `json:"optimal_review_interval"` // days between reviews
}

type ProductivityCycle struct {
	CycleType     string  `json:"cycle_type"`     // daily, weekly, monthly
	PeakTimes     []int   `json:"peak_times"`     // when productivity is highest
	LowTimes      []int   `json:"low_times"`      // when productivity is lowest
	CycleStrength float64 `json:"cycle_strength"` // how pronounced the cycle is
}

type WeekendWeekdayPattern struct {
	WeekdayPerformance float64 `json:"weekday_performance"`
	WeekendPerformance float64 `json:"weekend_performance"`
	WeekdayFrequency   float64 `json:"weekday_frequency"` // sessions per weekday
	WeekendFrequency   float64 `json:"weekend_frequency"` // sessions per weekend day
	PreferredPattern   string  `json:"preferred_pattern"` // weekday, weekend, or balanced
}

type PredictedOutcome struct {
	NextWeekScore      float64  `json:"next_week_score"`     // predicted average score
	MonthlyImprovement float64  `json:"monthly_improvement"` // expected improvement
	RiskFactors        []string `json:"risk_factors"`        // factors that might hurt performance
	OpportunityAreas   []string `json:"opportunity_areas"`   // areas with high improvement potential
	RecommendedActions []string `json:"recommended_actions"` // specific actions to take
	ConfidenceLevel    float64  `json:"confidence_level"`    // 0-100, confidence in predictions
}

// AnalyzeUserBehaviorPatterns performs comprehensive pattern analysis
func (prs *PatternRecognitionService) AnalyzeUserBehaviorPatterns(ctx context.Context, userID string) (*UserBehaviorPattern, error) {
	// Gather all necessary data
	recentTests, err := prs.store.GetUserTestHistory(ctx, userID, 50) // Get more data for pattern analysis
	if err != nil {
		return nil, fmt.Errorf("failed to get test history: %w", err)
	}

	topicPerformances, err := prs.store.GetUserTopicPerformances(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get topic performances: %w", err)
	}

	studyPatterns, err := prs.store.GetStudyPatterns(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get study patterns: %w", err)
	}

	// Analyze different pattern types
	pattern := &UserBehaviorPattern{
		UserID: userID,
	}

	pattern.StudyHabits = prs.analyzeStudyHabits(recentTests, studyPatterns)
	pattern.PerformancePatterns = prs.analyzePerformancePatterns(recentTests, topicPerformances)
	pattern.DifficultyPreferences = prs.analyzeDifficultyPreferences(recentTests)
	pattern.LearningVelocity = prs.analyzeLearningVelocity(recentTests, topicPerformances)
	pattern.TimePatterns = prs.analyzeTimePatterns(recentTests)
	pattern.SubjectAffinities = prs.analyzeSubjectAffinities(topicPerformances)
	pattern.PredictedOutcomes = prs.generatePredictions(pattern)

	return pattern, nil
}

// analyzeStudyHabits analyzes user's study consistency and habits
func (prs *PatternRecognitionService) analyzeStudyHabits(tests []models.UserTestHistory, patterns *models.StudyPattern) StudyHabitPattern {
	habit := StudyHabitPattern{
		AverageSessionLength: float64(patterns.AverageSessionTime),
		StudyFrequency:       patterns.WeeklyTestCount,
		ConsistencyScore:     patterns.ConsistencyScore,
	}

	// Analyze weekly distribution
	weeklyDist := [7]int{}
	for _, test := range tests {
		if test.CompletedAt != nil {
			weekday := test.CompletedAt.Weekday()
			weeklyDist[weekday]++
		}
	}
	habit.WeeklyDistribution = weeklyDist

	// Calculate optimal session length based on performance
	sessionPerformance := make(map[int][]float64) // session length -> scores
	for _, test := range tests {
		if test.Score != nil && test.TimeTakenSeconds != nil {
			sessionLength := *test.TimeTakenSeconds / 60  // convert to minutes
			if sessionLength > 0 && sessionLength < 300 { // reasonable session length
				sessionPerformance[sessionLength] = append(sessionPerformance[sessionLength], *test.Score)
			}
		}
	}

	// Find optimal session length
	bestLength := 30.0 // default
	bestScore := 0.0
	for length, scores := range sessionPerformance {
		if len(scores) >= 3 { // need enough data points
			avgScore := average(scores)
			if avgScore > bestScore {
				bestScore = avgScore
				bestLength = float64(length)
			}
		}
	}
	habit.OptimalSessionLength = bestLength

	return habit
}

// analyzePerformancePatterns analyzes performance trends and patterns
func (prs *PatternRecognitionService) analyzePerformancePatterns(tests []models.UserTestHistory, topics []models.TopicPerformanceWithName) PerformancePattern {
	pattern := PerformancePattern{
		SubjectPerformanceGaps: make(map[string]float64),
	}

	// Calculate basic statistics
	scores := make([]float64, 0, len(tests))
	for _, test := range tests {
		if test.Score != nil {
			scores = append(scores, *test.Score)
		}
	}

	if len(scores) > 0 {
		pattern.AverageScore = average(scores)
		pattern.ScoreVariability = standardDeviation(scores)
		pattern.PerformanceStability = 100 - pattern.ScoreVariability // higher is more stable
	}

	// Calculate improvement rate
	if len(scores) >= 5 {
		recentScores := scores[:5]            // last 5 tests
		olderScores := scores[len(scores)-5:] // first 5 tests
		recentAvg := average(recentScores)
		olderAvg := average(olderScores)

		// Calculate weekly improvement rate
		weeksSpan := float64(len(tests)) / 7.0 // approximate weeks
		if weeksSpan > 0 {
			pattern.ImprovementRate = (recentAvg - olderAvg) / weeksSpan
		}
	}

	// Analyze subject performance gaps
	subjectScores := make(map[string][]float64)
	for _, topic := range topics {
		subjectScores[topic.SubjectName] = append(subjectScores[topic.SubjectName], topic.ProficiencyScore)
	}

	subjectAvgs := make(map[string]float64)
	for subject, scores := range subjectScores {
		subjectAvgs[subject] = average(scores)
	}

	// Calculate gaps from best subject
	var bestScore float64
	for _, avg := range subjectAvgs {
		if avg > bestScore {
			bestScore = avg
		}
	}

	for subject, avg := range subjectAvgs {
		pattern.SubjectPerformanceGaps[subject] = bestScore - avg
	}

	return pattern
}

// Helper functions
func average(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}
	sum := 0.0
	for _, v := range values {
		sum += v
	}
	return sum / float64(len(values))
}

func standardDeviation(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}
	avg := average(values)
	sumSquares := 0.0
	for _, v := range values {
		diff := v - avg
		sumSquares += diff * diff
	}
	return math.Sqrt(sumSquares / float64(len(values)))
}

// Placeholder implementations for other analysis methods
func (prs *PatternRecognitionService) analyzeDifficultyPreferences(tests []models.UserTestHistory) DifficultyPreferencePattern {
	return DifficultyPreferencePattern{
		PreferredDifficulty: "medium",
		DifficultyDistribution: map[string]float64{
			"easy":   30.0,
			"medium": 50.0,
			"hard":   20.0,
		},
	}
}

func (prs *PatternRecognitionService) analyzeLearningVelocity(tests []models.UserTestHistory, topics []models.TopicPerformanceWithName) LearningVelocityPattern {
	return LearningVelocityPattern{
		ConceptRetention:     75.0,
		LearningAcceleration: 1.2,
	}
}

func (prs *PatternRecognitionService) analyzeTimePatterns(tests []models.UserTestHistory) TimePattern {
	return TimePattern{
		OptimalStudyHours: []int{9, 10, 14, 15, 19, 20},
	}
}

func (prs *PatternRecognitionService) analyzeSubjectAffinities(topics []models.TopicPerformanceWithName) []SubjectAffinityPattern {
	subjectMap := make(map[string][]float64)
	for _, topic := range topics {
		subjectMap[topic.SubjectName] = append(subjectMap[topic.SubjectName], topic.ProficiencyScore)
	}

	var affinities []SubjectAffinityPattern
	for subject, scores := range subjectMap {
		affinity := SubjectAffinityPattern{
			SubjectName:     subject,
			AffinityScore:   average(scores),
			LearningSpeed:   1.0,
			RetentionRate:   80.0,
			EngagementLevel: 75.0,
		}
		affinities = append(affinities, affinity)
	}

	return affinities
}

func (prs *PatternRecognitionService) generatePredictions(pattern *UserBehaviorPattern) PredictedOutcome {
	return PredictedOutcome{
		NextWeekScore:      pattern.PerformancePatterns.AverageScore + pattern.PerformancePatterns.ImprovementRate,
		MonthlyImprovement: pattern.PerformancePatterns.ImprovementRate * 4,
		RiskFactors:        []string{"Inconsistent practice", "Avoiding difficult topics"},
		OpportunityAreas:   []string{"Mathematics problem-solving", "Physics concepts"},
		RecommendedActions: []string{"Increase practice frequency", "Focus on weak topics"},
		ConfidenceLevel:    75.0,
	}
}
