package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
	"test-spark-backend/internal/types"

	"github.com/google/uuid"
)

// OpenAIProvider implements the AIProvider interface for OpenAI
type OpenAIProvider struct {
	config     *types.OpenAIProviderConfig
	httpClient *http.Client
	store      database.Store
}

// OpenAIRequest represents the request structure for OpenAI API
type OpenAIRequest struct {
	Model       string          `json:"model"`
	Messages    []OpenAIMessage `json:"messages"`
	Temperature float64         `json:"temperature"`
	MaxTokens   int             `json:"max_tokens"`
}

// OpenAIMessage represents a message in the OpenAI conversation
type OpenAIMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// OpenAIResponse represents the response structure from OpenAI API
type OpenAIResponse struct {
	Choices []OpenAIChoice `json:"choices"`
	Error   *OpenAIError   `json:"error,omitempty"`
	Usage   *OpenAIUsage   `json:"usage,omitempty"`
}

// OpenAIChoice represents a choice in the OpenAI response
type OpenAIChoice struct {
	Message OpenAIMessage `json:"message"`
}

// OpenAIError represents an error from OpenAI API
type OpenAIError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code"`
}

// OpenAIUsage represents token usage information
type OpenAIUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// NewOpenAIProvider creates a new OpenAI provider
func NewOpenAIProvider(config *types.OpenAIProviderConfig, store database.Store) *OpenAIProvider {
	baseURL := config.BaseURL
	if baseURL == "" {
		baseURL = "https://api.openai.com/v1/chat/completions"
	}

	return &OpenAIProvider{
		config: &types.OpenAIProviderConfig{
			APIKey:      config.APIKey,
			Model:       config.Model,
			Temperature: config.Temperature,
			MaxTokens:   config.MaxTokens,
			BaseURL:     baseURL,
			OrgID:       config.OrgID,
		},
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		store: store,
	}
}

// GenerateQuestions implements AIProvider.GenerateQuestions
func (oai *OpenAIProvider) GenerateQuestions(ctx context.Context, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	// Build the enhanced prompt
	builder := NewPromptTemplateBuilder()
	prompt := builder.BuildEnhancedPrompt(genCtx)

	// Make API request
	responseContent, err := oai.makeAPIRequest(ctx, prompt)
	if err != nil {
		return nil, fmt.Errorf("failed to make API request: %w", err)
	}

	// Parse the response
	questions, err := oai.parseQuestionsFromResponse(responseContent, genCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to parse questions: %w", err)
	}

	return questions, nil
}

// GetProviderName implements AIProvider.GetProviderName
func (oai *OpenAIProvider) GetProviderName() string {
	return "openai"
}

// IsAvailable implements AIProvider.IsAvailable
func (oai *OpenAIProvider) IsAvailable(ctx context.Context) bool {
	if oai.config.APIKey == "" {
		return false
	}

	// TODO: Implement health check endpoint call
	// For now, just check if we have the required configuration
	return oai.config.BaseURL != "" && oai.config.Model != ""
}

// GetCapabilities implements AIProvider.GetCapabilities
func (oai *OpenAIProvider) GetCapabilities() types.ProviderCapabilities {
	return types.ProviderCapabilities{
		MaxQuestionsPerRequest: 50,
		SupportedDifficulties:  []string{"easy", "medium", "hard"},
		SupportsGradeLevel:     true,
		SupportsBoard:          true,
		MaxTokens:              oai.config.MaxTokens,
		SupportsStreaming:      true,
	}
}

// makeAPIRequest makes the actual API request to OpenAI
func (oai *OpenAIProvider) makeAPIRequest(ctx context.Context, prompt string) (string, error) {
	request := OpenAIRequest{
		Model: oai.config.Model,
		Messages: []OpenAIMessage{
			{
				Role:    "user",
				Content: prompt,
			},
		},
		Temperature: oai.config.Temperature,
		MaxTokens:   oai.config.MaxTokens,
	}

	// Marshal request
	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", oai.config.BaseURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+oai.config.APIKey)
	if oai.config.OrgID != "" {
		req.Header.Set("OpenAI-Organization", oai.config.OrgID)
	}

	// Make request
	resp, err := oai.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var openaiResp OpenAIResponse
	if err := json.NewDecoder(resp.Body).Decode(&openaiResp); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	// Check for API errors
	if openaiResp.Error != nil {
		return "", &types.AIProviderError{
			Message:  openaiResp.Error.Message,
			Provider: "openai",
			Code:     openaiResp.Error.Code,
		}
	}

	// Check for empty response
	if len(openaiResp.Choices) == 0 {
		return "", &types.AIProviderError{
			Message:  "no choices in response",
			Provider: "openai",
			Code:     "EMPTY_RESPONSE",
		}
	}

	return openaiResp.Choices[0].Message.Content, nil
}

// parseQuestionsFromResponse parses the AI response into Question models
func (oai *OpenAIProvider) parseQuestionsFromResponse(responseContent string, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	// Clean the response content (remove any markdown formatting)
	responseContent = strings.TrimSpace(responseContent)
	responseContent = strings.TrimPrefix(responseContent, "```json")
	responseContent = strings.TrimSuffix(responseContent, "```")
	responseContent = strings.TrimSpace(responseContent)

	// Parse JSON response
	var questionContents []models.QuestionContent
	if err := json.Unmarshal([]byte(responseContent), &questionContents); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	// Convert to Question models
	var questions []models.Question
	for i, content := range questionContents {
		// Validate question content
		if err := oai.validateQuestionContent(&content); err != nil {
			return nil, fmt.Errorf("invalid question %d: %w", i+1, err)
		}

		// Convert to JSON for storage
		contentBytes, err := json.Marshal(content)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal question content %d: %w", i+1, err)
		}

		// Determine topic and difficulty for this question
		topicID := oai.selectTopicForQuestion(i, genCtx)
		difficulty := oai.selectDifficultyForQuestion(i, genCtx)

		question := models.Question{
			ID:            uuid.New(),
			TopicID:       topicID,
			Content:       contentBytes,
			Difficulty:    &difficulty,
			AuthorAIModel: &oai.config.Model,
			CreatedAt:     time.Now(),
		}

		questions = append(questions, question)
	}

	return questions, nil
}

// selectTopicForQuestion selects an appropriate topic for a question
func (oai *OpenAIProvider) selectTopicForQuestion(questionIndex int, genCtx *QuestionGenerationContext) int {
	// For now, distribute topics cyclically
	// TODO: Implement smarter topic selection based on question content
	if len(genCtx.TopicNames) == 0 {
		return 1 // Default topic ID
	}

	return questionIndex%len(genCtx.TopicNames) + 1
}

// selectDifficultyForQuestion selects an appropriate difficulty for a question
func (oai *OpenAIProvider) selectDifficultyForQuestion(questionIndex int, genCtx *QuestionGenerationContext) models.DifficultyLevel {
	if len(genCtx.Difficulties) == 0 {
		return models.DifficultyMedium // Default difficulty
	}

	// Distribute difficulties cyclically
	return genCtx.Difficulties[questionIndex%len(genCtx.Difficulties)]
}

// validateQuestionContent validates the structure and content of a question
func (oai *OpenAIProvider) validateQuestionContent(content *models.QuestionContent) error {
	if content.Question == "" {
		return fmt.Errorf("question text is empty")
	}

	if len(content.Options) != 4 {
		return fmt.Errorf("question must have exactly 4 options, got %d", len(content.Options))
	}

	for i, option := range content.Options {
		if option == "" {
			return fmt.Errorf("option %d is empty", i+1)
		}
	}

	if content.CorrectOptionIndex < 0 || content.CorrectOptionIndex >= len(content.Options) {
		return fmt.Errorf("correct_option_index %d is out of range", content.CorrectOptionIndex)
	}

	if content.Explanation == "" {
		return fmt.Errorf("explanation is empty")
	}

	return nil
}
