package services

import (
	"context"
	"fmt"
	"sort"
	"time"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
)

// RecommendationEngine provides personalized recommendations
type RecommendationEngine struct {
	store          database.Store
	patternService *PatternRecognitionService
	insightEngine  *InsightEngine
}

// NewRecommendationEngine creates a new recommendation engine
func NewRecommendationEngine(store database.Store) *RecommendationEngine {
	return &RecommendationEngine{
		store:          store,
		patternService: NewPatternRecognitionService(store),
		insightEngine:  NewInsightEngine(store),
	}
}

// RecommendationType represents different types of recommendations
type RecommendationType string

const (
	TopicRecommendation      RecommendationType = "topic"
	DifficultyRecommendation RecommendationType = "difficulty"
	ScheduleRecommendation   RecommendationType = "schedule"
	StrategyRecommendation   RecommendationType = "strategy"
	ReviewRecommendation     RecommendationType = "review"
	MotivationRecommendation RecommendationType = "motivation"
)

// RecommendationContext provides context for generating recommendations
type RecommendationContext struct {
	UserID             string
	CurrentPerformance *models.DashboardSummary
	BehaviorPatterns   *UserBehaviorPattern
	RecentTests        []models.UserTestHistory
	TopicPerformances  []models.TopicPerformanceWithName
	StudyPatterns      *models.StudyPattern
	TimeOfRequest      time.Time
	UserGoals          []UserGoal // Future: user-defined goals
}

// UserGoal represents a user's learning goal
type UserGoal struct {
	ID        string    `json:"id"`
	Type      string    `json:"type"`       // "score_improvement", "topic_mastery", "consistency"
	Target    float64   `json:"target"`     // target value
	Deadline  time.Time `json:"deadline"`   // when to achieve by
	Priority  int       `json:"priority"`   // 1-5, higher is more important
	SubjectID *int      `json:"subject_id"` // if goal is subject-specific
	TopicID   *int      `json:"topic_id"`   // if goal is topic-specific
}

// PersonalizedRecommendation represents a comprehensive recommendation
type PersonalizedRecommendation struct {
	ID                  string             `json:"id"`
	Type                RecommendationType `json:"type"`
	Title               string             `json:"title"`
	Description         string             `json:"description"`
	ActionItems         []ActionItem       `json:"action_items"`
	Priority            int                `json:"priority"`             // 1-5, higher is more urgent
	Confidence          float64            `json:"confidence"`           // 0-100, confidence in recommendation
	ExpectedImpact      float64            `json:"expected_impact"`      // 0-100, expected improvement
	TimeToSeeResults    int                `json:"time_to_see_results"`  // days
	Difficulty          string             `json:"difficulty"`           // easy, medium, hard to implement
	PersonalizationTags []string           `json:"personalization_tags"` // why this is personalized for user
	RelatedTopics       []int              `json:"related_topics"`       // topic IDs this affects
	RelatedSubjects     []string           `json:"related_subjects"`     // subject names this affects
	CreatedAt           time.Time          `json:"created_at"`
	ExpiresAt           *time.Time         `json:"expires_at"` // when recommendation becomes stale
}

// ActionItem represents a specific action within a recommendation
type ActionItem struct {
	ID            string     `json:"id"`
	Description   string     `json:"description"`
	Type          string     `json:"type"` // "practice", "review", "schedule", "strategy"
	Completed     bool       `json:"completed"`
	DueDate       *time.Time `json:"due_date"`
	EstimatedTime int        `json:"estimated_time"` // minutes
	Resources     []Resource `json:"resources"`      // helpful resources
}

// Resource represents a learning resource
type Resource struct {
	Type        string `json:"type"` // "video", "article", "practice_test", "flashcards"
	Title       string `json:"title"`
	URL         string `json:"url"`
	Description string `json:"description"`
	Duration    int    `json:"duration"` // minutes
}

// GeneratePersonalizedRecommendations creates comprehensive personalized recommendations
func (re *RecommendationEngine) GeneratePersonalizedRecommendations(ctx context.Context, userID string) ([]PersonalizedRecommendation, error) {
	// Gather comprehensive context
	context, err := re.gatherRecommendationContext(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to gather recommendation context: %w", err)
	}

	var recommendations []PersonalizedRecommendation

	// Generate different types of recommendations
	topicRecs := re.generateTopicRecommendations(context)
	recommendations = append(recommendations, topicRecs...)

	difficultyRecs := re.generateDifficultyRecommendations(context)
	recommendations = append(recommendations, difficultyRecs...)

	scheduleRecs := re.generateScheduleRecommendations(context)
	recommendations = append(recommendations, scheduleRecs...)

	strategyRecs := re.generateStrategyRecommendations(context)
	recommendations = append(recommendations, strategyRecs...)

	reviewRecs := re.generateReviewRecommendations(context)
	recommendations = append(recommendations, reviewRecs...)

	motivationRecs := re.generateMotivationRecommendations(context)
	recommendations = append(recommendations, motivationRecs...)

	// Sort by priority and confidence
	sort.Slice(recommendations, func(i, j int) bool {
		if recommendations[i].Priority != recommendations[j].Priority {
			return recommendations[i].Priority > recommendations[j].Priority
		}
		return recommendations[i].Confidence > recommendations[j].Confidence
	})

	// Limit to top 8 recommendations to avoid overwhelming user
	if len(recommendations) > 8 {
		recommendations = recommendations[:8]
	}

	return recommendations, nil
}

// gatherRecommendationContext collects all data needed for recommendations
func (re *RecommendationEngine) gatherRecommendationContext(ctx context.Context, userID string) (*RecommendationContext, error) {
	context := &RecommendationContext{
		UserID:        userID,
		TimeOfRequest: time.Now(),
	}

	// Get current performance
	dashboard, err := re.store.GetDashboardSummary(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get dashboard: %w", err)
	}
	context.CurrentPerformance = dashboard

	// Get behavior patterns
	patterns, err := re.patternService.AnalyzeUserBehaviorPatterns(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze behavior patterns: %w", err)
	}
	context.BehaviorPatterns = patterns

	// Get recent tests
	recentTests, err := re.store.GetUserTestHistory(ctx, userID, 20)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent tests: %w", err)
	}
	context.RecentTests = recentTests

	// Get topic performances
	topicPerformances, err := re.store.GetUserTopicPerformances(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get topic performances: %w", err)
	}
	context.TopicPerformances = topicPerformances

	// Get study patterns
	studyPatterns, err := re.store.GetStudyPatterns(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get study patterns: %w", err)
	}
	context.StudyPatterns = studyPatterns

	return context, nil
}

// generateTopicRecommendations suggests specific topics to focus on
func (re *RecommendationEngine) generateTopicRecommendations(ctx *RecommendationContext) []PersonalizedRecommendation {
	var recommendations []PersonalizedRecommendation

	// Find weakest topics that need attention
	weakTopics := re.findWeakestTopics(ctx.TopicPerformances, 3)

	for i, topic := range weakTopics {
		priority := 5 - i // Higher priority for weaker topics
		if topic.ProficiencyScore < 40 {
			priority = 5 // Critical priority
		}

		confidence := re.calculateTopicRecommendationConfidence(topic, ctx)
		expectedImpact := re.calculateExpectedImpact(topic.ProficiencyScore)

		rec := PersonalizedRecommendation{
			ID:               fmt.Sprintf("topic_%d_%d", topic.TopicID, time.Now().Unix()),
			Type:             TopicRecommendation,
			Title:            fmt.Sprintf("Master %s fundamentals", topic.TopicName),
			Description:      fmt.Sprintf("Your proficiency in %s is %.1f%%. Focused practice in this area will significantly improve your overall performance in %s.", topic.TopicName, topic.ProficiencyScore, topic.SubjectName),
			Priority:         priority,
			Confidence:       confidence,
			ExpectedImpact:   expectedImpact,
			TimeToSeeResults: re.estimateTimeToResults(topic.ProficiencyScore),
			Difficulty:       re.assessDifficulty(topic.ProficiencyScore),
			PersonalizationTags: []string{
				fmt.Sprintf("Based on your %.1f%% proficiency", topic.ProficiencyScore),
				"Identified as priority weakness",
				fmt.Sprintf("Will improve %s performance", topic.SubjectName),
			},
			RelatedTopics:   []int{topic.TopicID},
			RelatedSubjects: []string{topic.SubjectName},
			CreatedAt:       ctx.TimeOfRequest,
			ActionItems:     re.generateTopicActionItems(topic),
		}

		// Set expiration (recommendations become stale after 1 week)
		expiresAt := ctx.TimeOfRequest.Add(7 * 24 * time.Hour)
		rec.ExpiresAt = &expiresAt

		recommendations = append(recommendations, rec)
	}

	return recommendations
}

// generateDifficultyRecommendations suggests optimal difficulty levels
func (re *RecommendationEngine) generateDifficultyRecommendations(ctx *RecommendationContext) []PersonalizedRecommendation {
	var recommendations []PersonalizedRecommendation

	if ctx.BehaviorPatterns == nil {
		return recommendations
	}

	diffPref := ctx.BehaviorPatterns.DifficultyPreferences

	// Recommend difficulty progression if user is ready
	if diffPref.PreferredDifficulty == "easy" && ctx.CurrentPerformance.AverageScore > 75 {
		rec := PersonalizedRecommendation{
			ID:               fmt.Sprintf("difficulty_progression_%d", time.Now().Unix()),
			Type:             DifficultyRecommendation,
			Title:            "Ready for medium difficulty challenges",
			Description:      fmt.Sprintf("Your average score of %.1f%% shows you're ready to tackle medium difficulty questions. This will accelerate your learning.", ctx.CurrentPerformance.AverageScore),
			Priority:         3,
			Confidence:       85.0,
			ExpectedImpact:   15.0,
			TimeToSeeResults: 14,
			Difficulty:       "medium",
			PersonalizationTags: []string{
				"Based on your high performance in easy questions",
				"Optimal challenge level for growth",
			},
			CreatedAt: ctx.TimeOfRequest,
			ActionItems: []ActionItem{
				{
					ID:            "try_medium_questions",
					Description:   "Take a practice test with 70% medium difficulty questions",
					Type:          "practice",
					EstimatedTime: 30,
				},
				{
					ID:            "gradual_increase",
					Description:   "Gradually increase medium questions by 10% each week",
					Type:          "strategy",
					EstimatedTime: 5,
				},
			},
		}
		recommendations = append(recommendations, rec)
	}

	return recommendations
}

// generateScheduleRecommendations suggests optimal study schedules
func (re *RecommendationEngine) generateScheduleRecommendations(ctx *RecommendationContext) []PersonalizedRecommendation {
	var recommendations []PersonalizedRecommendation

	if ctx.StudyPatterns == nil {
		return recommendations
	}

	// Recommend consistency improvement if needed
	if ctx.StudyPatterns.ConsistencyScore < 60 {
		rec := PersonalizedRecommendation{
			ID:               fmt.Sprintf("schedule_consistency_%d", time.Now().Unix()),
			Type:             ScheduleRecommendation,
			Title:            "Establish a consistent study routine",
			Description:      fmt.Sprintf("Your consistency score is %.1f%%. Regular practice, even for short periods, is more effective than sporadic long sessions.", ctx.StudyPatterns.ConsistencyScore),
			Priority:         4,
			Confidence:       90.0,
			ExpectedImpact:   25.0,
			TimeToSeeResults: 21,
			Difficulty:       "easy",
			PersonalizationTags: []string{
				"Based on your current consistency patterns",
				"Scientifically proven to improve retention",
			},
			CreatedAt:   ctx.TimeOfRequest,
			ActionItems: re.generateScheduleActionItems(ctx.StudyPatterns),
		}
		recommendations = append(recommendations, rec)
	}

	return recommendations
}

// Helper functions

func (re *RecommendationEngine) findWeakestTopics(topics []models.TopicPerformanceWithName, limit int) []models.TopicPerformanceWithName {
	if len(topics) == 0 {
		return nil
	}

	// Sort by proficiency score (ascending)
	sorted := make([]models.TopicPerformanceWithName, len(topics))
	copy(sorted, topics)
	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i].ProficiencyScore < sorted[j].ProficiencyScore
	})

	if len(sorted) > limit {
		sorted = sorted[:limit]
	}

	return sorted
}

func (re *RecommendationEngine) calculateTopicRecommendationConfidence(topic models.TopicPerformanceWithName, ctx *RecommendationContext) float64 {
	confidence := 70.0 // base confidence

	// Higher confidence for topics with more attempts
	if topic.TotalAttempted > 10 {
		confidence += 15.0
	} else if topic.TotalAttempted > 5 {
		confidence += 10.0
	}

	// Higher confidence for very low scores (clear need for improvement)
	if topic.ProficiencyScore < 50 {
		confidence += 15.0
	}

	// Cap at 100
	if confidence > 100 {
		confidence = 100
	}

	return confidence
}

func (re *RecommendationEngine) calculateExpectedImpact(currentScore float64) float64 {
	// Lower scores have higher potential for improvement
	if currentScore < 30 {
		return 40.0
	} else if currentScore < 50 {
		return 30.0
	} else if currentScore < 70 {
		return 20.0
	} else {
		return 10.0
	}
}

func (re *RecommendationEngine) estimateTimeToResults(currentScore float64) int {
	// Lower scores take longer to see significant improvement
	if currentScore < 30 {
		return 21 // 3 weeks
	} else if currentScore < 50 {
		return 14 // 2 weeks
	} else {
		return 7 // 1 week
	}
}

func (re *RecommendationEngine) assessDifficulty(currentScore float64) string {
	if currentScore < 30 {
		return "hard"
	} else if currentScore < 60 {
		return "medium"
	} else {
		return "easy"
	}
}

func (re *RecommendationEngine) generateTopicActionItems(topic models.TopicPerformanceWithName) []ActionItem {
	return []ActionItem{
		{
			ID:            "focused_practice",
			Description:   fmt.Sprintf("Complete 20 practice questions in %s", topic.TopicName),
			Type:          "practice",
			EstimatedTime: 30,
		},
		{
			ID:            "review_concepts",
			Description:   fmt.Sprintf("Review fundamental concepts in %s", topic.TopicName),
			Type:          "review",
			EstimatedTime: 15,
		},
		{
			ID:            "track_progress",
			Description:   "Take a mini-test to track improvement",
			Type:          "practice",
			EstimatedTime: 10,
		},
	}
}

func (re *RecommendationEngine) generateScheduleActionItems(patterns *models.StudyPattern) []ActionItem {
	return []ActionItem{
		{
			ID:            "set_daily_reminder",
			Description:   "Set a daily study reminder for your optimal time",
			Type:          "schedule",
			EstimatedTime: 2,
		},
		{
			ID:            "start_small",
			Description:   "Begin with 15-minute daily sessions",
			Type:          "schedule",
			EstimatedTime: 15,
		},
		{
			ID:            "track_streak",
			Description:   "Use a habit tracker to monitor your study streak",
			Type:          "strategy",
			EstimatedTime: 1,
		},
	}
}

// Placeholder implementations for other recommendation types
func (re *RecommendationEngine) generateStrategyRecommendations(ctx *RecommendationContext) []PersonalizedRecommendation {
	return []PersonalizedRecommendation{}
}

func (re *RecommendationEngine) generateReviewRecommendations(ctx *RecommendationContext) []PersonalizedRecommendation {
	return []PersonalizedRecommendation{}
}

func (re *RecommendationEngine) generateMotivationRecommendations(ctx *RecommendationContext) []PersonalizedRecommendation {
	return []PersonalizedRecommendation{}
}
