package services

import (
	"context"
	"fmt"
	"log"

	"test-spark-backend/internal/config"
	"test-spark-backend/internal/database"
	"test-spark-backend/internal/types"
)

// AIServiceFactory creates and configures AI services
type AIServiceFactory struct {
	config *config.AIConfig
	store  database.Store
}

// NewAIServiceFactory creates a new AI service factory
func NewAIServiceFactory(config *config.AIConfig, store database.Store) *AIServiceFactory {
	return &AIServiceFactory{
		config: config,
		store:  store,
	}
}

// CreateAIProviderManager creates and configures an AI provider manager
func (f *AIServiceFactory) CreateAIProviderManager() (*AIProviderManager, error) {
	providerConfig := f.config.GetProviderConfig()

	// Create the provider manager
	manager := NewAIProviderManager(providerConfig)

	// Register providers based on configuration
	if err := f.registerProviders(manager, providerConfig); err != nil {
		return nil, fmt.Errorf("failed to register providers: %w", err)
	}

	return manager, nil
}

// registerProviders registers all configured providers with the manager
func (f *AIServiceFactory) registerProviders(manager *AIProviderManager, config *types.AIProviderConfig) error {
	// Register Groq provider if configured
	if config.GroqConfig != nil && config.GroqConfig.APIKey != "" {
		groqProvider := NewGroqProvider(config.GroqConfig, f.store)
		manager.RegisterProvider("groq", groqProvider)

		if f.config.Features.EnableLogging {
			log.Printf("Registered Groq provider with model: %s", config.GroqConfig.Model)
		}
	}

	// Register OpenAI provider if configured
	if config.OpenAIConfig != nil && config.OpenAIConfig.APIKey != "" {
		openaiProvider := NewOpenAIProvider(config.OpenAIConfig, f.store)
		manager.RegisterProvider("openai", openaiProvider)

		if f.config.Features.EnableLogging {
			log.Printf("Registered OpenAI provider with model: %s", config.OpenAIConfig.Model)
		}
	}

	// Register Hugging Face provider if configured
	if config.HuggingFaceConfig != nil && config.HuggingFaceConfig.APIKey != "" {
		hfProvider := NewHuggingFaceProvider(config.HuggingFaceConfig, f.store)
		manager.RegisterProvider("huggingface", hfProvider)

		if f.config.Features.EnableLogging {
			log.Printf("Registered Hugging Face provider with model: %s", config.HuggingFaceConfig.Model)
		}
	}

	// Register Gemini provider if configured
	if config.GeminiConfig != nil && config.GeminiConfig.APIKey != "" {
		geminiProvider := NewGeminiProvider(config.GeminiConfig, f.store)
		manager.RegisterProvider("gemini", geminiProvider)

		if f.config.Features.EnableLogging {
			log.Printf("Registered Gemini provider with model: %s", config.GeminiConfig.Model)
		}
	}

	// Register Cerebras provider if configured
	if config.CerebrasConfig != nil && config.CerebrasConfig.APIKey != "" {
		cerebrasProvider := NewCerebrasProvider(config.CerebrasConfig, f.store)
		manager.RegisterProvider("cerebras", cerebrasProvider)

		if f.config.Features.EnableLogging {
			log.Printf("Registered Cerebras provider with model: %s", config.CerebrasConfig.Model)
		}
	}

	// Always register mock provider for fallback
	mockProvider := FallbackProvider()
	manager.RegisterProvider("mock", mockProvider)

	if f.config.Features.EnableLogging {
		log.Printf("Registered mock provider for fallback")
	}

	// Verify that the primary provider is registered
	if manager.primary == nil {
		return fmt.Errorf("primary provider '%s' is not registered or configured", config.PrimaryProvider)
	}

	// Verify that the fallback provider is registered if fallback is enabled
	if config.EnableFallback && manager.fallback == nil {
		return fmt.Errorf("fallback provider '%s' is not registered or configured", config.FallbackProvider)
	}

	return nil
}

// CreateTestService creates a test service with the configured AI provider
func (f *AIServiceFactory) CreateTestService() (*TestService, error) {
	// Create AI provider manager
	aiProviderMgr, err := f.CreateAIProviderManager()
	if err != nil {
		return nil, fmt.Errorf("failed to create AI provider manager: %w", err)
	}

	// Create test service with AI provider manager
	testService := NewTestServiceWithAI(f.store, aiProviderMgr)

	if f.config.Features.EnableLogging {
		log.Printf("Created test service with AI provider manager")
	}

	return testService, nil
}

// CreateLegacyTestService creates a test service with the legacy Groq client (for backward compatibility)
func (f *AIServiceFactory) CreateLegacyTestService() (*TestService, error) {
	if f.config.Provider.GroqConfig == nil || f.config.Provider.GroqConfig.APIKey == "" {
		return nil, fmt.Errorf("groq configuration is required for legacy test service")

	}

	// Create legacy Groq client
	groqClient := NewGroqClient(f.config.Provider.GroqConfig.APIKey, f.store)

	// Create test service with Groq client
	testService := NewTestService(f.store, groqClient)

	if f.config.Features.EnableLogging {
		log.Printf("Created legacy test service with Groq client")
	}

	return testService, nil
}

// GetProviderStatus returns the status of all configured providers
func (f *AIServiceFactory) GetProviderStatus() (map[string]ProviderInfo, error) {
	manager, err := f.CreateAIProviderManager()
	if err != nil {
		return nil, fmt.Errorf("failed to create provider manager: %w", err)
	}

	status := make(map[string]ProviderInfo)

	// Check each registered provider
	for name, provider := range manager.providers {
		info := ProviderInfo{
			Name:         provider.GetProviderName(),
			IsAvailable:  provider.IsAvailable(context.TODO()), // TODO: Pass proper context
			Capabilities: provider.GetCapabilities(),
			IsPrimary:    name == f.config.Provider.PrimaryProvider,
			IsFallback:   name == f.config.Provider.FallbackProvider,
		}
		status[name] = info
	}

	return status, nil
}

// ProviderInfo contains information about a provider
type ProviderInfo struct {
	Name         string                     `json:"name"`
	IsAvailable  bool                       `json:"is_available"`
	Capabilities types.ProviderCapabilities `json:"capabilities"`
	IsPrimary    bool                       `json:"is_primary"`
	IsFallback   bool                       `json:"is_fallback"`
}

// ValidateConfiguration validates the current configuration
func (f *AIServiceFactory) ValidateConfiguration() error {
	// Try to create the AI provider manager to validate configuration
	_, err := f.CreateAIProviderManager()
	if err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	return nil
}

// GetConfigurationSummary returns a summary of the current configuration
func (f *AIServiceFactory) GetConfigurationSummary() ConfigurationSummary {
	return ConfigurationSummary{
		PrimaryProvider:        f.config.Provider.PrimaryProvider,
		FallbackProvider:       f.config.Provider.FallbackProvider,
		FallbackEnabled:        f.config.Provider.EnableFallback,
		GradeAdaptationEnabled: f.config.Features.EnableGradeAdaptation,
		BoardSupportEnabled:    f.config.Features.EnableBoardSupport,
		ValidationEnabled:      f.config.Features.EnableValidation,
		MaxQuestionsPerRequest: f.config.Limits.MaxQuestionsPerRequest,
		TimeoutSeconds:         f.config.Limits.TimeoutSeconds,
	}
}

// ConfigurationSummary provides a summary of the AI configuration
type ConfigurationSummary struct {
	PrimaryProvider        string `json:"primary_provider"`
	FallbackProvider       string `json:"fallback_provider"`
	FallbackEnabled        bool   `json:"fallback_enabled"`
	GradeAdaptationEnabled bool   `json:"grade_adaptation_enabled"`
	BoardSupportEnabled    bool   `json:"board_support_enabled"`
	ValidationEnabled      bool   `json:"validation_enabled"`
	MaxQuestionsPerRequest int    `json:"max_questions_per_request"`
	TimeoutSeconds         int    `json:"timeout_seconds"`
}

// CreateDefaultFactory creates a factory with default configuration
func CreateDefaultFactory(store database.Store) (*AIServiceFactory, error) {
	config, err := config.LoadAIConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to load AI configuration: %w", err)
	}

	return NewAIServiceFactory(config, store), nil
}
