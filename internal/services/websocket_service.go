// Package services provides WebSocket functionality for real-time analytics updates.
// This service manages WebSocket connections and broadcasts analytics updates
// to connected clients for live dashboard functionality.
package services

import (
	"context"
	"log"
	"net/http"
	"sync"
	"time"

	"test-spark-backend/internal/models"

	"github.com/gorilla/websocket"
)

// WebSocketService manages WebSocket connections for real-time updates
type WebSocketService struct {
	// Connection management
	clients    map[string]map[*websocket.Conn]bool // userID -> connections
	clientsMux sync.RWMutex

	// Message broadcasting
	broadcast chan *WebSocketMessage

	// Connection upgrader
	upgrader websocket.Upgrader

	// Service lifecycle
	ctx    context.Context
	cancel context.CancelFunc

	// Structured logger
	logger *Logger
}

// WebSocketMessage represents a message sent over WebSocket
type WebSocketMessage struct {
	Type      string      `json:"type"`
	UserID    string      `json:"user_id,omitempty"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

// WebSocketMessageType defines the types of WebSocket messages
type WebSocketMessageType string

const (
	MessageTypeDashboardUpdate   WebSocketMessageType = "dashboard_update"
	MessageTypePerformanceUpdate WebSocketMessageType = "performance_update"
	MessageTypeTestCompleted     WebSocketMessageType = "test_completed"
	MessageTypeInsightGenerated  WebSocketMessageType = "insight_generated"
	MessageTypeConnectionStatus  WebSocketMessageType = "connection_status"
	MessageTypeError             WebSocketMessageType = "error"
)

// NewWebSocketService creates a new WebSocket service
func NewWebSocketService() *WebSocketService {
	ctx, cancel := context.WithCancel(context.Background())
	logger := GetLogger()

	ws := &WebSocketService{
		clients:   make(map[string]map[*websocket.Conn]bool),
		broadcast: make(chan *WebSocketMessage, 256),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// In production, implement proper origin checking
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
		ctx:    ctx,
		cancel: cancel,
		logger: logger,
	}

	// Start the message broadcasting goroutine
	go ws.handleMessages()

	logger.WithComponent("websocket").Info("WebSocket service initialized successfully")
	return ws
}

// HandleWebSocket upgrades HTTP connections to WebSocket
func (ws *WebSocketService) HandleWebSocket(w http.ResponseWriter, r *http.Request, userID string) {
	// Upgrade the HTTP connection to WebSocket
	conn, err := ws.upgrader.Upgrade(w, r, nil)
	if err != nil {
		ws.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   userID,
			"component": "websocket",
		}).Error("Failed to upgrade connection to WebSocket")
		return
	}

	// Register the client
	ws.registerClient(userID, conn)
	defer ws.unregisterClient(userID, conn)

	ws.logger.LogWebSocketEvent("connection_established", userID, ws.GetConnectionCount())

	// Send connection status message
	ws.sendToUser(userID, &WebSocketMessage{
		Type:      string(MessageTypeConnectionStatus),
		Data:      map[string]string{"status": "connected"},
		Timestamp: time.Now(),
	})

	// Handle incoming messages from client
	ws.handleClientMessages(conn, userID)
}

// registerClient adds a new client connection
func (ws *WebSocketService) registerClient(userID string, conn *websocket.Conn) {
	ws.clientsMux.Lock()
	defer ws.clientsMux.Unlock()

	if ws.clients[userID] == nil {
		ws.clients[userID] = make(map[*websocket.Conn]bool)
	}
	ws.clients[userID][conn] = true

	log.Printf("Client registered for user: %s (total connections: %d)", userID, len(ws.clients[userID]))
}

// unregisterClient removes a client connection
func (ws *WebSocketService) unregisterClient(userID string, conn *websocket.Conn) {
	ws.clientsMux.Lock()
	defer ws.clientsMux.Unlock()

	if connections, exists := ws.clients[userID]; exists {
		if _, exists := connections[conn]; exists {
			delete(connections, conn)
			conn.Close()

			// Remove user entry if no connections left
			if len(connections) == 0 {
				delete(ws.clients, userID)
			}

			log.Printf("Client unregistered for user: %s", userID)
		}
	}
}

// handleClientMessages processes incoming messages from WebSocket clients
func (ws *WebSocketService) handleClientMessages(conn *websocket.Conn, userID string) {
	defer conn.Close()

	// Set read deadline and pong handler for connection health
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	// Start ping ticker
	ticker := time.NewTicker(54 * time.Second)
	defer ticker.Stop()

	go func() {
		for {
			select {
			case <-ticker.C:
				conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
				if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
					return
				}
			case <-ws.ctx.Done():
				return
			}
		}
	}()

	// Read messages from client
	for {
		var msg WebSocketMessage
		err := conn.ReadJSON(&msg)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error for user %s: %v", userID, err)
			}
			break
		}

		// Handle client message (for future use - client-initiated requests)
		log.Printf("Received message from user %s: %s", userID, msg.Type)
	}
}

// handleMessages processes outgoing messages to be broadcast
func (ws *WebSocketService) handleMessages() {
	for {
		select {
		case message := <-ws.broadcast:
			if message.UserID != "" {
				// Send to specific user
				ws.sendToUser(message.UserID, message)
			} else {
				// Broadcast to all users
				ws.broadcastToAll(message)
			}
		case <-ws.ctx.Done():
			return
		}
	}
}

// sendToUser sends a message to all connections of a specific user
func (ws *WebSocketService) sendToUser(userID string, message *WebSocketMessage) {
	ws.clientsMux.RLock()
	connections, exists := ws.clients[userID]
	ws.clientsMux.RUnlock()

	if !exists {
		return
	}

	// Send message to all user connections
	for conn := range connections {
		select {
		case <-ws.ctx.Done():
			return
		default:
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteJSON(message); err != nil {
				log.Printf("Error sending message to user %s: %v", userID, err)
				ws.unregisterClient(userID, conn)
			}
		}
	}
}

// broadcastToAll sends a message to all connected clients
func (ws *WebSocketService) broadcastToAll(message *WebSocketMessage) {
	ws.clientsMux.RLock()
	defer ws.clientsMux.RUnlock()

	for userID, connections := range ws.clients {
		for conn := range connections {
			select {
			case <-ws.ctx.Done():
				return
			default:
				conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
				if err := conn.WriteJSON(message); err != nil {
					log.Printf("Error broadcasting to user %s: %v", userID, err)
					go ws.unregisterClient(userID, conn)
				}
			}
		}
	}
}

// Public methods for sending specific types of updates

// SendDashboardUpdate sends a dashboard update to a specific user
func (ws *WebSocketService) SendDashboardUpdate(userID string, dashboard *models.DashboardSummary) {
	message := &WebSocketMessage{
		Type:      string(MessageTypeDashboardUpdate),
		UserID:    userID,
		Data:      dashboard,
		Timestamp: time.Now(),
	}

	select {
	case ws.broadcast <- message:
	case <-ws.ctx.Done():
	default:
		log.Printf("Broadcast channel full, dropping dashboard update for user: %s", userID)
	}
}

// SendPerformanceUpdate sends a performance update to a specific user
func (ws *WebSocketService) SendPerformanceUpdate(userID string, trends []models.PerformanceTrend) {
	message := &WebSocketMessage{
		Type:      string(MessageTypePerformanceUpdate),
		UserID:    userID,
		Data:      trends,
		Timestamp: time.Now(),
	}

	select {
	case ws.broadcast <- message:
	case <-ws.ctx.Done():
	default:
		log.Printf("Broadcast channel full, dropping performance update for user: %s", userID)
	}
}

// SendTestCompletedNotification sends a test completion notification
func (ws *WebSocketService) SendTestCompletedNotification(userID string, testResult *models.TestResult) {
	message := &WebSocketMessage{
		Type:      string(MessageTypeTestCompleted),
		UserID:    userID,
		Data:      testResult,
		Timestamp: time.Now(),
	}

	select {
	case ws.broadcast <- message:
	case <-ws.ctx.Done():
	default:
		log.Printf("Broadcast channel full, dropping test completion notification for user: %s", userID)
	}
}

// SendInsightGenerated sends a new insight notification
func (ws *WebSocketService) SendInsightGenerated(userID string, insight *models.AnalyticsInsight) {
	message := &WebSocketMessage{
		Type:      string(MessageTypeInsightGenerated),
		UserID:    userID,
		Data:      insight,
		Timestamp: time.Now(),
	}

	select {
	case ws.broadcast <- message:
	case <-ws.ctx.Done():
	default:
		log.Printf("Broadcast channel full, dropping insight notification for user: %s", userID)
	}
}

// GetConnectedUsers returns a list of currently connected user IDs
func (ws *WebSocketService) GetConnectedUsers() []string {
	ws.clientsMux.RLock()
	defer ws.clientsMux.RUnlock()

	users := make([]string, 0, len(ws.clients))
	for userID := range ws.clients {
		users = append(users, userID)
	}
	return users
}

// GetConnectionCount returns the total number of active connections
func (ws *WebSocketService) GetConnectionCount() int {
	ws.clientsMux.RLock()
	defer ws.clientsMux.RUnlock()

	count := 0
	for _, connections := range ws.clients {
		count += len(connections)
	}
	return count
}

// Close shuts down the WebSocket service
func (ws *WebSocketService) Close() {
	log.Println("Shutting down WebSocket service...")

	// Cancel context to stop goroutines
	ws.cancel()

	// Close all connections
	ws.clientsMux.Lock()
	for userID, connections := range ws.clients {
		for conn := range connections {
			conn.Close()
		}
		delete(ws.clients, userID)
	}
	ws.clientsMux.Unlock()

	// Close broadcast channel
	close(ws.broadcast)

	log.Println("WebSocket service shut down successfully")
}
