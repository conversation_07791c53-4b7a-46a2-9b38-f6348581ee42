package services

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
	"test-spark-backend/internal/types"

	"github.com/google/uuid"
)

// GroqProvider implements the AIProvider interface for Groq AI
type GroqProvider struct {
	config     *types.GroqProviderConfig
	httpClient *http.Client
	store      database.Store
}

// NewGroqProvider creates a new Groq AI provider
func NewGroqProvider(config *types.GroqProviderConfig, store database.Store) *GroqProvider {
	// Create HTTP client with proper TLS configuration
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			// Use system's root CA certificates
			RootCAs:            nil,   // Use system default
			InsecureSkipVerify: false, // Enable certificate verification
			MinVersion:         tls.VersionTLS12,
			MaxVersion:         tls.VersionTLS13,
			ServerName:         "api.groq.com", // Explicitly set server name
		},
		// Connection settings for better reliability
		DisableKeepAlives:     false,
		DisableCompression:    false,
		MaxIdleConns:          10,
		MaxIdleConnsPerHost:   2,
		MaxConnsPerHost:       10,
		IdleConnTimeout:       90 * time.Second,
		ResponseHeaderTimeout: 60 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	// Check if we're in a Docker environment and need special handling
	if os.Getenv("DOCKER_ENV") == "true" || os.Getenv("SSL_VERIFY") == "false" {
		fmt.Printf("Docker environment detected for Groq provider, configuring TLS\n")
		transport.TLSClientConfig.InsecureSkipVerify = true
		fmt.Printf("TLS certificate verification disabled for Docker environment\n")
	}

	httpClient := &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second,
	}

	return &GroqProvider{
		config:     config,
		httpClient: httpClient,
		store:      store,
	}
}

// GenerateQuestions implements AIProvider.GenerateQuestions
func (gp *GroqProvider) GenerateQuestions(ctx context.Context, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	// Build the enhanced prompt
	builder := NewPromptTemplateBuilder()
	prompt := builder.BuildEnhancedPrompt(genCtx)

	// Make API request
	responseContent, err := gp.makeAPIRequest(ctx, prompt)
	if err != nil {
		return nil, fmt.Errorf("failed to make API request: %w", err)
	}

	// Parse the response
	questions, err := gp.parseQuestionsFromResponse(responseContent, genCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to parse questions: %w", err)
	}

	return questions, nil
}

// GetProviderName implements AIProvider.GetProviderName
func (gp *GroqProvider) GetProviderName() string {
	return "groq"
}

// IsAvailable implements AIProvider.IsAvailable
func (gp *GroqProvider) IsAvailable(ctx context.Context) bool {
	if gp.config.APIKey == "" {
		return false
	}

	// TODO: Implement health check endpoint call
	// For now, just check if we have the required configuration
	return gp.config.BaseURL != "" && gp.config.Model != ""
}

// GetCapabilities implements AIProvider.GetCapabilities
func (gp *GroqProvider) GetCapabilities() types.ProviderCapabilities {
	return types.ProviderCapabilities{
		MaxQuestionsPerRequest: 50,
		SupportedDifficulties:  []string{"easy", "medium", "hard"},
		SupportsGradeLevel:     true,
		SupportsBoard:          true,
		MaxTokens:              gp.config.MaxTokens,
		SupportsStreaming:      false,
	}
}

// makeAPIRequest makes the actual API request to Groq
func (gp *GroqProvider) makeAPIRequest(ctx context.Context, prompt string) (string, error) {
	fmt.Printf("=== GROQ API REQUEST STARTING ===\n")
	fmt.Printf("Groq API URL: %s\n", gp.config.BaseURL)
	request := GroqRequest{
		Model: gp.config.Model,
		Messages: []GroqMessage{
			{
				Role:    "user",
				Content: prompt,
			},
		},
		Temperature: gp.config.Temperature,
		MaxTokens:   gp.config.MaxTokens,
	}

	// Marshal request
	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", gp.config.BaseURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+gp.config.APIKey)

	// Use the provider's configured HTTP client
	resp, err := gp.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body first for debugging
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	// Debug: Print response details
	fmt.Printf("=== GROQ PROVIDER API RESPONSE ===\n")
	fmt.Printf("Status Code: %d\n", resp.StatusCode)
	fmt.Printf("Response Body Length: %d\n", len(bodyBytes))
	maxLen := len(bodyBytes)
	if maxLen > 500 {
		maxLen = 500
	}
	fmt.Printf("Response Body (first 500 chars): %s\n", string(bodyBytes[:maxLen]))

	// Check for non-200 status codes
	if resp.StatusCode != 200 {
		return "", fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response
	var groqResp GroqResponse
	if err := json.Unmarshal(bodyBytes, &groqResp); err != nil {
		return "", fmt.Errorf("failed to decode response: %w (body: %s)", err, string(bodyBytes))
	}

	// Check for API errors
	if groqResp.Error != nil {
		return "", &types.AIProviderError{
			Message:  groqResp.Error.Message,
			Provider: "groq",
			Code:     groqResp.Error.Code,
		}
	}

	// Check for empty response
	if len(groqResp.Choices) == 0 {
		return "", &types.AIProviderError{
			Message:  "no choices in response",
			Provider: "groq",
			Code:     "EMPTY_RESPONSE",
		}
	}

	return groqResp.Choices[0].Message.Content, nil
}

// parseQuestionsFromResponse parses the AI response into Question models
func (gp *GroqProvider) parseQuestionsFromResponse(responseContent string, genCtx *QuestionGenerationContext) ([]models.Question, error) {
	// Clean the response content (remove any markdown formatting)
	responseContent = strings.TrimSpace(responseContent)
	responseContent = strings.TrimPrefix(responseContent, "```json")
	responseContent = strings.TrimSuffix(responseContent, "```")
	responseContent = strings.TrimSpace(responseContent)

	// Parse JSON response
	var questionContents []models.QuestionContent
	if err := json.Unmarshal([]byte(responseContent), &questionContents); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	// Convert to Question models
	var questions []models.Question
	for i, content := range questionContents {
		// Validate question content
		if err := gp.validateQuestionContent(&content); err != nil {
			return nil, fmt.Errorf("invalid question %d: %w", i+1, err)
		}

		// Convert to JSON for storage
		contentBytes, err := json.Marshal(content)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal question content %d: %w", i+1, err)
		}

		// Determine topic and difficulty for this question
		topicID := gp.selectTopicForQuestion(i, genCtx)
		difficulty := gp.selectDifficultyForQuestion(i, genCtx)

		question := models.Question{
			ID:            uuid.New(),
			TopicID:       topicID,
			Content:       contentBytes,
			Difficulty:    &difficulty,
			AuthorAIModel: stringPtr(gp.config.Model),
			CreatedAt:     time.Now(),
		}

		questions = append(questions, question)
	}

	return questions, nil
}

// selectTopicForQuestion selects an appropriate topic for a question
func (gp *GroqProvider) selectTopicForQuestion(questionIndex int, genCtx *QuestionGenerationContext) int {
	// Use actual topic IDs from the context
	if len(genCtx.TopicIDs) == 0 {
		return 1 // Default topic ID as fallback
	}

	// Distribute topics cyclically using actual topic IDs
	return genCtx.TopicIDs[questionIndex%len(genCtx.TopicIDs)]
}

// selectDifficultyForQuestion selects an appropriate difficulty for a question
func (gp *GroqProvider) selectDifficultyForQuestion(questionIndex int, genCtx *QuestionGenerationContext) models.DifficultyLevel {
	if len(genCtx.Difficulties) == 0 {
		return models.DifficultyMedium // Default difficulty
	}

	// Distribute difficulties cyclically
	return genCtx.Difficulties[questionIndex%len(genCtx.Difficulties)]
}

// validateQuestionContent validates the structure and content of a question
func (gp *GroqProvider) validateQuestionContent(content *models.QuestionContent) error {
	if content.Question == "" {
		return fmt.Errorf("question text is empty")
	}

	if len(content.Options) != 4 {
		return fmt.Errorf("question must have exactly 4 options, got %d", len(content.Options))
	}

	for i, option := range content.Options {
		if option == "" {
			return fmt.Errorf("option %d is empty", i+1)
		}
	}

	if content.CorrectOptionIndex < 0 || content.CorrectOptionIndex >= len(content.Options) {
		return fmt.Errorf("correct_option_index %d is out of range", content.CorrectOptionIndex)
	}

	if content.Explanation == "" {
		return fmt.Errorf("explanation is empty")
	}

	return nil
}
