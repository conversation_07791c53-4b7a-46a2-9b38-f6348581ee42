package api

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"

	"test-spark-backend/internal/models"
	"test-spark-backend/internal/services"

	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
)

// AdaptiveHandlers contains handlers for adaptive learning endpoints
type AdaptiveHandlers struct {
	adaptiveService *services.AdaptiveLearningService
}

// NewAdaptiveHandlers creates a new adaptive handlers instance
func NewAdaptiveHandlers(adaptiveService *services.AdaptiveLearningService) *AdaptiveHandlers {
	return &AdaptiveHandlers{
		adaptiveService: adaptiveService,
	}
}

// GetMasteryLevels retrieves mastery levels for a user
func (h *AdaptiveHandlers) GetMasteryLevels(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r.Context())
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Check for subject filter
	subjectIDStr := r.URL.Query().Get("subject_id")

	var masteryLevels []models.MasteryLevel

	if subjectIDStr != "" {
		subjectID, err := strconv.Atoi(subjectIDStr)
		if err != nil {
			http.Error(w, "Invalid subject ID", http.StatusBadRequest)
			return
		}

		// Get mastery levels for specific subject
		// Note: GetMasteryLevelsBySubject not yet implemented in service layer
		// Returning empty array as placeholder for subject ID: %d
		masteryLevels = []models.MasteryLevel{} // Subject ID: %d (placeholder implementation)
		_ = subjectID                           // Suppress unused variable warning until service method is implemented
	} else {
		// Get all mastery levels for user
		// This would require implementing GetMasteryLevelsByUser in the service
		// For now, return empty array
		masteryLevels = []models.MasteryLevel{}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"mastery_levels": masteryLevels,
		"user_id":        userID,
	})
}

// GetAdaptiveLearningPath generates or retrieves an adaptive learning path
func (h *AdaptiveHandlers) GetAdaptiveLearningPath(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r.Context())
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	subjectIDStr := chi.URLParam(r, "subjectId")
	subjectID, err := strconv.Atoi(subjectIDStr)
	if err != nil {
		http.Error(w, "Invalid subject ID", http.StatusBadRequest)
		return
	}

	// Parse target mastery from query params (default to 0.8)
	targetMasteryStr := r.URL.Query().Get("target_mastery")
	targetMastery := 0.8
	if targetMasteryStr != "" {
		if parsed, err := strconv.ParseFloat(targetMasteryStr, 64); err == nil {
			targetMastery = parsed
		}
	}

	// Generate adaptive learning path
	learningPath, err := h.adaptiveService.GenerateAdaptiveLearningPath(
		r.Context(),
		userID,
		subjectID,
		targetMastery,
	)
	if err != nil {
		http.Error(w, "Failed to generate learning path: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(learningPath)
}

// UpdateMasteryLevel updates mastery level for a topic
func (h *AdaptiveHandlers) UpdateMasteryLevel(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r.Context())
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	topicIDStr := chi.URLParam(r, "topicId")
	topicID, err := strconv.Atoi(topicIDStr)
	if err != nil {
		http.Error(w, "Invalid topic ID", http.StatusBadRequest)
		return
	}

	// Calculate current mastery level
	masteryLevel, err := h.adaptiveService.CalculateMasteryLevel(r.Context(), userID, topicID)
	if err != nil {
		http.Error(w, "Failed to calculate mastery level: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(masteryLevel)
}

// GetAdaptiveDifficulty gets the recommended difficulty for next question
func (h *AdaptiveHandlers) GetAdaptiveDifficulty(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r.Context())
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	topicIDStr := chi.URLParam(r, "topicId")
	topicID, err := strconv.Atoi(topicIDStr)
	if err != nil {
		http.Error(w, "Invalid topic ID", http.StatusBadRequest)
		return
	}

	// For now, return a simple response
	// In a full implementation, this would analyze recent responses
	// and return optimal difficulty using the adaptive service

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"recommended_difficulty": 0.0,
		"confidence":             0.8,
		"topic_id":               topicID,
		"user_id":                userID,
	})
}

// SubmitAdaptiveResponse records a response for adaptive analysis
func (h *AdaptiveHandlers) SubmitAdaptiveResponse(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r.Context())
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	var request struct {
		QuestionID   string  `json:"question_id"`
		IsCorrect    bool    `json:"is_correct"`
		ResponseTime int     `json:"response_time"`
		Difficulty   float64 `json:"difficulty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	questionID, err := uuid.Parse(request.QuestionID)
	if err != nil {
		http.Error(w, "Invalid question ID", http.StatusBadRequest)
		return
	}

	// Create adaptive response record
	adaptiveResponse := &models.AdaptiveResponse{
		QuestionID:   questionID,
		UserID:       userID,
		IsCorrect:    request.IsCorrect,
		ResponseTime: request.ResponseTime,
		IRTParameters: models.IRTParameters{
			Difficulty:     request.Difficulty,
			Discrimination: 1.0, // Default value
			Guessing:       0.0, // Default value
		},
		AbilityEstimate: 0.0, // Would be calculated
		Confidence:      1.0, // Would be calculated
	}

	// In a full implementation, this would:
	// 1. Store the response in the database
	// 2. Update IRT parameters
	// 3. Recalculate ability estimate
	// 4. Update mastery levels

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":         true,
		"updated_ability": adaptiveResponse.AbilityEstimate,
		"confidence":      adaptiveResponse.Confidence,
		"message":         "Response recorded successfully",
	})
}

// GetLearningInsights provides personalized learning insights
func (h *AdaptiveHandlers) GetLearningInsights(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r.Context())
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Generate insights based on user's learning data
	insights := map[string]interface{}{
		"user_id": userID,
		"insights": []map[string]interface{}{
			{
				"type":        "strength",
				"title":       "Strong Performance in Algebra",
				"description": "You're excelling in algebraic concepts with 85% accuracy",
				"action":      "Continue practicing advanced algebra topics",
				"priority":    1,
			},
			{
				"type":        "weakness",
				"title":       "Geometry Needs Attention",
				"description": "Your geometry performance is below average at 45%",
				"action":      "Focus on basic geometry principles and practice more",
				"priority":    5,
			},
			{
				"type":        "recommendation",
				"title":       "Optimal Study Time",
				"description": "Your performance is best between 2-4 PM",
				"action":      "Schedule important practice sessions during this time",
				"priority":    3,
			},
		},
		"overall_progress": map[string]interface{}{
			"current_ability":    0.2,
			"learning_velocity":  0.15,
			"mastery_percentage": 67.5,
			"topics_mastered":    8,
			"topics_in_progress": 5,
			"topics_to_review":   3,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(insights)
}

// GetSpacedRepetitionSchedule returns topics due for review
func (h *AdaptiveHandlers) GetSpacedRepetitionSchedule(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r.Context())
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// In a full implementation, this would query the database for due reviews
	schedule := map[string]interface{}{
		"user_id": userID,
		"due_reviews": []map[string]interface{}{
			{
				"topic_id":       1,
				"topic_name":     "Linear Equations",
				"subject_name":   "Mathematics",
				"due_date":       "2024-01-15T10:00:00Z",
				"priority":       "high",
				"estimated_time": 15,
			},
			{
				"topic_id":       3,
				"topic_name":     "Photosynthesis",
				"subject_name":   "Biology",
				"due_date":       "2024-01-15T14:00:00Z",
				"priority":       "medium",
				"estimated_time": 20,
			},
		},
		"total_due":            2,
		"estimated_total_time": 35,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(schedule)
}

// Helper function to get user ID from context
func getUserIDFromContext(ctx context.Context) (uuid.UUID, error) {
	// This would extract the user ID from the JWT token in the context
	// For now, return a placeholder UUID
	return uuid.New(), nil
}
