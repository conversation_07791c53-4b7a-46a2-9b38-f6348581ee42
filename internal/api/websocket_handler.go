// Package api provides WebSocket handlers for real-time analytics updates.
// This handler manages WebSocket connections and integrates with the analytics
// system to provide live updates to connected clients.
package api

import (
	"net/http"

	"test-spark-backend/internal/services"
)

// WebSocketHandler handles WebSocket connections for real-time updates
type WebSocketHandler struct {
	websocketService *services.WebSocketService
}

// NewWebSocketHandler creates a new WebSocket handler
func NewWebSocketHandler(websocketService *services.WebSocketService) *WebSocketHandler {
	return &WebSocketHandler{
		websocketService: websocketService,
	}
}

// HandleAnalyticsWebSocket handles WebSocket connections for analytics updates
// GET /api/v1/ws/analytics
func (h *WebSocketHandler) HandleAnalyticsWebSocket(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context (set by authentication middleware)
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		WriteErrorResponse(w, http.StatusUnauthorized, "User ID not found in context")
		return
	}

	// Upgrade connection to WebSocket and handle it
	h.websocketService.HandleWebSocket(w, r, userID)
}

// GetWebSocketStats returns WebSocket connection statistics
// GET /api/v1/ws/stats
func (h *WebSocketHandler) GetWebSocketStats(w http.ResponseWriter, r *http.Request) {
	stats := map[string]interface{}{
		"connected_users":   h.websocketService.GetConnectedUsers(),
		"total_connections": h.websocketService.GetConnectionCount(),
		"service_status":    "active",
	}

	WriteJSONResponse(w, http.StatusOK, stats)
}
