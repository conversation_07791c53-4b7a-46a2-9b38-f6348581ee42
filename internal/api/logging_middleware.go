// Package api provides logging middleware for structured request/response logging.
// This middleware captures HTTP request details, response status, timing information,
// and user context for comprehensive API monitoring and debugging.
package api

import (
	"context"
	"net/http"
	"time"

	"test-spark-backend/internal/services"

	"github.com/go-chi/chi/v5/middleware"
	"github.com/google/uuid"
)

// LoggingMiddleware provides structured logging for HTTP requests
func LoggingMiddleware(logger *services.Logger) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()

			// Generate request ID if not present
			requestID := r.Header.Get("X-Request-ID")
			if requestID == "" {
				requestID = uuid.New().String()
			}

			// Add request ID to context
			ctx := context.WithValue(r.Context(), "request_id", requestID)
			r = r.WithContext(ctx)

			// Add request ID to response headers
			w.<PERSON><PERSON>().Set("X-Request-ID", requestID)

			// Wrap response writer to capture status code
			ww := middleware.NewWrapResponseWriter(w, r.ProtoMajor)

			// Get user ID from context if available (set by auth middleware)
			userID := ""
			if uid, ok := GetUserIDFromContext(ctx); ok {
				userID = uid
			}

			// Log request start
			logger.WithFields(map[string]interface{}{
				"method":     r.Method,
				"path":       r.URL.Path,
				"query":      r.URL.RawQuery,
				"user_agent": r.UserAgent(),
				"remote_ip":  GetClientIP(r),
				"user_id":    userID,
				"request_id": requestID,
				"type":       "request_start",
			}).Info("HTTP request started")

			// Process request
			next.ServeHTTP(ww, r)

			// Calculate duration
			duration := time.Since(start)

			// Log request completion
			logger.LogAPIRequest(
				r.Method,
				r.URL.Path,
				userID,
				requestID,
				ww.Status(),
				duration.Milliseconds(),
			)

			// Log slow requests as warnings
			if duration > 5*time.Second {
				logger.WithFields(map[string]interface{}{
					"method":      r.Method,
					"path":        r.URL.Path,
					"user_id":     userID,
					"request_id":  requestID,
					"duration_ms": duration.Milliseconds(),
					"status_code": ww.Status(),
					"type":        "slow_request",
				}).Warn("Slow HTTP request detected")
			}

			// Log errors (4xx and 5xx status codes)
			if ww.Status() >= 400 {
				logLevel := "warn"
				if ww.Status() >= 500 {
					logLevel = "error"
				}

				entry := logger.WithFields(map[string]interface{}{
					"method":      r.Method,
					"path":        r.URL.Path,
					"user_id":     userID,
					"request_id":  requestID,
					"status_code": ww.Status(),
					"duration_ms": duration.Milliseconds(),
					"type":        "http_error",
				})

				if logLevel == "error" {
					entry.Error("HTTP request failed with server error")
				} else {
					entry.Warn("HTTP request failed with client error")
				}
			}
		})
	}
}

// GetClientIP extracts the real client IP address from the request
func GetClientIP(r *http.Request) string {
	// Check X-Forwarded-For header (most common)
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		// X-Forwarded-For can contain multiple IPs, take the first one
		if idx := len(xff); idx > 0 {
			if commaIdx := 0; commaIdx < idx {
				for i, char := range xff {
					if char == ',' {
						commaIdx = i
						break
					}
				}
				if commaIdx > 0 {
					return xff[:commaIdx]
				}
			}
			return xff
		}
	}

	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}

	// Check X-Forwarded header
	if xf := r.Header.Get("X-Forwarded"); xf != "" {
		return xf
	}

	// Fall back to RemoteAddr
	return r.RemoteAddr
}

// SecurityLoggingMiddleware logs security-related events
func SecurityLoggingMiddleware(logger *services.Logger) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Log authentication attempts
			if r.URL.Path == "/api/v1/auth/login" || r.URL.Path == "/api/v1/auth/register" {
				logger.LogSecurityEvent(
					"auth_attempt",
					"", // User ID not available yet
					GetClientIP(r),
					map[string]interface{}{
						"endpoint":   r.URL.Path,
						"user_agent": r.UserAgent(),
						"method":     r.Method,
					},
				)
			}

			// Log admin endpoint access
			if r.URL.Path == "/api/v1/admin" || r.URL.Path == "/api/v1/admin/" {
				userID := ""
				if uid, ok := GetUserIDFromContext(r.Context()); ok {
					userID = uid
				}

				logger.LogSecurityEvent(
					"admin_access",
					userID,
					GetClientIP(r),
					map[string]interface{}{
						"endpoint": r.URL.Path,
						"method":   r.Method,
					},
				)
			}

			// Continue with request
			next.ServeHTTP(w, r)
		})
	}
}

// ErrorLoggingMiddleware logs panics and recovers gracefully
func ErrorLoggingMiddleware(logger *services.Logger) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			defer func() {
				if err := recover(); err != nil {
					userID := ""
					if uid, ok := GetUserIDFromContext(r.Context()); ok {
						userID = uid
					}

					requestID := ""
					if rid := r.Context().Value("request_id"); rid != nil {
						requestID = rid.(string)
					}

					logger.WithFields(map[string]interface{}{
						"method":     r.Method,
						"path":       r.URL.Path,
						"user_id":    userID,
						"request_id": requestID,
						"panic":      err,
						"type":       "panic_recovery",
					}).Error("Panic recovered in HTTP handler")

					// Return 500 error
					http.Error(w, "Internal Server Error", http.StatusInternalServerError)
				}
			}()

			next.ServeHTTP(w, r)
		})
	}
}
