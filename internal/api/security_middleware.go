// Package api provides security middleware for the Test-Spark application.
// This middleware implements various security measures including rate limiting,
// security headers, and request validation to protect against common attacks.
package api

import (
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
)

// RateLimiter implements a simple token bucket rate limiter
type RateLimiter struct {
	clients map[string]*ClientBucket
	mutex   sync.RWMutex

	// Configuration
	requestsPerMinute int
	burstSize         int
	cleanupInterval   time.Duration
}

// ClientBucket represents a token bucket for a specific client
type ClientBucket struct {
	tokens     int
	lastRefill time.Time
	mutex      sync.Mutex
}

// NewRateLimiter creates a new rate limiter with the specified configuration
func NewRateLimiter(requestsPerMinute, burstSize int) *RateLimiter {
	rl := &RateLimiter{
		clients:           make(map[string]*ClientBucket),
		requestsPerMinute: requestsPerMinute,
		burstSize:         burstSize,
		cleanupInterval:   5 * time.Minute,
	}

	// Start cleanup goroutine
	go rl.cleanup()

	return rl
}

// Allow checks if a request from the given client IP should be allowed
func (rl *RateLimiter) Allow(clientIP string) bool {
	rl.mutex.RLock()
	bucket, exists := rl.clients[clientIP]
	rl.mutex.RUnlock()

	if !exists {
		bucket = &ClientBucket{
			tokens:     rl.burstSize,
			lastRefill: time.Now(),
		}
		rl.mutex.Lock()
		rl.clients[clientIP] = bucket
		rl.mutex.Unlock()
	}

	bucket.mutex.Lock()
	defer bucket.mutex.Unlock()

	// Refill tokens based on time elapsed
	now := time.Now()
	elapsed := now.Sub(bucket.lastRefill)
	tokensToAdd := int(elapsed.Minutes() * float64(rl.requestsPerMinute))

	if tokensToAdd > 0 {
		bucket.tokens += tokensToAdd
		if bucket.tokens > rl.burstSize {
			bucket.tokens = rl.burstSize
		}
		bucket.lastRefill = now
	}

	// Check if request can be allowed
	if bucket.tokens > 0 {
		bucket.tokens--
		return true
	}

	return false
}

// cleanup removes old client buckets to prevent memory leaks
func (rl *RateLimiter) cleanup() {
	ticker := time.NewTicker(rl.cleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		rl.mutex.Lock()
		cutoff := time.Now().Add(-rl.cleanupInterval)

		for ip, bucket := range rl.clients {
			bucket.mutex.Lock()
			if bucket.lastRefill.Before(cutoff) {
				delete(rl.clients, ip)
			}
			bucket.mutex.Unlock()
		}
		rl.mutex.Unlock()
	}
}

// RateLimitMiddleware creates a middleware that applies rate limiting
func RateLimitMiddleware(requestsPerMinute, burstSize int) func(http.Handler) http.Handler {
	limiter := NewRateLimiter(requestsPerMinute, burstSize)

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Get client IP (handle X-Forwarded-For and X-Real-IP headers)
			clientIP := getClientIP(r)

			if !limiter.Allow(clientIP) {
				// Rate limit exceeded
				w.Header().Set("X-RateLimit-Limit", strconv.Itoa(requestsPerMinute))
				w.Header().Set("X-RateLimit-Remaining", "0")
				w.Header().Set("X-RateLimit-Reset", strconv.FormatInt(time.Now().Add(time.Minute).Unix(), 10))

				WriteErrorResponse(w, http.StatusTooManyRequests, "Rate limit exceeded")
				return
			}

			// Add rate limit headers
			w.Header().Set("X-RateLimit-Limit", strconv.Itoa(requestsPerMinute))

			next.ServeHTTP(w, r)
		})
	}
}

// SecurityHeadersMiddleware adds security headers to all responses
func SecurityHeadersMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Prevent clickjacking
		w.Header().Set("X-Frame-Options", "DENY")

		// Prevent MIME type sniffing
		w.Header().Set("X-Content-Type-Options", "nosniff")

		// Enable XSS protection
		w.Header().Set("X-XSS-Protection", "1; mode=block")

		// Enforce HTTPS (only in production)
		if r.Header.Get("X-Forwarded-Proto") == "https" || r.TLS != nil {
			w.Header().Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		}

		// Content Security Policy
		csp := "default-src 'self'; " +
			"script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
			"style-src 'self' 'unsafe-inline'; " +
			"img-src 'self' data: https:; " +
			"font-src 'self' data:; " +
			"connect-src 'self'; " +
			"frame-ancestors 'none'"
		w.Header().Set("Content-Security-Policy", csp)

		// Referrer Policy
		w.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")

		// Permissions Policy (formerly Feature Policy)
		w.Header().Set("Permissions-Policy", "camera=(), microphone=(), geolocation=()")

		next.ServeHTTP(w, r)
	})
}

// InputValidationMiddleware validates request size and content type
func InputValidationMiddleware(maxBodySize int64) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Limit request body size
			if r.ContentLength > maxBodySize {
				WriteErrorResponse(w, http.StatusRequestEntityTooLarge, "Request body too large")
				return
			}

			// Limit request body reader
			r.Body = http.MaxBytesReader(w, r.Body, maxBodySize)

			// Validate content type for POST/PUT requests
			if r.Method == "POST" || r.Method == "PUT" || r.Method == "PATCH" {
				contentType := r.Header.Get("Content-Type")
				if contentType != "" && !isValidContentType(contentType) {
					WriteErrorResponse(w, http.StatusUnsupportedMediaType, "Invalid content type")
					return
				}
			}

			next.ServeHTTP(w, r)
		})
	}
}

// EnhancedCORSMiddleware handles Cross-Origin Resource Sharing with enhanced security
func EnhancedCORSMiddleware(allowedOrigins []string, allowedMethods []string, allowedHeaders []string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			origin := r.Header.Get("Origin")

			// Check if origin is allowed
			if isOriginAllowed(origin, allowedOrigins) {
				w.Header().Set("Access-Control-Allow-Origin", origin)
			}

			// Set other CORS headers
			w.Header().Set("Access-Control-Allow-Methods", strings.Join(allowedMethods, ", "))
			w.Header().Set("Access-Control-Allow-Headers", strings.Join(allowedHeaders, ", "))
			w.Header().Set("Access-Control-Allow-Credentials", "true")
			w.Header().Set("Access-Control-Max-Age", "86400") // 24 hours

			// Handle preflight requests
			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// Helper functions

// getClientIP extracts the real client IP from the request
func getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header (can contain multiple IPs)
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		// Take the first IP (original client)
		ips := strings.Split(xff, ",")
		return strings.TrimSpace(ips[0])
	}

	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}

	// Fall back to RemoteAddr
	ip := r.RemoteAddr
	if colon := strings.LastIndex(ip, ":"); colon != -1 {
		ip = ip[:colon]
	}

	return ip
}

// isValidContentType checks if the content type is allowed
func isValidContentType(contentType string) bool {
	allowedTypes := []string{
		"application/json",
		"application/x-www-form-urlencoded",
		"multipart/form-data",
		"text/plain",
	}

	// Extract main content type (ignore charset, boundary, etc.)
	mainType := strings.Split(contentType, ";")[0]
	mainType = strings.TrimSpace(mainType)

	for _, allowed := range allowedTypes {
		if strings.EqualFold(mainType, allowed) {
			return true
		}
	}

	return false
}

// isOriginAllowed checks if an origin is in the allowed list
func isOriginAllowed(origin string, allowedOrigins []string) bool {
	if origin == "" {
		return false
	}

	for _, allowed := range allowedOrigins {
		if allowed == "*" || allowed == origin {
			return true
		}

		// Support wildcard subdomains (e.g., *.example.com)
		if strings.HasPrefix(allowed, "*.") {
			domain := allowed[2:]
			if strings.HasSuffix(origin, "."+domain) || origin == domain {
				return true
			}
		}
	}

	return false
}

// LogSecurityEvent logs security-related events for monitoring
func LogSecurityEvent(eventType, clientIP, userAgent, details string) {
	// In a production environment, this would integrate with a security monitoring system
	// Security event logged: eventType, clientIP, userAgent, details
}
