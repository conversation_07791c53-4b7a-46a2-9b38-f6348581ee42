package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
)

// WriteJSONResponse writes a JSON response with the given status code and data
func WriteJSONResponse(w http.ResponseWriter, statusCode int, data interface{}) {
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	if err := json.NewEncoder(w).Encode(data); err != nil {
		// Log the error for debugging
		fmt.Printf("JSON encoding error: %v, data: %+v\n", err, data)
		// Don't call http.Error here as headers are already written
		// Just write a simple error message
		w.Write([]byte(`{"error": "Failed to encode JSON response"}`))
	}
}

// WriteErrorResponse writes an error response with the given status code and message
func WriteErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	errorResponse := map[string]interface{}{
		"error":   true,
		"message": message,
		"status":  statusCode,
	}
	WriteJSONResponse(w, statusCode, errorResponse)
}

// ParseJSONBody parses the JSON body of a request into the given struct
func ParseJSONBody(r *http.Request, v interface{}) error {
	if r.Body == nil {
		return fmt.Errorf("request body is empty")
	}

	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields()

	if err := decoder.Decode(v); err != nil {
		return fmt.Errorf("invalid JSON: %w", err)
	}

	return nil
}

// GetURLParam extracts a URL parameter from the request
func GetURLParam(r *http.Request, key string) string {
	return chi.URLParam(r, key)
}

// GetQueryParam extracts a query parameter from the request
func GetQueryParam(r *http.Request, key string) string {
	return r.URL.Query().Get(key)
}

// GetQueryParamInt extracts an integer query parameter from the request
func GetQueryParamInt(r *http.Request, key string, defaultValue int) int {
	value := r.URL.Query().Get(key)
	if value == "" {
		return defaultValue
	}

	intValue, err := strconv.Atoi(value)
	if err != nil {
		return defaultValue
	}

	return intValue
}

// parseUUID parses a string as a UUID
func parseUUID(s string) (uuid.UUID, error) {
	return uuid.Parse(s)
}

// GetURLParamAsInt extracts an integer URL parameter from the request
func GetURLParamAsInt(r *http.Request, key string) (int, error) {
	value := GetURLParam(r, key)
	if value == "" {
		return 0, fmt.Errorf("parameter '%s' is required", key)
	}

	intValue, err := strconv.Atoi(value)
	if err != nil {
		return 0, fmt.Errorf("parameter '%s' must be an integer", key)
	}

	return intValue, nil
}

// ValidateRequiredFields checks if required fields are present in a map
func ValidateRequiredFields(data map[string]interface{}, requiredFields []string) error {
	for _, field := range requiredFields {
		if value, exists := data[field]; !exists || value == nil || value == "" {
			return fmt.Errorf("required field '%s' is missing or empty", field)
		}
	}
	return nil
}

// HandleNotFound handles 404 errors
func HandleNotFound(w http.ResponseWriter, r *http.Request) {
	WriteErrorResponse(w, http.StatusNotFound, "Resource not found")
}

// HandleMethodNotAllowed handles 405 errors
func HandleMethodNotAllowed(w http.ResponseWriter, r *http.Request) {
	WriteErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
}
