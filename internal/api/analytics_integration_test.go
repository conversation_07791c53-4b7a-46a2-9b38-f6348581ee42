package api

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"test-spark-backend/internal/models"
	"test-spark-backend/internal/services"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockAnalyticsStore implements the Store interface for analytics testing
type MockAnalyticsStore struct {
	mock.Mock
}

// Implement required Store methods for analytics testing
func (m *MockAnalyticsStore) GetDashboardSummary(ctx context.Context, userID string) (*models.DashboardSummary, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*models.DashboardSummary), args.Error(1)
}

func (m *MockAnalyticsStore) GetUserTestHistory(ctx context.Context, userID string, limit int) ([]models.UserTestHistory, error) {
	args := m.Called(ctx, userID, limit)
	return args.Get(0).([]models.UserTestHistory), args.Error(1)
}

func (m *MockAnalyticsStore) GetUserTopicPerformances(ctx context.Context, userID string) ([]models.TopicPerformanceWithName, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.TopicPerformanceWithName), args.Error(1)
}

func (m *MockAnalyticsStore) GetStudyPatterns(ctx context.Context, userID string) (*models.StudyPattern, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*models.StudyPattern), args.Error(1)
}

func (m *MockAnalyticsStore) GetPerformanceTrends(ctx context.Context, userID string, timeFilter models.TimeFilter) ([]models.PerformanceTrend, error) {
	args := m.Called(ctx, userID, timeFilter)
	return args.Get(0).([]models.PerformanceTrend), args.Error(1)
}

func (m *MockAnalyticsStore) GetPerformanceTrendsWithDateRange(ctx context.Context, userID string, timeFilter models.TimeFilter, dateRange *models.DateRange) ([]models.PerformanceTrend, error) {
	args := m.Called(ctx, userID, timeFilter, dateRange)
	return args.Get(0).([]models.PerformanceTrend), args.Error(1)
}

func (m *MockAnalyticsStore) GetHeatmapData(ctx context.Context, userID string) ([]models.HeatmapData, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.HeatmapData), args.Error(1)
}

func (m *MockAnalyticsStore) GetSubjectComparisons(ctx context.Context, userID string) ([]models.SubjectComparison, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.SubjectComparison), args.Error(1)
}

func (m *MockAnalyticsStore) GetUserInsights(ctx context.Context, userID string, limit int) ([]models.AnalyticsInsight, error) {
	args := m.Called(ctx, userID, limit)
	return args.Get(0).([]models.AnalyticsInsight), args.Error(1)
}

func (m *MockAnalyticsStore) CreateInsight(ctx context.Context, insight *models.AnalyticsInsight) error {
	args := m.Called(ctx, insight)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetTimeFilteredTestHistory(ctx context.Context, userID string, timeFilter models.TimeFilter) ([]models.UserTestHistory, error) {
	args := m.Called(ctx, userID, timeFilter)
	return args.Get(0).([]models.UserTestHistory), args.Error(1)
}

func (m *MockAnalyticsStore) GetTimeFilteredTestHistoryWithDateRange(ctx context.Context, userID string, timeFilter models.TimeFilter, dateRange *models.DateRange) ([]models.UserTestHistory, error) {
	args := m.Called(ctx, userID, timeFilter, dateRange)
	return args.Get(0).([]models.UserTestHistory), args.Error(1)
}

func (m *MockAnalyticsStore) GetWeakestTopics(ctx context.Context, userID string, limit int) ([]models.TopicPerformanceWithName, error) {
	args := m.Called(ctx, userID, limit)
	return args.Get(0).([]models.TopicPerformanceWithName), args.Error(1)
}

// Ping implements the database health check method
func (m *MockAnalyticsStore) Ping(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

// Enhanced question methods (required by Store interface)
func (m *MockAnalyticsStore) GetSmartRandomQuestions(ctx context.Context, userID string, topicIDs []int, difficulties []models.DifficultyLevel, limit int, avoidRecentDays int) ([]models.Question, error) {
	args := m.Called(ctx, userID, topicIDs, difficulties, limit, avoidRecentDays)
	return args.Get(0).([]models.Question), args.Error(1)
}

func (m *MockAnalyticsStore) GetDiverseQuestions(ctx context.Context, userID string, topicIDs []int, difficulties []models.DifficultyLevel, limit int) ([]models.Question, error) {
	args := m.Called(ctx, userID, topicIDs, difficulties, limit)
	return args.Get(0).([]models.Question), args.Error(1)
}

func (m *MockAnalyticsStore) GetQuestionPoolSize(ctx context.Context, topicIDs []int, difficulties []models.DifficultyLevel) (int, error) {
	args := m.Called(ctx, topicIDs, difficulties)
	return args.Int(0), args.Error(1)
}

func (m *MockAnalyticsStore) RecordQuestionSeen(ctx context.Context, userID uuid.UUID, questionID uuid.UUID, responseTimeMs *int, isCorrect *bool) error {
	args := m.Called(ctx, userID, questionID, responseTimeMs, isCorrect)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetUserQuestionHistory(ctx context.Context, userID uuid.UUID, limit int) ([]models.UserQuestionHistory, error) {
	args := m.Called(ctx, userID, limit)
	return args.Get(0).([]models.UserQuestionHistory), args.Error(1)
}

func (m *MockAnalyticsStore) GetUserSeenQuestionIDs(ctx context.Context, userID uuid.UUID, topicIDs []int, difficulties []models.DifficultyLevel, daysSince int) ([]uuid.UUID, error) {
	args := m.Called(ctx, userID, topicIDs, difficulties, daysSince)
	return args.Get(0).([]uuid.UUID), args.Error(1)
}

// Implement missing Store interface methods

// UserRepository methods
func (m *MockAnalyticsStore) CreateUser(ctx context.Context, user *models.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetUserByID(ctx context.Context, id string) (*models.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockAnalyticsStore) GetUserByEmail(ctx context.Context, email string) (*models.User, error) {
	args := m.Called(ctx, email)
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockAnalyticsStore) UpdateUser(ctx context.Context, user *models.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockAnalyticsStore) CreateUserProfile(ctx context.Context, profile *models.UserProfile) error {
	args := m.Called(ctx, profile)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetUserProfile(ctx context.Context, userID string) (*models.UserProfile, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*models.UserProfile), args.Error(1)
}

func (m *MockAnalyticsStore) UpdateUserProfile(ctx context.Context, profile *models.UserProfile) error {
	args := m.Called(ctx, profile)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetUserWithProfile(ctx context.Context, userID string) (*models.UserWithProfile, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*models.UserWithProfile), args.Error(1)
}

// AuthRepository methods
func (m *MockAnalyticsStore) CreateRefreshToken(ctx context.Context, token *models.RefreshToken) error {
	args := m.Called(ctx, token)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetRefreshToken(ctx context.Context, tokenHash string) (*models.RefreshToken, error) {
	args := m.Called(ctx, tokenHash)
	return args.Get(0).(*models.RefreshToken), args.Error(1)
}

func (m *MockAnalyticsStore) RevokeRefreshToken(ctx context.Context, tokenHash string) error {
	args := m.Called(ctx, tokenHash)
	return args.Error(0)
}

func (m *MockAnalyticsStore) RevokeAllUserRefreshTokens(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

// ContentRepository methods
func (m *MockAnalyticsStore) GetSubjects(ctx context.Context) ([]models.Subject, error) {
	args := m.Called(ctx)
	return args.Get(0).([]models.Subject), args.Error(1)
}

func (m *MockAnalyticsStore) GetSubjectByID(ctx context.Context, id int) (*models.Subject, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Subject), args.Error(1)
}

func (m *MockAnalyticsStore) GetExams(ctx context.Context) ([]models.Exam, error) {
	args := m.Called(ctx)
	return args.Get(0).([]models.Exam), args.Error(1)
}

func (m *MockAnalyticsStore) GetExamByID(ctx context.Context, id int) (*models.Exam, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Exam), args.Error(1)
}

func (m *MockAnalyticsStore) GetTopicsBySubject(ctx context.Context, subjectID int) ([]models.Topic, error) {
	args := m.Called(ctx, subjectID)
	return args.Get(0).([]models.Topic), args.Error(1)
}

func (m *MockAnalyticsStore) GetTopicByID(ctx context.Context, id int) (*models.Topic, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Topic), args.Error(1)
}

func (m *MockAnalyticsStore) GetUserPreferences(ctx context.Context, userID string) ([]models.UserPreference, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.UserPreference), args.Error(1)
}

func (m *MockAnalyticsStore) CreateUserPreference(ctx context.Context, preference *models.UserPreference) error {
	args := m.Called(ctx, preference)
	return args.Error(0)
}

// TestRepository methods
func (m *MockAnalyticsStore) CreateTest(ctx context.Context, test *models.Test) error {
	args := m.Called(ctx, test)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetTestByID(ctx context.Context, id string) (*models.Test, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Test), args.Error(1)
}

func (m *MockAnalyticsStore) GetTestsByUserID(ctx context.Context, userID string) ([]models.Test, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.Test), args.Error(1)
}

func (m *MockAnalyticsStore) UpdateTestStatus(ctx context.Context, testID string, status models.TestStatus) error {
	args := m.Called(ctx, testID, status)
	return args.Error(0)
}

func (m *MockAnalyticsStore) CompleteTest(ctx context.Context, testID string) error {
	args := m.Called(ctx, testID)
	return args.Error(0)
}

func (m *MockAnalyticsStore) CreateTestQuestion(ctx context.Context, testQuestion *models.TestQuestion) error {
	args := m.Called(ctx, testQuestion)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetTestQuestions(ctx context.Context, testID string) ([]models.TestQuestion, error) {
	args := m.Called(ctx, testID)
	return args.Get(0).([]models.TestQuestion), args.Error(1)
}

func (m *MockAnalyticsStore) GetTestQuestionByOrder(ctx context.Context, testID string, questionOrder int) (*models.TestQuestionWithDetails, error) {
	args := m.Called(ctx, testID, questionOrder)
	return args.Get(0).(*models.TestQuestionWithDetails), args.Error(1)
}

// QuestionRepository methods
func (m *MockAnalyticsStore) CreateQuestion(ctx context.Context, question *models.Question) error {
	args := m.Called(ctx, question)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetQuestionByID(ctx context.Context, id string) (*models.Question, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Question), args.Error(1)
}

func (m *MockAnalyticsStore) GetQuestionsByTopicID(ctx context.Context, topicID int) ([]models.Question, error) {
	args := m.Called(ctx, topicID)
	return args.Get(0).([]models.Question), args.Error(1)
}

func (m *MockAnalyticsStore) GetQuestionsByDifficulty(ctx context.Context, difficulty models.DifficultyLevel) ([]models.Question, error) {
	args := m.Called(ctx, difficulty)
	return args.Get(0).([]models.Question), args.Error(1)
}

func (m *MockAnalyticsStore) GetRandomQuestions(ctx context.Context, topicIDs []int, difficulties []models.DifficultyLevel, limit int) ([]models.Question, error) {
	args := m.Called(ctx, topicIDs, difficulties, limit)
	return args.Get(0).([]models.Question), args.Error(1)
}

// AnswerRepository methods
func (m *MockAnalyticsStore) CreateUserAnswer(ctx context.Context, answer *models.UserAnswer) error {
	args := m.Called(ctx, answer)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetUserAnswer(ctx context.Context, testQuestionID int) (*models.UserAnswer, error) {
	args := m.Called(ctx, testQuestionID)
	return args.Get(0).(*models.UserAnswer), args.Error(1)
}

func (m *MockAnalyticsStore) GetUserAnswersByTestID(ctx context.Context, testID string) ([]models.UserAnswer, error) {
	args := m.Called(ctx, testID)
	return args.Get(0).([]models.UserAnswer), args.Error(1)
}

func (m *MockAnalyticsStore) GetTestQuestionID(ctx context.Context, testID string, questionID string) (int, error) {
	args := m.Called(ctx, testID, questionID)
	return args.Int(0), args.Error(1)
}

// Additional AnalyticsRepository methods
func (m *MockAnalyticsStore) CreateTestResult(ctx context.Context, result *models.TestResult) error {
	args := m.Called(ctx, result)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetTestResult(ctx context.Context, testID string) (*models.TestResult, error) {
	args := m.Called(ctx, testID)
	return args.Get(0).(*models.TestResult), args.Error(1)
}

func (m *MockAnalyticsStore) GetTopicPerformance(ctx context.Context, userID string, topicID int) (*models.TopicPerformanceSummary, error) {
	args := m.Called(ctx, userID, topicID)
	return args.Get(0).(*models.TopicPerformanceSummary), args.Error(1)
}

// AdaptiveLearningRepository methods
func (m *MockAnalyticsStore) CreateAdaptiveLearningEngine(ctx context.Context, engine *models.AdaptiveLearningEngine) error {
	args := m.Called(ctx, engine)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetAdaptiveLearningEngine(ctx context.Context, userID uuid.UUID) (*models.AdaptiveLearningEngine, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*models.AdaptiveLearningEngine), args.Error(1)
}

func (m *MockAnalyticsStore) UpdateAdaptiveLearningEngine(ctx context.Context, engine *models.AdaptiveLearningEngine) error {
	args := m.Called(ctx, engine)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetMasteryLevel(ctx context.Context, userID uuid.UUID, topicID int) (*models.MasteryLevel, error) {
	args := m.Called(ctx, userID, topicID)
	return args.Get(0).(*models.MasteryLevel), args.Error(1)
}

func (m *MockAnalyticsStore) UpdateMasteryLevel(ctx context.Context, userID uuid.UUID, mastery *models.MasteryLevel) error {
	args := m.Called(ctx, userID, mastery)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetMasteryLevelsByUser(ctx context.Context, userID uuid.UUID) ([]models.MasteryLevel, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.MasteryLevel), args.Error(1)
}

func (m *MockAnalyticsStore) GetUserResponsesByTopic(ctx context.Context, userID uuid.UUID, topicID int, days int) ([]models.AdaptiveResponse, error) {
	args := m.Called(ctx, userID, topicID, days)
	return args.Get(0).([]models.AdaptiveResponse), args.Error(1)
}

func (m *MockAnalyticsStore) CreateAdaptiveResponse(ctx context.Context, response *models.AdaptiveResponse) error {
	args := m.Called(ctx, response)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetLearningPath(ctx context.Context, pathID uuid.UUID) (*models.LearningPath, error) {
	args := m.Called(ctx, pathID)
	return args.Get(0).(*models.LearningPath), args.Error(1)
}

func (m *MockAnalyticsStore) CreateLearningPath(ctx context.Context, path *models.LearningPath) error {
	args := m.Called(ctx, path)
	return args.Error(0)
}

func (m *MockAnalyticsStore) UpdateLearningPath(ctx context.Context, path *models.LearningPath) error {
	args := m.Called(ctx, path)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetMasteryLevelsBySubject(ctx context.Context, userID uuid.UUID, subjectID int) ([]models.MasteryLevel, error) {
	args := m.Called(ctx, userID, subjectID)
	return args.Get(0).([]models.MasteryLevel), args.Error(1)
}

func (m *MockAnalyticsStore) GetLearningPathsByUser(ctx context.Context, userID uuid.UUID) ([]models.LearningPath, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.LearningPath), args.Error(1)
}

func (m *MockAnalyticsStore) DeleteLearningPath(ctx context.Context, pathID uuid.UUID) error {
	args := m.Called(ctx, pathID)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetUserResponsesByTimeRange(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) ([]models.AdaptiveResponse, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Get(0).([]models.AdaptiveResponse), args.Error(1)
}

func (m *MockAnalyticsStore) GetRecentResponses(ctx context.Context, userID uuid.UUID, limit int) ([]models.AdaptiveResponse, error) {
	args := m.Called(ctx, userID, limit)
	return args.Get(0).([]models.AdaptiveResponse), args.Error(1)
}

func (m *MockAnalyticsStore) CreateSpacedRepetitionSchedule(ctx context.Context, schedule *models.SpacedRepetitionSchedule) error {
	args := m.Called(ctx, schedule)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetSpacedRepetitionSchedule(ctx context.Context, userID uuid.UUID, topicID int) (*models.SpacedRepetitionSchedule, error) {
	args := m.Called(ctx, userID, topicID)
	return args.Get(0).(*models.SpacedRepetitionSchedule), args.Error(1)
}

func (m *MockAnalyticsStore) UpdateSpacedRepetitionSchedule(ctx context.Context, schedule *models.SpacedRepetitionSchedule) error {
	args := m.Called(ctx, schedule)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetDueReviews(ctx context.Context, userID uuid.UUID) ([]models.SpacedRepetitionSchedule, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.SpacedRepetitionSchedule), args.Error(1)
}

func (m *MockAnalyticsStore) GetQuestionIRTParameters(ctx context.Context, questionID uuid.UUID) (*models.IRTParameters, error) {
	args := m.Called(ctx, questionID)
	return args.Get(0).(*models.IRTParameters), args.Error(1)
}

func (m *MockAnalyticsStore) UpdateQuestionIRTParameters(ctx context.Context, questionID uuid.UUID, params *models.IRTParameters) error {
	args := m.Called(ctx, questionID, params)
	return args.Error(0)
}

func (m *MockAnalyticsStore) GetUserAbilityEstimate(ctx context.Context, userID uuid.UUID, subjectID int) (float64, float64, error) {
	args := m.Called(ctx, userID, subjectID)
	return args.Get(0).(float64), args.Get(1).(float64), args.Error(2)
}

func (m *MockAnalyticsStore) UpdateUserAbilityEstimate(ctx context.Context, userID uuid.UUID, subjectID int, ability, confidence float64) error {
	args := m.Called(ctx, userID, subjectID, ability, confidence)
	return args.Error(0)
}

// Test helper functions
func createTestDashboard() *models.DashboardSummary {
	return &models.DashboardSummary{
		OverallProficiency: 75.5,
		TotalTestsTaken:    25,
		AverageScore:       78.2,
		StrongestSubject:   stringPtr("Mathematics"),
		WeakestSubject:     stringPtr("Physics"),
		RecentTests:        []models.UserTestHistory{},
		TopicPerformance:   []models.TopicPerformanceWithName{},
	}
}

func createTestPerformanceTrends() []models.PerformanceTrend {
	return []models.PerformanceTrend{
		{
			Period:    "Week 1",
			Date:      time.Now().Add(-7 * 24 * time.Hour),
			Subjects:  map[string]float64{"Mathematics": 75.0, "Physics": 70.0},
			TestCount: 3,
		},
		{
			Period:    "Week 2",
			Date:      time.Now(),
			Subjects:  map[string]float64{"Mathematics": 80.0, "Physics": 75.0},
			TestCount: 4,
		},
	}
}

func createTestHeatmapData() []models.HeatmapData {
	return []models.HeatmapData{
		{
			SubjectName: "Mathematics",
			Topics: []models.HeatmapTopicPerformance{
				{TopicName: "Algebra", ProficiencyScore: 85.0},
				{TopicName: "Geometry", ProficiencyScore: 70.0},
			},
		},
	}
}

func stringPtr(s string) *string {
	return &s
}

// Integration Tests

func TestAnalyticsHandler_GetAdvancedAnalytics_Integration(t *testing.T) {
	tests := []struct {
		name             string
		userID           string
		timeFilter       string
		setupMocks       func(*MockAnalyticsStore)
		expectedStatus   int
		validateResponse func(*testing.T, []byte)
	}{
		{
			name:       "successful advanced analytics retrieval",
			userID:     "550e8400-e29b-41d4-a716-446655440000",
			timeFilter: "period=month",
			setupMocks: func(mockStore *MockAnalyticsStore) {
				// Mock all required calls for advanced analytics
				mockStore.On("GetPerformanceTrends", mock.Anything, "550e8400-e29b-41d4-a716-446655440000", models.TimeFilterMonth).
					Return(createTestPerformanceTrends(), nil)

				mockStore.On("GetUserTestHistory", mock.Anything, "550e8400-e29b-41d4-a716-446655440000", 20).
					Return([]models.UserTestHistory{}, nil)

				mockStore.On("GetUserTopicPerformances", mock.Anything, "550e8400-e29b-41d4-a716-446655440000").
					Return([]models.TopicPerformanceWithName{}, nil)

				mockStore.On("GetStudyPatterns", mock.Anything, "550e8400-e29b-41d4-a716-446655440000").
					Return(&models.StudyPattern{
						OptimalStudyHour:    14,
						AverageSessionTime:  30,
						ConsistencyScore:    75.0,
						BestPerformanceDay:  "Wednesday",
						PreferredDifficulty: "medium",
						CurrentStreak:       5,
						LongestStreak:       12,
						WeeklyTestCount:     3.5,
					}, nil)

				mockStore.On("GetHeatmapData", mock.Anything, "550e8400-e29b-41d4-a716-446655440000").
					Return(createTestHeatmapData(), nil)

				mockStore.On("GetSubjectComparisons", mock.Anything, "550e8400-e29b-41d4-a716-446655440000").
					Return([]models.SubjectComparison{}, nil)

				// Mock for calculateOverallImprovement
				mockStore.On("GetTimeFilteredTestHistory", mock.Anything, "550e8400-e29b-41d4-a716-446655440000", models.TimeFilterMonth).
					Return([]models.UserTestHistory{}, nil)

				// Mock for recommendation engine
				mockStore.On("GetDashboardSummary", mock.Anything, "550e8400-e29b-41d4-a716-446655440000").
					Return(createTestDashboard(), nil)

				// Mock for pattern recognition service (called by recommendation engine)
				mockStore.On("GetUserTestHistory", mock.Anything, "550e8400-e29b-41d4-a716-446655440000", 50).
					Return([]models.UserTestHistory{}, nil)

				// Mock for insight engine (called by analytics service)
				mockStore.On("GetPerformanceTrends", mock.Anything, "550e8400-e29b-41d4-a716-446655440000", models.TimeFilterMonth).
					Return(createTestPerformanceTrends(), nil)

			},
			expectedStatus: http.StatusOK,
			validateResponse: func(t *testing.T, body []byte) {
				var response models.AdvancedAnalytics
				err := json.Unmarshal(body, &response)
				assert.NoError(t, err)

				// Verify response structure
				assert.NotNil(t, response.PerformanceTrends)
				// Insights and Recommendations can be nil (empty) in JSON, which is valid
				assert.NotNil(t, response.StudyPatterns)
				assert.NotNil(t, response.HeatmapData)
				assert.NotNil(t, response.SubjectComparisons)

				// Verify that the response has the expected structure
				assert.GreaterOrEqual(t, len(response.PerformanceTrends), 0)
				assert.GreaterOrEqual(t, len(response.HeatmapData), 0)
			},
		},
		{
			name:       "custom date range analytics",
			userID:     "550e8400-e29b-41d4-a716-446655440001",
			timeFilter: "period=custom&start_date=2024-01-01&end_date=2024-01-31",
			setupMocks: func(mockStore *MockAnalyticsStore) {
				// The analytics service calls GetPerformanceTrends for custom filters (not GetPerformanceTrendsWithDateRange)
				mockStore.On("GetPerformanceTrends", mock.Anything, "550e8400-e29b-41d4-a716-446655440001", models.TimeFilterCustom).
					Return(createTestPerformanceTrends(), nil)

				// Setup other required mocks
				mockStore.On("GetUserTestHistory", mock.Anything, "550e8400-e29b-41d4-a716-446655440001", 20).
					Return([]models.UserTestHistory{}, nil)
				mockStore.On("GetUserTopicPerformances", mock.Anything, "550e8400-e29b-41d4-a716-446655440001").
					Return([]models.TopicPerformanceWithName{}, nil)
				mockStore.On("GetStudyPatterns", mock.Anything, "550e8400-e29b-41d4-a716-446655440001").
					Return(&models.StudyPattern{WeeklyTestCount: 2.0}, nil)
				mockStore.On("GetHeatmapData", mock.Anything, "550e8400-e29b-41d4-a716-446655440001").
					Return([]models.HeatmapData{}, nil)
				mockStore.On("GetSubjectComparisons", mock.Anything, "550e8400-e29b-41d4-a716-446655440001").
					Return([]models.SubjectComparison{}, nil)

				// Mock for calculateOverallImprovement
				mockStore.On("GetTimeFilteredTestHistory", mock.Anything, "550e8400-e29b-41d4-a716-446655440001", models.TimeFilterCustom).
					Return([]models.UserTestHistory{}, nil)

				// Mock for recommendation engine
				mockStore.On("GetDashboardSummary", mock.Anything, "550e8400-e29b-41d4-a716-446655440001").
					Return(createTestDashboard(), nil)

				// Mock for pattern recognition service (called by recommendation engine)
				mockStore.On("GetUserTestHistory", mock.Anything, "550e8400-e29b-41d4-a716-446655440001", 50).
					Return([]models.UserTestHistory{}, nil)

				// Mock for insight engine (called by analytics service)
				mockStore.On("GetPerformanceTrends", mock.Anything, "550e8400-e29b-41d4-a716-446655440001", models.TimeFilterMonth).
					Return(createTestPerformanceTrends(), nil)

			},
			expectedStatus: http.StatusOK,
			validateResponse: func(t *testing.T, body []byte) {
				var response models.AdvancedAnalytics
				err := json.Unmarshal(body, &response)
				assert.NoError(t, err)
				assert.NotNil(t, response.PerformanceTrends)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockStore := new(MockAnalyticsStore)
			tt.setupMocks(mockStore)

			analyticsService := services.NewAnalyticsService(mockStore)
			// For tests, we use nil for cached analytics service
			handler := NewAnalysisHandler(mockStore, analyticsService, nil)

			// Create request
			req := httptest.NewRequest("GET", "/analytics/advanced?"+tt.timeFilter, nil)

			// Add user ID to context using the correct context key
			ctx := context.WithValue(req.Context(), UserIDKey, tt.userID)
			req = req.WithContext(ctx)

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute
			handler.GetAdvancedAnalytics(rr, req)

			// Verify
			assert.Equal(t, tt.expectedStatus, rr.Code)

			if tt.expectedStatus == http.StatusOK && tt.validateResponse != nil {
				tt.validateResponse(t, rr.Body.Bytes())
			}

			mockStore.AssertExpectations(t)
		})
	}
}

func TestAnalyticsHandler_GetPerformanceTrends_Integration(t *testing.T) {
	tests := []struct {
		name             string
		userID           string
		queryParams      string
		setupMocks       func(*MockAnalyticsStore)
		expectedStatus   int
		validateResponse func(*testing.T, []byte)
	}{
		{
			name:        "get monthly performance trends",
			userID:      "test-user-789",
			queryParams: "period=month",
			setupMocks: func(mockStore *MockAnalyticsStore) {
				mockStore.On("GetPerformanceTrends", mock.Anything, "test-user-789", models.TimeFilterMonth).
					Return(createTestPerformanceTrends(), nil)
			},
			expectedStatus: http.StatusOK,
			validateResponse: func(t *testing.T, body []byte) {
				var trends []models.PerformanceTrend
				err := json.Unmarshal(body, &trends)
				assert.NoError(t, err)
				assert.Len(t, trends, 2)
				assert.Equal(t, "Week 1", trends[0].Period)
				assert.Equal(t, 3, trends[0].TestCount)
			},
		},
		{
			name:        "get custom date range trends",
			userID:      "test-user-101",
			queryParams: "period=custom&start_date=2024-01-01&end_date=2024-01-31",
			setupMocks: func(mockStore *MockAnalyticsStore) {
				mockStore.On("GetPerformanceTrendsWithDateRange", mock.Anything, "test-user-101", models.TimeFilterCustom, mock.AnythingOfType("*models.DateRange")).
					Return(createTestPerformanceTrends(), nil)
			},
			expectedStatus: http.StatusOK,
			validateResponse: func(t *testing.T, body []byte) {
				var trends []models.PerformanceTrend
				err := json.Unmarshal(body, &trends)
				assert.NoError(t, err)
				assert.NotEmpty(t, trends)
			},
		},
		{
			name:           "invalid time filter",
			userID:         "test-user-102",
			queryParams:    "period=invalid",
			setupMocks:     func(mockStore *MockAnalyticsStore) {},
			expectedStatus: http.StatusBadRequest,
			validateResponse: func(t *testing.T, body []byte) {
				assert.Contains(t, string(body), "invalid time filter")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockStore := new(MockAnalyticsStore)
			tt.setupMocks(mockStore)

			analyticsService := services.NewAnalyticsService(mockStore)
			handler := NewAnalysisHandler(mockStore, analyticsService, nil)

			// Create request
			req := httptest.NewRequest("GET", "/analytics/trends?"+tt.queryParams, nil)

			// Add user ID to context using the correct context key
			ctx := context.WithValue(req.Context(), UserIDKey, tt.userID)
			req = req.WithContext(ctx)

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute
			handler.GetPerformanceTrends(rr, req)

			// Verify
			assert.Equal(t, tt.expectedStatus, rr.Code)

			if tt.validateResponse != nil {
				tt.validateResponse(t, rr.Body.Bytes())
			}

			mockStore.AssertExpectations(t)
		})
	}
}

func TestAnalyticsFlow_EndToEnd(t *testing.T) {
	// This test simulates a complete analytics flow from API to service to database
	mockStore := new(MockAnalyticsStore)

	// Setup comprehensive mocks for full analytics flow
	userID := "550e8400-e29b-41d4-a716-446655440002"

	// Dashboard data
	mockStore.On("GetDashboardSummary", mock.Anything, userID).
		Return(createTestDashboard(), nil)

	// Performance trends
	mockStore.On("GetPerformanceTrends", mock.Anything, userID, models.TimeFilterMonth).
		Return(createTestPerformanceTrends(), nil)

	// Study patterns
	mockStore.On("GetStudyPatterns", mock.Anything, userID).
		Return(&models.StudyPattern{
			OptimalStudyHour:   14,
			AverageSessionTime: 30,
			ConsistencyScore:   75.0,
			WeeklyTestCount:    3.5,
		}, nil)

	// Test history for insights
	mockStore.On("GetUserTestHistory", mock.Anything, userID, 20).
		Return([]models.UserTestHistory{}, nil)
	mockStore.On("GetUserTestHistory", mock.Anything, userID, 50).
		Return([]models.UserTestHistory{}, nil)

	// Topic performances
	mockStore.On("GetUserTopicPerformances", mock.Anything, userID).
		Return([]models.TopicPerformanceWithName{}, nil)

	// Other analytics data
	mockStore.On("GetHeatmapData", mock.Anything, userID).
		Return(createTestHeatmapData(), nil)
	mockStore.On("GetSubjectComparisons", mock.Anything, userID).
		Return([]models.SubjectComparison{}, nil)

	// Additional mocks needed for GetAdvancedAnalytics
	mockStore.On("GetTimeFilteredTestHistory", mock.Anything, userID, models.TimeFilterMonth).
		Return([]models.UserTestHistory{}, nil)
	mockStore.On("GetDashboardSummary", mock.Anything, userID).
		Return(createTestDashboard(), nil)

	// Create services and handlers
	analyticsService := services.NewAnalyticsService(mockStore)
	handler := NewAnalysisHandler(mockStore, analyticsService, nil)

	// Test multiple endpoints in sequence
	endpoints := []struct {
		path   string
		method string
	}{
		{"/analytics/advanced?period=month", "GET"},
		{"/analytics/trends?period=week", "GET"},
		{"/analysis/dashboard", "GET"},
	}

	for _, endpoint := range endpoints {
		t.Run("endpoint_"+endpoint.path, func(t *testing.T) {
			req := httptest.NewRequest(endpoint.method, endpoint.path, nil)
			ctx := context.WithValue(req.Context(), UserIDKey, userID)
			req = req.WithContext(ctx)

			rr := httptest.NewRecorder()

			// Route to appropriate handler
			switch {
			case endpoint.path == "/analysis/dashboard":
				handler.GetDashboard(rr, req)
			case endpoint.path == "/analytics/advanced?period=month":
				handler.GetAdvancedAnalytics(rr, req)
			case endpoint.path == "/analytics/trends?period=week":
				// Update mock for week filter
				mockStore.On("GetPerformanceTrends", mock.Anything, userID, models.TimeFilterWeek).
					Return(createTestPerformanceTrends(), nil)
				handler.GetPerformanceTrends(rr, req)
			}

			assert.Equal(t, http.StatusOK, rr.Code)
			assert.Contains(t, rr.Header().Get("Content-Type"), "application/json")
		})
	}

	mockStore.AssertExpectations(t)
}
