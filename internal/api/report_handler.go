package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"test-spark-backend/internal/models"
	"test-spark-backend/internal/services"

	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
)

// ReportHandler handles report-related HTTP requests
type ReportHandler struct {
	reportService *services.ReportService
}

// NewReportHandler creates a new report handler
func NewReportHandler(reportService *services.ReportService) *ReportHandler {
	return &ReportHandler{
		reportService: reportService,
	}
}

// GenerateReport handles POST /api/reports/generate
func (rh *ReportHandler) GenerateReport(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context (set by auth middleware)
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}

	// Parse user ID
	parsedUserID, err := uuid.Parse(userID)
	if err != nil {
		http.Error(w, "Invalid user ID", http.StatusBadRequest)
		return
	}

	// Parse request body
	var req models.ReportRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Set user ID from context
	req.UserID = parsedUserID

	// Validate request
	if err := rh.validateReportRequest(&req); err != nil {
		http.Error(w, fmt.Sprintf("Invalid request: %v", err), http.StatusBadRequest)
		return
	}

	// Generate report
	response, err := rh.reportService.GenerateReport(r.Context(), &req)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to generate report: %v", err), http.StatusInternalServerError)
		return
	}

	// Return response
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// DownloadReport handles GET /api/reports/download/{reportId}
func (rh *ReportHandler) DownloadReport(w http.ResponseWriter, r *http.Request) {
	reportID := chi.URLParam(r, "reportId")
	if reportID == "" {
		http.Error(w, "Report ID is required", http.StatusBadRequest)
		return
	}

	// For this implementation, we'll look for files in the generated_reports directory
	// In production, you might want to store report metadata in a database
	reportsDir := "generated_reports"

	// Find the report file - prioritize PDF files for downloads
	var filePath string
	var contentType string

	// Check for PDF file first (since this is a download endpoint)
	pdfPath := filepath.Join(reportsDir, fmt.Sprintf("report_%s.pdf", reportID))
	if _, err := os.Stat(pdfPath); err == nil {
		filePath = pdfPath
		contentType = "application/pdf"
	} else {
		// Fallback to HTML file if PDF doesn't exist
		htmlPath := filepath.Join(reportsDir, fmt.Sprintf("report_%s.html", reportID))
		if _, err := os.Stat(htmlPath); err == nil {
			filePath = htmlPath
			contentType = "text/html"
		}
	}

	if filePath == "" {
		http.Error(w, "Report not found", http.StatusNotFound)
		return
	}

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.Error(w, "Report file not found", http.StatusNotFound)
		return
	}

	// Read file
	fileData, err := os.ReadFile(filePath)
	if err != nil {
		http.Error(w, "Failed to read report file", http.StatusInternalServerError)
		return
	}

	// Set headers
	w.Header().Set("Content-Type", contentType)
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filepath.Base(filePath)))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(fileData)))

	// Write file data
	w.Write(fileData)
}

// GetReportTemplates handles GET /api/reports/templates
func (rh *ReportHandler) GetReportTemplates(w http.ResponseWriter, r *http.Request) {
	// Create a safe copy of templates to avoid any potential circular references
	templates := make([]models.ReportTemplate, len(models.DefaultReportTemplates))
	copy(templates, models.DefaultReportTemplates)

	// Create response structure
	response := struct {
		Templates []models.ReportTemplate `json:"templates"`
		Sections  map[string]string       `json:"sections"`
	}{
		Templates: templates,
		Sections:  models.ReportSections,
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
}

// PreviewReport handles POST /api/reports/preview
func (rh *ReportHandler) PreviewReport(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}

	// Parse user ID
	parsedUserID, err := uuid.Parse(userID)
	if err != nil {
		http.Error(w, "Invalid user ID", http.StatusBadRequest)
		return
	}

	// Parse request body
	var req models.ReportRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Set user ID and force HTML format for preview
	req.UserID = parsedUserID
	req.Format = models.ReportFormatHTML

	// Generate report data (without saving to file)
	reportData, err := rh.reportService.GatherReportData(r.Context(), &req)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to gather report data: %v", err), http.StatusInternalServerError)
		return
	}

	// Generate HTML preview instead of raw JSON
	htmlContent, err := rh.reportService.GenerateHTMLPreview(reportData)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to generate HTML preview: %v", err), http.StatusInternalServerError)
		return
	}

	// Return HTML content for preview
	w.Header().Set("Content-Type", "text/html")
	w.Write([]byte(htmlContent))
}

// validateReportRequest validates the report request
func (rh *ReportHandler) validateReportRequest(req *models.ReportRequest) error {
	if req.UserID == uuid.Nil {
		return fmt.Errorf("user ID is required")
	}

	if req.ReportType == "" {
		req.ReportType = models.ReportTypePerformance // Default
	}

	if req.Format == "" {
		req.Format = models.ReportFormatHTML // Default
	}

	if req.TimeFilter == "" {
		req.TimeFilter = models.TimeFilterMonth // Default
	}

	// Validate report type
	validTypes := map[models.ReportType]bool{
		models.ReportTypePerformance: true,
		models.ReportTypeProgress:    true,
		models.ReportTypeDetailed:    true,
		models.ReportTypeComparative: true,
	}
	if !validTypes[req.ReportType] {
		return fmt.Errorf("invalid report type: %s", req.ReportType)
	}

	// Validate format
	validFormats := map[models.ReportFormat]bool{
		models.ReportFormatHTML: true,
		models.ReportFormatPDF:  true,
	}
	if !validFormats[req.Format] {
		return fmt.Errorf("invalid report format: %s", req.Format)
	}

	// Validate time filter
	validTimeFilters := map[models.TimeFilter]bool{
		models.TimeFilterWeek:    true,
		models.TimeFilterMonth:   true,
		models.TimeFilterQuarter: true,
		models.TimeFilterYear:    true,
		models.TimeFilterCustom:  true,
	}
	if !validTimeFilters[req.TimeFilter] {
		return fmt.Errorf("invalid time filter: %s", req.TimeFilter)
	}

	// If custom time filter, validate date range
	if req.TimeFilter == models.TimeFilterCustom && req.DateRange == nil {
		return fmt.Errorf("date range is required for custom time filter")
	}

	return nil
}

// GetReportHistory handles GET /api/reports/history
func (rh *ReportHandler) GetReportHistory(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}

	// For this implementation, we'll scan the generated_reports directory
	// In production, you would store this in a database
	reportsDir := "generated_reports"

	var reports []map[string]interface{}

	// Scan directory for user's reports
	if entries, err := os.ReadDir(reportsDir); err == nil {
		for _, entry := range entries {
			if entry.IsDir() {
				continue
			}

			filename := entry.Name()
			// Check if this file belongs to the current user (simple check)
			if strings.Contains(filename, userID) {
				info, _ := entry.Info()

				// Determine format from extension
				var format string
				if strings.HasSuffix(filename, ".html") {
					format = "html"
				} else if strings.HasSuffix(filename, ".pdf") {
					format = "pdf"
				} else {
					continue
				}

				reports = append(reports, map[string]interface{}{
					"filename":     filename,
					"format":       format,
					"size":         info.Size(),
					"generated_at": info.ModTime(),
					"download_url": fmt.Sprintf("/api/reports/download/%s", strings.TrimSuffix(filename, filepath.Ext(filename))),
				})
			}
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"reports": reports,
	})
}
