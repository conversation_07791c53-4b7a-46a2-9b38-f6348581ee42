package config

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"

	"test-spark-backend/internal/types"
)

// AIConfig holds the complete AI configuration
type AIConfig struct {
	Provider types.AIProviderConfig `json:"provider"`
	Features FeatureConfig          `json:"features"`
	Limits   LimitConfig            `json:"limits"`
}

// FeatureConfig holds feature-specific configuration
type FeatureConfig struct {
	EnableGradeAdaptation bool `json:"enable_grade_adaptation"`
	EnableBoardSupport    bool `json:"enable_board_support"`
	EnableFallback        bool `json:"enable_fallback"`
	EnableValidation      bool `json:"enable_validation"`
	EnableLogging         bool `json:"enable_logging"`
}

// LimitConfig holds rate limiting and quota configuration
type LimitConfig struct {
	MaxQuestionsPerRequest int `json:"max_questions_per_request"`
	MaxRequestsPerMinute   int `json:"max_requests_per_minute"`
	MaxTokensPerRequest    int `json:"max_tokens_per_request"`
	TimeoutSeconds         int `json:"timeout_seconds"`
}

// LoadAIConfig loads AI configuration from environment variables and defaults
func LoadAIConfig() (*AIConfig, error) {
	config := &AIConfig{
		Provider: *types.DefaultAIProviderConfig(),
		Features: FeatureConfig{
			EnableGradeAdaptation: true,
			EnableBoardSupport:    true,
			EnableFallback:        true,
			EnableValidation:      true,
			EnableLogging:         true,
		},
		Limits: LimitConfig{
			MaxQuestionsPerRequest: 50,
			MaxRequestsPerMinute:   60,
			MaxTokensPerRequest:    4000,
			TimeoutSeconds:         60,
		},
	}

	// Load provider configuration from environment
	if err := loadProviderConfig(&config.Provider); err != nil {
		return nil, fmt.Errorf("failed to load provider config: %w", err)
	}

	// Load feature configuration from environment
	loadFeatureConfig(&config.Features)

	// Load limit configuration from environment
	loadLimitConfig(&config.Limits)

	// Validate configuration
	if err := validateAIConfig(config); err != nil {
		return nil, fmt.Errorf("invalid AI configuration: %w", err)
	}

	return config, nil
}

// loadProviderConfig loads provider-specific configuration from environment
func loadProviderConfig(config *types.AIProviderConfig) error {
	// Primary provider
	if provider := os.Getenv("AI_PRIMARY_PROVIDER"); provider != "" {
		config.PrimaryProvider = provider
	}

	// Fallback provider
	if provider := os.Getenv("AI_FALLBACK_PROVIDER"); provider != "" {
		config.FallbackProvider = provider
	}

	// Global settings
	if enableFallback := os.Getenv("AI_ENABLE_FALLBACK"); enableFallback != "" {
		config.EnableFallback = strings.ToLower(enableFallback) == "true"
	}

	if timeout := os.Getenv("AI_TIMEOUT_SECONDS"); timeout != "" {
		if val, err := strconv.Atoi(timeout); err == nil {
			config.TimeoutSeconds = val
		}
	}

	if retries := os.Getenv("AI_MAX_RETRIES"); retries != "" {
		if val, err := strconv.Atoi(retries); err == nil {
			config.MaxRetries = val
		}
	}

	// Groq configuration
	if err := loadGroqConfig(config); err != nil {
		return fmt.Errorf("failed to load Groq config: %w", err)
	}

	// OpenAI configuration
	if err := loadOpenAIConfig(config); err != nil {
		return fmt.Errorf("failed to load OpenAI config: %w", err)
	}

	// Hugging Face configuration
	if err := loadHuggingFaceConfig(config); err != nil {
		return fmt.Errorf("failed to load Hugging Face config: %w", err)
	}

	// Gemini configuration
	if err := loadGeminiConfig(config); err != nil {
		return fmt.Errorf("failed to load Gemini config: %w", err)
	}

	// Cerebras configuration
	if err := loadCerebrasConfig(config); err != nil {
		return fmt.Errorf("failed to load Cerebras config: %w", err)
	}

	return nil
}

// loadGroqConfig loads Groq-specific configuration
func loadGroqConfig(config *types.AIProviderConfig) error {
	groqConfig := &types.GroqProviderConfig{
		Model:       "llama-3.3-70b-versatile",
		Temperature: 0.7,
		MaxTokens:   4000,
		BaseURL:     "https://api.groq.com/openai/v1/chat/completions",
	}

	// API Key (required)
	if apiKey := os.Getenv("GROQ_API_KEY"); apiKey != "" {
		groqConfig.APIKey = apiKey
	}

	// Model
	if model := os.Getenv("GROQ_MODEL"); model != "" {
		groqConfig.Model = model
	}

	// Temperature
	if temp := os.Getenv("GROQ_TEMPERATURE"); temp != "" {
		if val, err := strconv.ParseFloat(temp, 64); err == nil {
			groqConfig.Temperature = val
		}
	}

	// Max tokens
	if tokens := os.Getenv("GROQ_MAX_TOKENS"); tokens != "" {
		if val, err := strconv.Atoi(tokens); err == nil {
			groqConfig.MaxTokens = val
		}
	}

	// Base URL
	if baseURL := os.Getenv("GROQ_BASE_URL"); baseURL != "" {
		groqConfig.BaseURL = baseURL
	}

	config.GroqConfig = groqConfig
	return nil
}

// loadOpenAIConfig loads OpenAI-specific configuration
func loadOpenAIConfig(config *types.AIProviderConfig) error {
	openaiConfig := &types.OpenAIProviderConfig{
		Model:       "gpt-4",
		Temperature: 0.7,
		MaxTokens:   4000,
		BaseURL:     "https://api.openai.com/v1/chat/completions",
	}

	// API Key (required)
	if apiKey := os.Getenv("OPENAI_API_KEY"); apiKey != "" {
		openaiConfig.APIKey = apiKey
	}

	// Model
	if model := os.Getenv("OPENAI_MODEL"); model != "" {
		openaiConfig.Model = model
	}

	// Temperature
	if temp := os.Getenv("OPENAI_TEMPERATURE"); temp != "" {
		if val, err := strconv.ParseFloat(temp, 64); err == nil {
			openaiConfig.Temperature = val
		}
	}

	// Max tokens
	if tokens := os.Getenv("OPENAI_MAX_TOKENS"); tokens != "" {
		if val, err := strconv.Atoi(tokens); err == nil {
			openaiConfig.MaxTokens = val
		}
	}

	// Base URL
	if baseURL := os.Getenv("OPENAI_BASE_URL"); baseURL != "" {
		openaiConfig.BaseURL = baseURL
	}

	// Organization ID
	if orgID := os.Getenv("OPENAI_ORG_ID"); orgID != "" {
		openaiConfig.OrgID = orgID
	}

	config.OpenAIConfig = openaiConfig
	return nil
}

// loadFeatureConfig loads feature configuration from environment
func loadFeatureConfig(config *FeatureConfig) {
	if val := os.Getenv("AI_ENABLE_GRADE_ADAPTATION"); val != "" {
		config.EnableGradeAdaptation = strings.ToLower(val) == "true"
	}

	if val := os.Getenv("AI_ENABLE_BOARD_SUPPORT"); val != "" {
		config.EnableBoardSupport = strings.ToLower(val) == "true"
	}

	if val := os.Getenv("AI_ENABLE_FALLBACK"); val != "" {
		config.EnableFallback = strings.ToLower(val) == "true"
	}

	if val := os.Getenv("AI_ENABLE_VALIDATION"); val != "" {
		config.EnableValidation = strings.ToLower(val) == "true"
	}

	if val := os.Getenv("AI_ENABLE_LOGGING"); val != "" {
		config.EnableLogging = strings.ToLower(val) == "true"
	}
}

// loadLimitConfig loads limit configuration from environment
func loadLimitConfig(config *LimitConfig) {
	if val := os.Getenv("AI_MAX_QUESTIONS_PER_REQUEST"); val != "" {
		if num, err := strconv.Atoi(val); err == nil {
			config.MaxQuestionsPerRequest = num
		}
	}

	if val := os.Getenv("AI_MAX_REQUESTS_PER_MINUTE"); val != "" {
		if num, err := strconv.Atoi(val); err == nil {
			config.MaxRequestsPerMinute = num
		}
	}

	if val := os.Getenv("AI_MAX_TOKENS_PER_REQUEST"); val != "" {
		if num, err := strconv.Atoi(val); err == nil {
			config.MaxTokensPerRequest = num
		}
	}

	if val := os.Getenv("AI_TIMEOUT_SECONDS"); val != "" {
		if num, err := strconv.Atoi(val); err == nil {
			config.TimeoutSeconds = num
		}
	}
}

// loadHuggingFaceConfig loads Hugging Face-specific configuration
func loadHuggingFaceConfig(config *types.AIProviderConfig) error {
	hfConfig := &types.HuggingFaceProviderConfig{
		Model:          "microsoft/DialoGPT-large",
		Temperature:    0.7,
		MaxTokens:      2000,
		BaseURL:        "https://api-inference.huggingface.co/models",
		TimeoutSeconds: 60,
	}

	// Load from environment variables
	if apiKey := os.Getenv("HUGGINGFACE_API_KEY"); apiKey != "" {
		hfConfig.APIKey = apiKey
	}

	if model := os.Getenv("HUGGINGFACE_MODEL"); model != "" {
		hfConfig.Model = model
	}

	if temp := os.Getenv("HUGGINGFACE_TEMPERATURE"); temp != "" {
		if val, err := strconv.ParseFloat(temp, 64); err == nil {
			hfConfig.Temperature = val
		}
	}

	if maxTokens := os.Getenv("HUGGINGFACE_MAX_TOKENS"); maxTokens != "" {
		if val, err := strconv.Atoi(maxTokens); err == nil {
			hfConfig.MaxTokens = val
		}
	}

	if baseURL := os.Getenv("HUGGINGFACE_BASE_URL"); baseURL != "" {
		hfConfig.BaseURL = baseURL
	}

	config.HuggingFaceConfig = hfConfig
	return nil
}

// loadGeminiConfig loads Google Gemini-specific configuration
func loadGeminiConfig(config *types.AIProviderConfig) error {
	geminiConfig := &types.GeminiProviderConfig{
		Model:          "gemini-1.5-flash",
		Temperature:    0.7,
		MaxTokens:      2000,
		BaseURL:        "https://generativelanguage.googleapis.com/v1beta/models",
		TimeoutSeconds: 60,
	}

	// Load from environment variables
	if apiKey := os.Getenv("GEMINI_API_KEY"); apiKey != "" {
		geminiConfig.APIKey = apiKey
	}

	if model := os.Getenv("GEMINI_MODEL"); model != "" {
		geminiConfig.Model = model
	}

	if temp := os.Getenv("GEMINI_TEMPERATURE"); temp != "" {
		if val, err := strconv.ParseFloat(temp, 64); err == nil {
			geminiConfig.Temperature = val
		}
	}

	if maxTokens := os.Getenv("GEMINI_MAX_TOKENS"); maxTokens != "" {
		if val, err := strconv.Atoi(maxTokens); err == nil {
			geminiConfig.MaxTokens = val
		}
	}

	if baseURL := os.Getenv("GEMINI_BASE_URL"); baseURL != "" {
		geminiConfig.BaseURL = baseURL
	}

	config.GeminiConfig = geminiConfig
	return nil
}

// loadCerebrasConfig loads Cerebras-specific configuration
func loadCerebrasConfig(config *types.AIProviderConfig) error {
	cerebrasConfig := &types.CerebrasProviderConfig{
		Model:          "llama3.1-8b",
		Temperature:    0.7,
		MaxTokens:      4000,
		BaseURL:        "https://api.cerebras.ai/v1/chat/completions",
		TimeoutSeconds: 60,
	}

	// Load from environment variables
	if apiKey := os.Getenv("CEREBRAS_API_KEY"); apiKey != "" {
		cerebrasConfig.APIKey = apiKey
	}

	if model := os.Getenv("CEREBRAS_MODEL"); model != "" {
		cerebrasConfig.Model = model
	}

	if temp := os.Getenv("CEREBRAS_TEMPERATURE"); temp != "" {
		if val, err := strconv.ParseFloat(temp, 64); err == nil {
			cerebrasConfig.Temperature = val
		}
	}

	if maxTokens := os.Getenv("CEREBRAS_MAX_TOKENS"); maxTokens != "" {
		if val, err := strconv.Atoi(maxTokens); err == nil {
			cerebrasConfig.MaxTokens = val
		}
	}

	if baseURL := os.Getenv("CEREBRAS_BASE_URL"); baseURL != "" {
		cerebrasConfig.BaseURL = baseURL
	}

	if timeout := os.Getenv("CEREBRAS_TIMEOUT_SECONDS"); timeout != "" {
		if val, err := strconv.Atoi(timeout); err == nil {
			cerebrasConfig.TimeoutSeconds = val
		}
	}

	config.CerebrasConfig = cerebrasConfig
	return nil
}

// validateAIConfig validates the AI configuration
func validateAIConfig(config *AIConfig) error {
	// Validate provider configuration
	if err := types.ValidateConfig(&config.Provider); err != nil {
		return fmt.Errorf("provider config validation failed: %w", err)
	}

	// Validate limits
	if config.Limits.MaxQuestionsPerRequest <= 0 {
		return fmt.Errorf("max questions per request must be positive")
	}

	if config.Limits.MaxRequestsPerMinute <= 0 {
		return fmt.Errorf("max requests per minute must be positive")
	}

	if config.Limits.TimeoutSeconds <= 0 {
		return fmt.Errorf("timeout seconds must be positive")
	}

	// Validate that at least one provider is configured
	primaryProvider := config.Provider.PrimaryProvider
	if primaryProvider == "groq" && (config.Provider.GroqConfig == nil || config.Provider.GroqConfig.APIKey == "") {
		return fmt.Errorf("Groq API key is required when using Groq as primary provider")
	}

	if primaryProvider == "openai" && (config.Provider.OpenAIConfig == nil || config.Provider.OpenAIConfig.APIKey == "") {
		return fmt.Errorf("OpenAI API key is required when using OpenAI as primary provider")
	}

	return nil
}

// ToJSON converts the configuration to JSON string
func (c *AIConfig) ToJSON() (string, error) {
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// GetProviderConfig returns the provider configuration
func (c *AIConfig) GetProviderConfig() *types.AIProviderConfig {
	return &c.Provider
}
