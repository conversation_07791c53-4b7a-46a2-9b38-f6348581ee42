package database

import (
	"context"
	"fmt"
	"time"

	"test-spark-backend/internal/models"

	"github.com/google/uuid"
)

// AdaptiveLearningRepository defines methods for adaptive learning data operations
type AdaptiveLearningRepository interface {
	// Adaptive Learning Engine
	GetAdaptiveLearningEngine(ctx context.Context, userID uuid.UUID) (*models.AdaptiveLearningEngine, error)
	UpdateAdaptiveLearningEngine(ctx context.Context, engine *models.AdaptiveLearningEngine) error
	CreateAdaptiveLearningEngine(ctx context.Context, engine *models.AdaptiveLearningEngine) error

	// Mastery Levels
	GetMasteryLevel(ctx context.Context, userID uuid.UUID, topicID int) (*models.MasteryLevel, error)
	UpdateMasteryLevel(ctx context.Context, userID uuid.UUID, mastery *models.MasteryLevel) error
	GetMasteryLevelsByUser(ctx context.Context, userID uuid.UUID) ([]models.MasteryLevel, error)
	GetMasteryLevelsBySubject(ctx context.Context, userID uuid.UUID, subjectID int) ([]models.MasteryLevel, error)

	// Learning Paths
	CreateLearningPath(ctx context.Context, path *models.LearningPath) error
	GetLearningPath(ctx context.Context, pathID uuid.UUID) (*models.LearningPath, error)
	GetLearningPathsByUser(ctx context.Context, userID uuid.UUID) ([]models.LearningPath, error)
	UpdateLearningPath(ctx context.Context, path *models.LearningPath) error
	DeleteLearningPath(ctx context.Context, pathID uuid.UUID) error

	// Adaptive Responses
	CreateAdaptiveResponse(ctx context.Context, response *models.AdaptiveResponse) error
	GetUserResponsesByTopic(ctx context.Context, userID uuid.UUID, topicID int, days int) ([]models.AdaptiveResponse, error)
	GetUserResponsesByTimeRange(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) ([]models.AdaptiveResponse, error)
	GetRecentResponses(ctx context.Context, userID uuid.UUID, limit int) ([]models.AdaptiveResponse, error)

	// Spaced Repetition
	CreateSpacedRepetitionSchedule(ctx context.Context, schedule *models.SpacedRepetitionSchedule) error
	GetSpacedRepetitionSchedule(ctx context.Context, userID uuid.UUID, topicID int) (*models.SpacedRepetitionSchedule, error)
	UpdateSpacedRepetitionSchedule(ctx context.Context, schedule *models.SpacedRepetitionSchedule) error
	GetDueReviews(ctx context.Context, userID uuid.UUID) ([]models.SpacedRepetitionSchedule, error)

	// IRT Parameters
	GetQuestionIRTParameters(ctx context.Context, questionID uuid.UUID) (*models.IRTParameters, error)
	UpdateQuestionIRTParameters(ctx context.Context, questionID uuid.UUID, params *models.IRTParameters) error
	GetUserAbilityEstimate(ctx context.Context, userID uuid.UUID, subjectID int) (float64, float64, error)
	UpdateUserAbilityEstimate(ctx context.Context, userID uuid.UUID, subjectID int, ability, confidence float64) error
}

// The adaptive learning methods are implemented directly on SQLStore

// GetAdaptiveLearningEngine retrieves the adaptive learning engine for a user
func (s *SQLStore) GetAdaptiveLearningEngine(ctx context.Context, userID uuid.UUID) (*models.AdaptiveLearningEngine, error) {
	query := `
		SELECT user_id, current_ability, learning_velocity, last_updated
		FROM testspark_db.adaptive_learning_engines 
		WHERE user_id = $1`

	var engine models.AdaptiveLearningEngine
	err := s.db.QueryRow(ctx, query, userID).Scan(
		&engine.UserID,
		&engine.CurrentAbility,
		&engine.LearningVelocity,
		&engine.LastUpdated,
	)

	if err != nil {
		return nil, err
	}

	// Get ability history
	historyQuery := `
		SELECT timestamp, ability, confidence, context
		FROM testspark_db.ability_measurements
		WHERE user_id = $1
		ORDER BY timestamp DESC
		LIMIT 100`

	rows, err := s.db.Query(ctx, historyQuery, userID)
	if err != nil {
		return &engine, nil // Return engine without history if query fails
	}
	defer rows.Close()

	var history []models.AbilityMeasurement
	for rows.Next() {
		var measurement models.AbilityMeasurement
		err := rows.Scan(
			&measurement.Timestamp,
			&measurement.Ability,
			&measurement.Confidence,
			&measurement.Context,
		)
		if err != nil {
			continue
		}
		history = append(history, measurement)
	}

	engine.AbilityHistory = history
	return &engine, nil
}

// CreateAdaptiveLearningEngine creates a new adaptive learning engine
func (s *SQLStore) CreateAdaptiveLearningEngine(ctx context.Context, engine *models.AdaptiveLearningEngine) error {
	query := `
		INSERT INTO testspark_db.adaptive_learning_engines
		(user_id, current_ability, learning_velocity, last_updated)
		VALUES ($1, $2, $3, $4)`

	_, err := s.db.Exec(ctx, query,
		engine.UserID,
		engine.CurrentAbility,
		engine.LearningVelocity,
		engine.LastUpdated,
	)

	return err
}

// UpdateAdaptiveLearningEngine updates an existing adaptive learning engine
func (s *SQLStore) UpdateAdaptiveLearningEngine(ctx context.Context, engine *models.AdaptiveLearningEngine) error {
	query := `
		UPDATE testspark_db.adaptive_learning_engines
		SET current_ability = $2, learning_velocity = $3, last_updated = $4
		WHERE user_id = $1`

	_, err := s.db.Exec(ctx, query,
		engine.UserID,
		engine.CurrentAbility,
		engine.LearningVelocity,
		engine.LastUpdated,
	)

	return err
}

// GetMasteryLevel retrieves mastery level for a specific topic
func (s *SQLStore) GetMasteryLevel(ctx context.Context, userID uuid.UUID, topicID int) (*models.MasteryLevel, error) {
	query := `
		SELECT topic_id, mastery_score, confidence_level, last_practiced, 
		       practice_count, consecutive_correct, needs_review, next_review_date,
		       min_difficulty, max_difficulty, optimal_difficulty
		FROM testspark_db.mastery_levels 
		WHERE user_id = $1 AND topic_id = $2`

	var mastery models.MasteryLevel
	err := s.db.QueryRow(ctx, query, userID, topicID).Scan(
		&mastery.TopicID,
		&mastery.MasteryScore,
		&mastery.ConfidenceLevel,
		&mastery.LastPracticed,
		&mastery.PracticeCount,
		&mastery.ConsecutiveCorrect,
		&mastery.NeedsReview,
		&mastery.NextReviewDate,
		&mastery.DifficultyRange.MinDifficulty,
		&mastery.DifficultyRange.MaxDifficulty,
		&mastery.DifficultyRange.OptimalDifficulty,
	)

	if err != nil {
		return nil, err
	}

	return &mastery, nil
}

// UpdateMasteryLevel updates mastery level for a topic
func (s *SQLStore) UpdateMasteryLevel(ctx context.Context, userID uuid.UUID, mastery *models.MasteryLevel) error {
	query := `
		INSERT INTO testspark_db.mastery_levels 
		(user_id, topic_id, mastery_score, confidence_level, last_practiced, 
		 practice_count, consecutive_correct, needs_review, next_review_date,
		 min_difficulty, max_difficulty, optimal_difficulty)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
		ON CONFLICT (user_id, topic_id) 
		DO UPDATE SET 
			mastery_score = EXCLUDED.mastery_score,
			confidence_level = EXCLUDED.confidence_level,
			last_practiced = EXCLUDED.last_practiced,
			practice_count = EXCLUDED.practice_count,
			consecutive_correct = EXCLUDED.consecutive_correct,
			needs_review = EXCLUDED.needs_review,
			next_review_date = EXCLUDED.next_review_date,
			min_difficulty = EXCLUDED.min_difficulty,
			max_difficulty = EXCLUDED.max_difficulty,
			optimal_difficulty = EXCLUDED.optimal_difficulty`

	_, err := s.db.Exec(ctx, query,
		userID,
		mastery.TopicID,
		mastery.MasteryScore,
		mastery.ConfidenceLevel,
		mastery.LastPracticed,
		mastery.PracticeCount,
		mastery.ConsecutiveCorrect,
		mastery.NeedsReview,
		mastery.NextReviewDate,
		mastery.DifficultyRange.MinDifficulty,
		mastery.DifficultyRange.MaxDifficulty,
		mastery.DifficultyRange.OptimalDifficulty,
	)

	return err
}

// GetMasteryLevelsByUser retrieves all mastery levels for a user
func (s *SQLStore) GetMasteryLevelsByUser(ctx context.Context, userID uuid.UUID) ([]models.MasteryLevel, error) {
	query := `
		SELECT topic_id, mastery_score, confidence_level, last_practiced, 
		       practice_count, consecutive_correct, needs_review, next_review_date,
		       min_difficulty, max_difficulty, optimal_difficulty
		FROM testspark_db.mastery_levels 
		WHERE user_id = $1
		ORDER BY mastery_score ASC`

	rows, err := s.db.Query(ctx, query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var masteryLevels []models.MasteryLevel
	for rows.Next() {
		var mastery models.MasteryLevel
		err := rows.Scan(
			&mastery.TopicID,
			&mastery.MasteryScore,
			&mastery.ConfidenceLevel,
			&mastery.LastPracticed,
			&mastery.PracticeCount,
			&mastery.ConsecutiveCorrect,
			&mastery.NeedsReview,
			&mastery.NextReviewDate,
			&mastery.DifficultyRange.MinDifficulty,
			&mastery.DifficultyRange.MaxDifficulty,
			&mastery.DifficultyRange.OptimalDifficulty,
		)
		if err != nil {
			continue
		}
		masteryLevels = append(masteryLevels, mastery)
	}

	return masteryLevels, nil
}

// GetMasteryLevelsBySubject retrieves mastery levels for a specific subject
func (s *SQLStore) GetMasteryLevelsBySubject(ctx context.Context, userID uuid.UUID, subjectID int) ([]models.MasteryLevel, error) {
	query := `
		SELECT ml.topic_id, ml.mastery_score, ml.confidence_level, ml.last_practiced, 
		       ml.practice_count, ml.consecutive_correct, ml.needs_review, ml.next_review_date,
		       ml.min_difficulty, ml.max_difficulty, ml.optimal_difficulty
		FROM testspark_db.mastery_levels ml
		JOIN testspark_db.topics t ON ml.topic_id = t.id
		WHERE ml.user_id = $1 AND t.subject_id = $2
		ORDER BY ml.mastery_score ASC`

	rows, err := s.db.Query(ctx, query, userID, subjectID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var masteryLevels []models.MasteryLevel
	for rows.Next() {
		var mastery models.MasteryLevel
		err := rows.Scan(
			&mastery.TopicID,
			&mastery.MasteryScore,
			&mastery.ConfidenceLevel,
			&mastery.LastPracticed,
			&mastery.PracticeCount,
			&mastery.ConsecutiveCorrect,
			&mastery.NeedsReview,
			&mastery.NextReviewDate,
			&mastery.DifficultyRange.MinDifficulty,
			&mastery.DifficultyRange.MaxDifficulty,
			&mastery.DifficultyRange.OptimalDifficulty,
		)
		if err != nil {
			continue
		}
		masteryLevels = append(masteryLevels, mastery)
	}

	return masteryLevels, nil
}

// CreateAdaptiveResponse creates a new adaptive response record
func (s *SQLStore) CreateAdaptiveResponse(ctx context.Context, response *models.AdaptiveResponse) error {
	query := `
		INSERT INTO testspark_db.adaptive_responses
		(question_id, user_id, is_correct, response_time, difficulty, discrimination,
		 guessing, ability_estimate, confidence, timestamp)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`

	_, err := s.db.Exec(ctx, query,
		response.QuestionID,
		response.UserID,
		response.IsCorrect,
		response.ResponseTime,
		response.IRTParameters.Difficulty,
		response.IRTParameters.Discrimination,
		response.IRTParameters.Guessing,
		response.AbilityEstimate,
		response.Confidence,
		response.Timestamp,
	)

	return err
}

// GetUserResponsesByTopic retrieves user responses for a specific topic within a time range
func (s *SQLStore) GetUserResponsesByTopic(ctx context.Context, userID uuid.UUID, topicID int, days int) ([]models.AdaptiveResponse, error) {
	query := fmt.Sprintf(`
		SELECT ar.question_id, ar.user_id, ar.is_correct, ar.response_time,
		       ar.difficulty, ar.discrimination, ar.guessing, ar.ability_estimate,
		       ar.confidence, ar.timestamp
		FROM testspark_db.adaptive_responses ar
		JOIN testspark_db.questions q ON ar.question_id = q.id
		WHERE ar.user_id = $1 AND q.topic_id = $2
		  AND ar.timestamp >= NOW() - INTERVAL '%d days'
		ORDER BY ar.timestamp DESC`, days)

	rows, err := s.db.Query(ctx, query, userID, topicID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var responses []models.AdaptiveResponse
	for rows.Next() {
		var response models.AdaptiveResponse
		err := rows.Scan(
			&response.QuestionID,
			&response.UserID,
			&response.IsCorrect,
			&response.ResponseTime,
			&response.IRTParameters.Difficulty,
			&response.IRTParameters.Discrimination,
			&response.IRTParameters.Guessing,
			&response.AbilityEstimate,
			&response.Confidence,
			&response.Timestamp,
		)
		if err != nil {
			continue
		}
		responses = append(responses, response)
	}

	return responses, nil
}

// GetUserResponsesByTimeRange retrieves user responses within a specific time range
func (s *SQLStore) GetUserResponsesByTimeRange(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) ([]models.AdaptiveResponse, error) {
	query := `
		SELECT question_id, user_id, is_correct, response_time,
		       difficulty, discrimination, guessing, ability_estimate,
		       confidence, timestamp
		FROM testspark_db.adaptive_responses
		WHERE user_id = $1 AND timestamp BETWEEN $2 AND $3
		ORDER BY timestamp DESC`

	rows, err := s.db.Query(ctx, query, userID, startTime, endTime)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var responses []models.AdaptiveResponse
	for rows.Next() {
		var response models.AdaptiveResponse
		err := rows.Scan(
			&response.QuestionID,
			&response.UserID,
			&response.IsCorrect,
			&response.ResponseTime,
			&response.IRTParameters.Difficulty,
			&response.IRTParameters.Discrimination,
			&response.IRTParameters.Guessing,
			&response.AbilityEstimate,
			&response.Confidence,
			&response.Timestamp,
		)
		if err != nil {
			continue
		}
		responses = append(responses, response)
	}

	return responses, nil
}

// GetRecentResponses retrieves the most recent responses for a user
func (s *SQLStore) GetRecentResponses(ctx context.Context, userID uuid.UUID, limit int) ([]models.AdaptiveResponse, error) {
	query := `
		SELECT question_id, user_id, is_correct, response_time,
		       difficulty, discrimination, guessing, ability_estimate,
		       confidence, timestamp
		FROM testspark_db.adaptive_responses
		WHERE user_id = $1
		ORDER BY timestamp DESC
		LIMIT $2`

	rows, err := s.db.Query(ctx, query, userID, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var responses []models.AdaptiveResponse
	for rows.Next() {
		var response models.AdaptiveResponse
		err := rows.Scan(
			&response.QuestionID,
			&response.UserID,
			&response.IsCorrect,
			&response.ResponseTime,
			&response.IRTParameters.Difficulty,
			&response.IRTParameters.Discrimination,
			&response.IRTParameters.Guessing,
			&response.AbilityEstimate,
			&response.Confidence,
			&response.Timestamp,
		)
		if err != nil {
			continue
		}
		responses = append(responses, response)
	}

	return responses, nil
}

// CreateLearningPath creates a new learning path
func (s *SQLStore) CreateLearningPath(ctx context.Context, path *models.LearningPath) error {
	// Implementation placeholder - would create learning path in database
	return nil
}

// GetLearningPath retrieves a learning path by ID
func (s *SQLStore) GetLearningPath(ctx context.Context, pathID uuid.UUID) (*models.LearningPath, error) {
	// Implementation placeholder
	return nil, nil
}

// GetLearningPathsByUser retrieves all learning paths for a user
func (s *SQLStore) GetLearningPathsByUser(ctx context.Context, userID uuid.UUID) ([]models.LearningPath, error) {
	// Implementation placeholder
	return nil, nil
}

// UpdateLearningPath updates an existing learning path
func (s *SQLStore) UpdateLearningPath(ctx context.Context, path *models.LearningPath) error {
	// Implementation placeholder
	return nil
}

// DeleteLearningPath deletes a learning path
func (s *SQLStore) DeleteLearningPath(ctx context.Context, pathID uuid.UUID) error {
	// Implementation placeholder
	return nil
}

// CreateSpacedRepetitionSchedule creates a new spaced repetition schedule
func (s *SQLStore) CreateSpacedRepetitionSchedule(ctx context.Context, schedule *models.SpacedRepetitionSchedule) error {
	// Implementation placeholder
	return nil
}

// GetSpacedRepetitionSchedule retrieves a spaced repetition schedule
func (s *SQLStore) GetSpacedRepetitionSchedule(ctx context.Context, userID uuid.UUID, topicID int) (*models.SpacedRepetitionSchedule, error) {
	// Implementation placeholder
	return nil, nil
}

// UpdateSpacedRepetitionSchedule updates a spaced repetition schedule
func (s *SQLStore) UpdateSpacedRepetitionSchedule(ctx context.Context, schedule *models.SpacedRepetitionSchedule) error {
	// Implementation placeholder
	return nil
}

// GetDueReviews retrieves due reviews for a user
func (s *SQLStore) GetDueReviews(ctx context.Context, userID uuid.UUID) ([]models.SpacedRepetitionSchedule, error) {
	// Implementation placeholder
	return nil, nil
}

// GetQuestionIRTParameters retrieves IRT parameters for a question
func (s *SQLStore) GetQuestionIRTParameters(ctx context.Context, questionID uuid.UUID) (*models.IRTParameters, error) {
	// Implementation placeholder
	return nil, nil
}

// UpdateQuestionIRTParameters updates IRT parameters for a question
func (s *SQLStore) UpdateQuestionIRTParameters(ctx context.Context, questionID uuid.UUID, params *models.IRTParameters) error {
	// Implementation placeholder
	return nil
}

// GetUserAbilityEstimate retrieves user ability estimate for a subject
func (s *SQLStore) GetUserAbilityEstimate(ctx context.Context, userID uuid.UUID, subjectID int) (float64, float64, error) {
	// Implementation placeholder
	return 0.0, 1.0, nil
}

// UpdateUserAbilityEstimate updates user ability estimate for a subject
func (s *SQLStore) UpdateUserAbilityEstimate(ctx context.Context, userID uuid.UUID, subjectID int, ability, confidence float64) error {
	// Implementation placeholder
	return nil
}
