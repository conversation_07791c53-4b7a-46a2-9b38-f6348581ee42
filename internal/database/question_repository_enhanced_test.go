package database

import (
	"context"
	"testing"
	"time"

	"test-spark-backend/internal/models"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

// TestGetSmartRandomQuestions tests the enhanced question selection logic
func TestGetSmartRandomQuestions(t *testing.T) {
	// This test would require a test database setup
	// For now, we'll test the logic structure
	t.Skip("Integration test - requires test database")

	store := &SQLStore{} // Would need actual DB connection
	ctx := context.Background()
	userID := uuid.New().String()
	topicIDs := []int{1, 2}
	difficulties := []models.DifficultyLevel{models.DifficultyMedium}
	limit := 5
	avoidRecentDays := 7

	questions, err := store.GetSmartRandomQuestions(ctx, userID, topicIDs, difficulties, limit, avoidRecentDays)

	assert.NoError(t, err)
	assert.LessOrEqual(t, len(questions), limit)
}

// TestGetDiverseQuestions tests the diverse question selection logic
func TestGetDiverseQuestions(t *testing.T) {
	t.Skip("Integration test - requires test database")

	store := &SQLStore{}
	ctx := context.Background()
	userID := uuid.New().String()
	topicIDs := []int{1, 2, 3}
	difficulties := []models.DifficultyLevel{models.DifficultyEasy, models.DifficultyMedium}
	limit := 10

	questions, err := store.GetDiverseQuestions(ctx, userID, topicIDs, difficulties, limit)

	assert.NoError(t, err)
	assert.LessOrEqual(t, len(questions), limit)

	// Verify diversity - questions should span different topics and difficulties
	topicMap := make(map[int]bool)
	difficultyMap := make(map[models.DifficultyLevel]bool)

	for _, question := range questions {
		topicMap[question.TopicID] = true
		if question.Difficulty != nil {
			difficultyMap[*question.Difficulty] = true
		}
	}

	// Should have questions from multiple topics (if available)
	assert.GreaterOrEqual(t, len(topicMap), 1)
}

// TestRecordQuestionSeen tests the question history tracking
func TestRecordQuestionSeen(t *testing.T) {
	t.Skip("Integration test - requires test database")

	store := &SQLStore{}
	ctx := context.Background()
	userID := uuid.New()
	questionID := uuid.New()

	// Test first time seeing a question
	err := store.RecordQuestionSeen(ctx, userID, questionID, nil, nil)
	assert.NoError(t, err)

	// Test seeing the same question again with response time
	responseTime := 5000 // 5 seconds
	isCorrect := true
	err = store.RecordQuestionSeen(ctx, userID, questionID, &responseTime, &isCorrect)
	assert.NoError(t, err)

	// Verify the history was recorded
	history, err := store.GetUserQuestionHistory(ctx, userID, 10)
	assert.NoError(t, err)
	assert.Len(t, history, 1)

	record := history[0]
	assert.Equal(t, userID, record.UserID)
	assert.Equal(t, questionID, record.QuestionID)
	assert.Equal(t, 2, record.TimesSeen)
	assert.Equal(t, 1, record.TimesAnswered)
	assert.Equal(t, 1, record.TimesCorrect)
	assert.Equal(t, &responseTime, record.LastResponseTimeMs)
}

// TestGetQuestionPoolSize tests the pool size calculation
func TestGetQuestionPoolSize(t *testing.T) {
	t.Skip("Integration test - requires test database")

	store := &SQLStore{}
	ctx := context.Background()
	topicIDs := []int{1, 2}
	difficulties := []models.DifficultyLevel{models.DifficultyMedium}

	size, err := store.GetQuestionPoolSize(ctx, topicIDs, difficulties)
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, size, 0)
}

// TestGetUserSeenQuestionIDs tests retrieval of recently seen questions
func TestGetUserSeenQuestionIDs(t *testing.T) {
	t.Skip("Integration test - requires test database")

	store := &SQLStore{}
	ctx := context.Background()
	userID := uuid.New()
	topicIDs := []int{1, 2}
	difficulties := []models.DifficultyLevel{models.DifficultyMedium}
	daysSince := 7

	questionIDs, err := store.GetUserSeenQuestionIDs(ctx, userID, topicIDs, difficulties, daysSince)
	assert.NoError(t, err)
	assert.IsType(t, []uuid.UUID{}, questionIDs)
}

// TestQuestionGenerationSession tests session tracking
func TestQuestionGenerationSession(t *testing.T) {
	t.Skip("Integration test - requires test database")

	store := &SQLStore{}
	ctx := context.Background()

	session := &QuestionGenerationSession{
		ID:                   uuid.New(),
		UserID:               func() *uuid.UUID { u := uuid.New(); return &u }(),
		SessionContext:       []byte(`{"topics":[1,2],"difficulty":"medium"}`),
		QuestionsGenerated:   5,
		AIProvider:           func() *string { s := "groq"; return &s }(),
		GenerationPromptHash: func() *string { s := "hash123"; return &s }(),
		CreatedAt:            time.Now(),
	}

	err := store.CreateQuestionGenerationSession(ctx, session)
	assert.NoError(t, err)

	// Test linking questions to session
	questionID := uuid.New()
	err = store.LinkQuestionToSession(ctx, questionID, session.ID, 1)
	assert.NoError(t, err)
}

// TestGeneratePromptHash tests the prompt hashing function
func TestGeneratePromptHash(t *testing.T) {
	prompt1 := "Generate 5 questions about mathematics"
	prompt2 := "Generate 5 questions about science"
	prompt3 := "Generate 5 questions about mathematics" // Same as prompt1

	hash1 := GeneratePromptHash(prompt1)
	hash2 := GeneratePromptHash(prompt2)
	hash3 := GeneratePromptHash(prompt3)

	// Different prompts should have different hashes
	assert.NotEqual(t, hash1, hash2)

	// Same prompts should have same hashes
	assert.Equal(t, hash1, hash3)

	// Hashes should be consistent length (SHA256 = 64 hex characters)
	assert.Len(t, hash1, 64)
	assert.Len(t, hash2, 64)
}

// BenchmarkGetSmartRandomQuestions benchmarks the smart selection performance
func BenchmarkGetSmartRandomQuestions(b *testing.B) {
	b.Skip("Integration benchmark - requires test database")

	store := &SQLStore{}
	ctx := context.Background()
	userID := uuid.New().String()
	topicIDs := []int{1, 2, 3}
	difficulties := []models.DifficultyLevel{models.DifficultyMedium}
	limit := 10
	avoidRecentDays := 7

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := store.GetSmartRandomQuestions(ctx, userID, topicIDs, difficulties, limit, avoidRecentDays)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkGetDiverseQuestions benchmarks the diverse selection performance
func BenchmarkGetDiverseQuestions(b *testing.B) {
	b.Skip("Integration benchmark - requires test database")

	store := &SQLStore{}
	ctx := context.Background()
	userID := uuid.New().String()
	topicIDs := []int{1, 2, 3, 4, 5}
	difficulties := []models.DifficultyLevel{models.DifficultyEasy, models.DifficultyMedium, models.DifficultyHard}
	limit := 15

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := store.GetDiverseQuestions(ctx, userID, topicIDs, difficulties, limit)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// TestQuestionSelectionLogic tests the logic without database dependency
func TestQuestionSelectionLogic(t *testing.T) {
	// Test the logic for determining questions per combination
	topicCount := 3
	difficultyCount := 2
	totalLimit := 12

	questionsPerCombo := totalLimit / (topicCount * difficultyCount)
	expectedPerCombo := 2 // 12 / (3 * 2) = 2

	assert.Equal(t, expectedPerCombo, questionsPerCombo)

	// Test edge case where limit is smaller than combinations
	smallLimit := 3
	questionsPerComboSmall := smallLimit / (topicCount * difficultyCount)
	if questionsPerComboSmall == 0 {
		questionsPerComboSmall = 1
	}

	assert.Equal(t, 1, questionsPerComboSmall)
}

// TestPromptHashConsistency tests that prompt hashing is deterministic
func TestPromptHashConsistency(t *testing.T) {
	prompt := "Generate questions about physics with medium difficulty"

	// Generate hash multiple times
	hash1 := GeneratePromptHash(prompt)
	hash2 := GeneratePromptHash(prompt)
	hash3 := GeneratePromptHash(prompt)

	// All hashes should be identical
	assert.Equal(t, hash1, hash2)
	assert.Equal(t, hash2, hash3)

	// Hash should be non-empty
	assert.NotEmpty(t, hash1)

	// Hash should be hexadecimal
	assert.Regexp(t, "^[a-f0-9]+$", hash1)
}
