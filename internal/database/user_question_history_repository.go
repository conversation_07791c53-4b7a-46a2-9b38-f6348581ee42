package database

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"test-spark-backend/internal/models"

	"github.com/google/uuid"
)

// QuestionGenerationSession represents the question_generation_sessions table
type QuestionGenerationSession struct {
	ID                   uuid.UUID  `json:"id" db:"id"`
	UserID               *uuid.UUID `json:"user_id" db:"user_id"`
	SessionContext       []byte     `json:"session_context" db:"session_context"` // JSONB
	QuestionsGenerated   int        `json:"questions_generated" db:"questions_generated"`
	AIProvider           *string    `json:"ai_provider" db:"ai_provider"`
	GenerationPromptHash *string    `json:"generation_prompt_hash" db:"generation_prompt_hash"`
	CreatedAt            time.Time  `json:"created_at" db:"created_at"`
}

// RecordQuestionSeen records that a user has seen a question
func (s *SQLStore) RecordQuestionSeen(ctx context.Context, userID uuid.UUID, questionID uuid.UUID, responseTimeMs *int, isCorrect *bool) error {
	query := `
		INSERT INTO user_question_history (user_id, question_id, first_seen_at, last_seen_at, times_seen, times_answered, times_correct, last_response_time_ms)
		VALUES ($1, $2, NOW(), NOW(), 1, $3, $4, $5)
		ON CONFLICT (user_id, question_id)
		DO UPDATE SET
			last_seen_at = NOW(),
			times_seen = user_question_history.times_seen + 1,
			times_answered = user_question_history.times_answered + $3,
			times_correct = user_question_history.times_correct + $4,
			last_response_time_ms = $5,
			avg_response_time_ms = CASE 
				WHEN $5 IS NOT NULL THEN 
					CASE 
						WHEN user_question_history.avg_response_time_ms IS NULL THEN $5
						ELSE (user_question_history.avg_response_time_ms * user_question_history.times_answered + $5) / (user_question_history.times_answered + 1)
					END
				ELSE user_question_history.avg_response_time_ms
			END,
			updated_at = NOW()`

	timesAnswered := 0
	timesCorrect := 0
	if responseTimeMs != nil {
		timesAnswered = 1
		if isCorrect != nil && *isCorrect {
			timesCorrect = 1
		}
	}

	_, err := s.db.Exec(ctx, query, userID, questionID, timesAnswered, timesCorrect, responseTimeMs)
	if err != nil {
		return fmt.Errorf("failed to record question seen: %w", err)
	}

	return nil
}

// GetUserQuestionHistory retrieves question history for a user
func (s *SQLStore) GetUserQuestionHistory(ctx context.Context, userID uuid.UUID, limit int) ([]models.UserQuestionHistory, error) {
	query := `
		SELECT id, user_id, question_id, first_seen_at, last_seen_at, times_seen, 
		       times_answered, times_correct, avg_response_time_ms, last_response_time_ms, 
		       created_at, updated_at
		FROM user_question_history
		WHERE user_id = $1
		ORDER BY last_seen_at DESC
		LIMIT $2`

	rows, err := s.db.Query(ctx, query, userID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get user question history: %w", err)
	}
	defer rows.Close()

	var history []models.UserQuestionHistory
	for rows.Next() {
		var h models.UserQuestionHistory
		err := rows.Scan(
			&h.ID, &h.UserID, &h.QuestionID, &h.FirstSeenAt, &h.LastSeenAt,
			&h.TimesSeen, &h.TimesAnswered, &h.TimesCorrect,
			&h.AvgResponseTimeMs, &h.LastResponseTimeMs, &h.CreatedAt, &h.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan question history: %w", err)
		}
		history = append(history, h)
	}

	return history, nil
}

// GetUserSeenQuestionIDs retrieves question IDs that a user has seen recently
func (s *SQLStore) GetUserSeenQuestionIDs(ctx context.Context, userID uuid.UUID, topicIDs []int, difficulties []models.DifficultyLevel, daysSince int) ([]uuid.UUID, error) {
	if len(topicIDs) == 0 || len(difficulties) == 0 {
		return []uuid.UUID{}, nil
	}

	// Build placeholders for topic IDs and difficulties
	topicPlaceholders := make([]string, len(topicIDs))
	difficultyPlaceholders := make([]string, len(difficulties))

	args := make([]interface{}, 0, len(topicIDs)+len(difficulties)+2)
	argIndex := 1

	args = append(args, userID)
	argIndex++

	// Add topic IDs to args and create placeholders
	for i, topicID := range topicIDs {
		topicPlaceholders[i] = fmt.Sprintf("$%d", argIndex)
		args = append(args, topicID)
		argIndex++
	}

	// Add difficulties to args and create placeholders
	for i, difficulty := range difficulties {
		difficultyPlaceholders[i] = fmt.Sprintf("$%d", argIndex)
		args = append(args, difficulty)
		argIndex++
	}

	args = append(args, daysSince)

	query := fmt.Sprintf(`
		SELECT DISTINCT uqh.question_id
		FROM user_question_history uqh
		JOIN questions q ON uqh.question_id = q.id
		WHERE uqh.user_id = $1
		  AND q.topic_id IN (%s)
		  AND q.difficulty IN (%s)
		  AND uqh.last_seen_at > NOW() - INTERVAL '%d days'`,
		strings.Join(topicPlaceholders, ","),
		strings.Join(difficultyPlaceholders, ","),
		daysSince)

	rows, err := s.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get user seen question IDs: %w", err)
	}
	defer rows.Close()

	var questionIDs []uuid.UUID
	for rows.Next() {
		var questionID uuid.UUID
		err := rows.Scan(&questionID)
		if err != nil {
			return nil, fmt.Errorf("failed to scan question ID: %w", err)
		}
		questionIDs = append(questionIDs, questionID)
	}

	return questionIDs, nil
}

// CreateQuestionGenerationSession creates a new question generation session
func (s *SQLStore) CreateQuestionGenerationSession(ctx context.Context, session *QuestionGenerationSession) error {
	query := `
		INSERT INTO question_generation_sessions (id, user_id, session_context, questions_generated, ai_provider, generation_prompt_hash, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)`

	_, err := s.db.Exec(ctx, query,
		session.ID,
		session.UserID,
		session.SessionContext,
		session.QuestionsGenerated,
		session.AIProvider,
		session.GenerationPromptHash,
		session.CreatedAt,
	)
	if err != nil {
		return fmt.Errorf("failed to create question generation session: %w", err)
	}

	return nil
}

// LinkQuestionToSession links a question to its generation session
func (s *SQLStore) LinkQuestionToSession(ctx context.Context, questionID uuid.UUID, sessionID uuid.UUID, generationOrder int) error {
	query := `
		INSERT INTO question_generation_links (question_id, session_id, generation_order)
		VALUES ($1, $2, $3)`

	_, err := s.db.Exec(ctx, query, questionID, sessionID, generationOrder)
	if err != nil {
		return fmt.Errorf("failed to link question to session: %w", err)
	}

	return nil
}

// GeneratePromptHash creates a hash of the generation prompt for deduplication
func GeneratePromptHash(prompt string) string {
	hash := sha256.Sum256([]byte(prompt))
	return hex.EncodeToString(hash[:])
}

// GetQuestionPoolSize returns the number of available questions for given criteria
func (s *SQLStore) GetQuestionPoolSize(ctx context.Context, topicIDs []int, difficulties []models.DifficultyLevel) (int, error) {
	if len(topicIDs) == 0 || len(difficulties) == 0 {
		return 0, nil
	}

	// Build placeholders for topic IDs and difficulties
	topicPlaceholders := make([]string, len(topicIDs))
	difficultyPlaceholders := make([]string, len(difficulties))

	args := make([]interface{}, 0, len(topicIDs)+len(difficulties))
	argIndex := 1

	// Add topic IDs to args and create placeholders
	for i, topicID := range topicIDs {
		topicPlaceholders[i] = fmt.Sprintf("$%d", argIndex)
		args = append(args, topicID)
		argIndex++
	}

	// Add difficulties to args and create placeholders
	for i, difficulty := range difficulties {
		difficultyPlaceholders[i] = fmt.Sprintf("$%d", argIndex)
		args = append(args, difficulty)
		argIndex++
	}

	query := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM questions
		WHERE topic_id IN (%s) AND difficulty IN (%s)`,
		strings.Join(topicPlaceholders, ","),
		strings.Join(difficultyPlaceholders, ","))

	var count int
	err := s.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get question pool size: %w", err)
	}

	return count, nil
}
