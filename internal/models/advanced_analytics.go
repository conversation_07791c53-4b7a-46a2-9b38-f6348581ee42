package models

import (
	"time"

	"github.com/google/uuid"
)

// ComprehensiveAnalytics represents comprehensive learning analytics with predictive insights
type ComprehensiveAnalytics struct {
	UserID              uuid.UUID             `json:"user_id"`
	Period              AnalyticsPeriod       `json:"period"`
	LearningMetrics     LearningMetrics       `json:"learning_metrics"`
	PerformanceAnalysis PerformanceAnalysis   `json:"performance_analysis"`
	PredictiveInsights  PredictiveInsights    `json:"predictive_insights"`
	LearningPatterns    []LearningPattern     `json:"learning_patterns"`
	Recommendations     []Recommendation      `json:"recommendations"`
	CognitiveLoad       CognitiveLoadAnalysis `json:"cognitive_load"`
	MotivationFactors   MotivationAnalysis    `json:"motivation_factors"`
	GeneratedAt         time.Time             `json:"generated_at"`
	NextUpdateAt        time.Time             `json:"next_update_at"`
}

// AnalyticsPeriod defines the time period for analytics
type AnalyticsPeriod struct {
	Type      PeriodType `json:"type"`
	StartDate time.Time  `json:"start_date"`
	EndDate   time.Time  `json:"end_date"`
	Label     string     `json:"label"`
}

// PeriodType defines different analytics periods
type PeriodType string

const (
	PeriodTypeDaily     PeriodType = "daily"
	PeriodTypeWeekly    PeriodType = "weekly"
	PeriodTypeMonthly   PeriodType = "monthly"
	PeriodTypeQuarterly PeriodType = "quarterly"
	PeriodTypeYearly    PeriodType = "yearly"
	PeriodTypeCustom    PeriodType = "custom"
)

// LearningMetrics contains core learning performance metrics
type LearningMetrics struct {
	TotalStudyTime        int                    `json:"total_study_time_minutes"`
	ActiveLearningTime    int                    `json:"active_learning_time_minutes"`
	QuestionsAttempted    int                    `json:"questions_attempted"`
	QuestionsCorrect      int                    `json:"questions_correct"`
	OverallAccuracy       float64                `json:"overall_accuracy"`
	LearningVelocity      float64                `json:"learning_velocity"`
	RetentionRate         float64                `json:"retention_rate"`
	MasteryProgression    float64                `json:"mastery_progression"`
	SubjectBreakdown      map[int]SubjectMetrics `json:"subject_breakdown"`
	DifficultyProgression DifficultyProgression  `json:"difficulty_progression"`
	ConsistencyScore      float64                `json:"consistency_score"`
	EngagementLevel       float64                `json:"engagement_level"`
}

// SubjectMetrics contains metrics for a specific subject
type SubjectMetrics struct {
	SubjectID        int                  `json:"subject_id"`
	SubjectName      string               `json:"subject_name"`
	StudyTime        int                  `json:"study_time_minutes"`
	Accuracy         float64              `json:"accuracy"`
	MasteryLevel     float64              `json:"mastery_level"`
	ProgressRate     float64              `json:"progress_rate"`
	TopicBreakdown   map[int]TopicMetrics `json:"topic_breakdown"`
	StrengthAreas    []string             `json:"strength_areas"`
	WeaknessAreas    []string             `json:"weakness_areas"`
	RecommendedFocus []string             `json:"recommended_focus"`
}

// TopicMetrics contains metrics for a specific topic
type TopicMetrics struct {
	TopicID          int       `json:"topic_id"`
	TopicName        string    `json:"topic_name"`
	MasteryLevel     float64   `json:"mastery_level"`
	Accuracy         float64   `json:"accuracy"`
	TimeSpent        int       `json:"time_spent_minutes"`
	LastPracticed    time.Time `json:"last_practiced"`
	NeedsReview      bool      `json:"needs_review"`
	DifficultyRating float64   `json:"difficulty_rating"`
}

// DifficultyProgression tracks progression through difficulty levels
type DifficultyProgression struct {
	EasyLevel      DifficultyLevelMetrics `json:"easy_level"`
	MediumLevel    DifficultyLevelMetrics `json:"medium_level"`
	HardLevel      DifficultyLevelMetrics `json:"hard_level"`
	OverallTrend   string                 `json:"overall_trend"`
	ReadinessLevel string                 `json:"readiness_level"`
}

// DifficultyLevelMetrics contains metrics for a difficulty level
type DifficultyLevelMetrics struct {
	QuestionsAttempted int     `json:"questions_attempted"`
	Accuracy           float64 `json:"accuracy"`
	AverageTime        float64 `json:"average_time_seconds"`
	ConfidenceLevel    float64 `json:"confidence_level"`
	MasteryAchieved    bool    `json:"mastery_achieved"`
}

// PerformanceAnalysis provides detailed performance insights
type PerformanceAnalysis struct {
	PerformanceTrends    []DetailedPerformanceTrend `json:"performance_trends"`
	LearningCurve        LearningCurve              `json:"learning_curve"`
	ErrorAnalysis        ErrorAnalysis              `json:"error_analysis"`
	TimeAnalysis         TimeAnalysis               `json:"time_analysis"`
	CompetencyMapping    CompetencyMapping          `json:"competency_mapping"`
	PerformanceForecasts []PerformanceForecast      `json:"performance_forecasts"`
}

// DetailedPerformanceTrend represents performance over time with detailed analytics
type DetailedPerformanceTrend struct {
	Metric     string      `json:"metric"`
	Period     string      `json:"period"`
	DataPoints []DataPoint `json:"data_points"`
	Trend      string      `json:"trend"` // "improving", "declining", "stable"
	Confidence float64     `json:"confidence"`
}

// DataPoint represents a single data point in a trend
type DataPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
	Context   string    `json:"context"`
}

// LearningCurve analyzes the learning progression
type LearningCurve struct {
	InitialPerformance  float64              `json:"initial_performance"`
	CurrentPerformance  float64              `json:"current_performance"`
	PeakPerformance     float64              `json:"peak_performance"`
	LearningRate        float64              `json:"learning_rate"`
	PlateauPeriods      []PlateauPeriod      `json:"plateau_periods"`
	BreakthroughMoments []BreakthroughMoment `json:"breakthrough_moments"`
	PredictedTrajectory LearningTrajectory   `json:"predicted_trajectory"`
}

// PlateauPeriod represents a period of stagnant learning
type PlateauPeriod struct {
	StartDate     time.Time `json:"start_date"`
	EndDate       time.Time `json:"end_date"`
	Duration      int       `json:"duration_days"`
	Subject       string    `json:"subject"`
	Causes        []string  `json:"potential_causes"`
	Interventions []string  `json:"suggested_interventions"`
}

// BreakthroughMoment represents significant learning improvements
type BreakthroughMoment struct {
	Timestamp    time.Time `json:"timestamp"`
	Subject      string    `json:"subject"`
	Improvement  float64   `json:"improvement_percentage"`
	TriggerEvent string    `json:"trigger_event"`
	Description  string    `json:"description"`
}

// LearningTrajectory predicts future learning path
type LearningTrajectory struct {
	ShortTerm  TrajectoryPrediction `json:"short_term"`  // 1-2 weeks
	MediumTerm TrajectoryPrediction `json:"medium_term"` // 1-3 months
	LongTerm   TrajectoryPrediction `json:"long_term"`   // 6+ months
}

// TrajectoryPrediction contains prediction details
type TrajectoryPrediction struct {
	TimeFrame            string   `json:"time_frame"`
	PredictedPerformance float64  `json:"predicted_performance"`
	Confidence           float64  `json:"confidence"`
	KeyMilestones        []string `json:"key_milestones"`
	RiskFactors          []string `json:"risk_factors"`
}

// ErrorAnalysis analyzes learning mistakes and patterns
type ErrorAnalysis struct {
	CommonMistakes      []MistakePattern     `json:"common_mistakes"`
	ErrorCategories     map[string]int       `json:"error_categories"`
	MistakeProgression  []MistakeProgression `json:"mistake_progression"`
	ConceptualGaps      []ConceptualGap      `json:"conceptual_gaps"`
	RemedyEffectiveness map[string]float64   `json:"remedy_effectiveness"`
}

// MistakePattern represents a pattern of mistakes
type MistakePattern struct {
	Pattern      string    `json:"pattern"`
	Frequency    int       `json:"frequency"`
	Subjects     []string  `json:"subjects"`
	Severity     string    `json:"severity"`
	Trend        string    `json:"trend"`
	Solutions    []string  `json:"solutions"`
	LastOccurred time.Time `json:"last_occurred"`
}

// MistakeProgression tracks how mistakes change over time
type MistakeProgression struct {
	MistakeType string      `json:"mistake_type"`
	Timeline    []DataPoint `json:"timeline"`
	Improvement float64     `json:"improvement_rate"`
	Status      string      `json:"status"` // "resolved", "improving", "persistent"
}

// ConceptualGap represents missing foundational knowledge
type ConceptualGap struct {
	Concept       string   `json:"concept"`
	Subject       string   `json:"subject"`
	Severity      float64  `json:"severity"`
	Prerequisites []string `json:"prerequisites"`
	Impact        string   `json:"impact"`
	Priority      int      `json:"priority"`
}

// TimeAnalysis analyzes learning time patterns
type TimeAnalysis struct {
	OptimalStudyTimes    []TimeWindow          `json:"optimal_study_times"`
	ProductivityPatterns []ProductivityPattern `json:"productivity_patterns"`
	AttentionSpan        AttentionAnalysis     `json:"attention_span"`
	BreakPatterns        BreakAnalysis         `json:"break_patterns"`
	SessionEffectiveness SessionEffectiveness  `json:"session_effectiveness"`
}

// TimeWindow represents an optimal time period
type TimeWindow struct {
	StartTime     string  `json:"start_time"`
	EndTime       string  `json:"end_time"`
	DayOfWeek     string  `json:"day_of_week"`
	Effectiveness float64 `json:"effectiveness"`
	Confidence    float64 `json:"confidence"`
}

// ProductivityPattern analyzes productivity over time
type ProductivityPattern struct {
	Pattern     string   `json:"pattern"`
	Description string   `json:"description"`
	Frequency   string   `json:"frequency"`
	Impact      float64  `json:"impact"`
	Suggestions []string `json:"suggestions"`
}

// AttentionAnalysis analyzes attention and focus patterns
type AttentionAnalysis struct {
	AverageSpan        int      `json:"average_span_minutes"`
	OptimalDuration    int      `json:"optimal_duration_minutes"`
	FocusDeclineRate   float64  `json:"focus_decline_rate"`
	DistractionFactors []string `json:"distraction_factors"`
	ImprovementTips    []string `json:"improvement_tips"`
}

// BreakAnalysis analyzes break patterns and effectiveness
type BreakAnalysis struct {
	OptimalBreakLength    int      `json:"optimal_break_length_minutes"`
	BreakFrequency        string   `json:"break_frequency"`
	BreakEffectiveness    float64  `json:"break_effectiveness"`
	RecommendedActivities []string `json:"recommended_activities"`
}

// SessionEffectiveness analyzes study session quality
type SessionEffectiveness struct {
	AverageEffectiveness float64               `json:"average_effectiveness"`
	FactorsAnalysis      map[string]float64    `json:"factors_analysis"`
	OptimalConditions    []string              `json:"optimal_conditions"`
	EffectivenessMetrics []EffectivenessMetric `json:"effectiveness_metrics"`
}

// EffectivenessMetric represents a metric for session effectiveness
type EffectivenessMetric struct {
	Metric      string  `json:"metric"`
	Value       float64 `json:"value"`
	Benchmark   float64 `json:"benchmark"`
	Status      string  `json:"status"`
	Improvement string  `json:"improvement_suggestion"`
}

// CompetencyMapping maps skills and competencies
type CompetencyMapping struct {
	CoreCompetencies      []Competency          `json:"core_competencies"`
	SkillGaps             []SkillGap            `json:"skill_gaps"`
	LearningPathways      []LearningPathway     `json:"learning_pathways"`
	CompetencyMatrix      map[string]float64    `json:"competency_matrix"`
	DevelopmentPriorities []DevelopmentPriority `json:"development_priorities"`
}

// Competency represents a skill or knowledge area
type Competency struct {
	ID            string   `json:"id"`
	Name          string   `json:"name"`
	Category      string   `json:"category"`
	Level         float64  `json:"level"`
	Target        float64  `json:"target"`
	Importance    float64  `json:"importance"`
	Prerequisites []string `json:"prerequisites"`
	Applications  []string `json:"applications"`
}

// SkillGap represents a gap in skills or knowledge
type SkillGap struct {
	Skill         string   `json:"skill"`
	CurrentLevel  float64  `json:"current_level"`
	RequiredLevel float64  `json:"required_level"`
	Gap           float64  `json:"gap"`
	Priority      int      `json:"priority"`
	Timeline      string   `json:"estimated_timeline"`
	Resources     []string `json:"recommended_resources"`
}

// LearningPathway represents a structured learning path
type LearningPathway struct {
	ID          string        `json:"id"`
	Name        string        `json:"name"`
	Description string        `json:"description"`
	Steps       []PathwayStep `json:"steps"`
	Duration    string        `json:"estimated_duration"`
	Difficulty  string        `json:"difficulty"`
	Outcomes    []string      `json:"learning_outcomes"`
}

// PathwayStep represents a step in a learning pathway
type PathwayStep struct {
	Order       int      `json:"order"`
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Type        string   `json:"type"`
	Duration    string   `json:"duration"`
	Resources   []string `json:"resources"`
	Assessments []string `json:"assessments"`
}

// DevelopmentPriority represents a prioritized development area
type DevelopmentPriority struct {
	Area      string  `json:"area"`
	Priority  int     `json:"priority"`
	Impact    float64 `json:"impact"`
	Effort    float64 `json:"effort"`
	Timeline  string  `json:"timeline"`
	Rationale string  `json:"rationale"`
}

// PerformanceForecast predicts future performance
type PerformanceForecast struct {
	Metric     string     `json:"metric"`
	TimeFrame  string     `json:"time_frame"`
	Prediction float64    `json:"prediction"`
	Confidence float64    `json:"confidence"`
	Factors    []string   `json:"influencing_factors"`
	Scenarios  []Scenario `json:"scenarios"`
}

// Scenario represents different prediction scenarios
type Scenario struct {
	Name        string   `json:"name"`
	Probability float64  `json:"probability"`
	Outcome     float64  `json:"outcome"`
	Description string   `json:"description"`
	Conditions  []string `json:"conditions"`
}

// PredictiveInsights contains AI-generated insights and predictions
type PredictiveInsights struct {
	ExamReadiness           ExamReadiness            `json:"exam_readiness"`
	LearningRecommendations []LearningRecommendation `json:"learning_recommendations"`
	RiskAssessment          RiskAssessment           `json:"risk_assessment"`
	OpportunityAnalysis     OpportunityAnalysis      `json:"opportunity_analysis"`
	PersonalizedGoals       []PersonalizedGoal       `json:"personalized_goals"`
}

// ExamReadiness predicts readiness for upcoming exams
type ExamReadiness struct {
	OverallReadiness float64            `json:"overall_readiness"`
	SubjectReadiness map[string]float64 `json:"subject_readiness"`
	WeakAreas        []string           `json:"weak_areas"`
	StudyPlan        []StudyPlanItem    `json:"study_plan"`
	TimeRequired     int                `json:"time_required_hours"`
	ConfidenceLevel  float64            `json:"confidence_level"`
}

// StudyPlanItem represents an item in the study plan
type StudyPlanItem struct {
	Subject    string    `json:"subject"`
	Topic      string    `json:"topic"`
	Priority   int       `json:"priority"`
	TimeNeeded int       `json:"time_needed_minutes"`
	Difficulty string    `json:"difficulty"`
	Resources  []string  `json:"resources"`
	Deadline   time.Time `json:"deadline"`
}

// LearningRecommendation provides personalized learning suggestions
type LearningRecommendation struct {
	Type        RecommendationType `json:"type"`
	Title       string             `json:"title"`
	Description string             `json:"description"`
	Priority    int                `json:"priority"`
	Impact      float64            `json:"impact"`
	Effort      float64            `json:"effort"`
	Timeline    string             `json:"timeline"`
	Actions     []string           `json:"actions"`
	Resources   []string           `json:"resources"`
}

// RecommendationType defines types of recommendations
type RecommendationType string

const (
	RecommendationTypeStudyMethod RecommendationType = "study_method"
	RecommendationTypeContent     RecommendationType = "content"
	RecommendationTypeSchedule    RecommendationType = "schedule"
	RecommendationTypeStrategy    RecommendationType = "strategy"
	RecommendationTypeMotivation  RecommendationType = "motivation"
	RecommendationTypeResource    RecommendationType = "resource"
)

// RiskAssessment identifies potential learning risks
type RiskAssessment struct {
	OverallRisk   float64      `json:"overall_risk"`
	RiskFactors   []RiskFactor `json:"risk_factors"`
	Mitigations   []string     `json:"mitigations"`
	EarlyWarnings []string     `json:"early_warnings"`
}

// RiskFactor represents a potential risk to learning success
type RiskFactor struct {
	Factor      string   `json:"factor"`
	Probability float64  `json:"probability"`
	Impact      float64  `json:"impact"`
	Severity    string   `json:"severity"`
	Description string   `json:"description"`
	Prevention  []string `json:"prevention_strategies"`
}

// OpportunityAnalysis identifies learning opportunities
type OpportunityAnalysis struct {
	Opportunities []LearningOpportunity `json:"opportunities"`
	QuickWins     []string              `json:"quick_wins"`
	LongTermGoals []string              `json:"long_term_goals"`
}

// LearningOpportunity represents a learning opportunity
type LearningOpportunity struct {
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Potential   float64  `json:"potential"`
	Effort      float64  `json:"effort"`
	Timeline    string   `json:"timeline"`
	Benefits    []string `json:"benefits"`
}

// PersonalizedGoal represents a personalized learning goal
type PersonalizedGoal struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Type        string    `json:"type"`
	Target      float64   `json:"target"`
	Current     float64   `json:"current"`
	Deadline    time.Time `json:"deadline"`
	Priority    int       `json:"priority"`
	Milestones  []string  `json:"milestones"`
	Progress    float64   `json:"progress"`
}

// CognitiveLoadAnalysis analyzes mental effort and capacity
type CognitiveLoadAnalysis struct {
	AverageLoad      float64            `json:"average_load"`
	PeakLoad         float64            `json:"peak_load"`
	LoadDistribution map[string]float64 `json:"load_distribution"`
	OverloadRisk     float64            `json:"overload_risk"`
	OptimalLoad      float64            `json:"optimal_load"`
	Recommendations  []string           `json:"recommendations"`
}

// MotivationAnalysis analyzes motivation factors and trends
type MotivationAnalysis struct {
	MotivationLevel   float64                  `json:"motivation_level"`
	MotivationTrends  []MotivationTrend        `json:"motivation_trends"`
	MotivationFactors map[string]float64       `json:"motivation_factors"`
	Interventions     []MotivationIntervention `json:"interventions"`
}

// MotivationTrend represents motivation over time
type MotivationTrend struct {
	Period    string    `json:"period"`
	Level     float64   `json:"level"`
	Factors   []string  `json:"factors"`
	Events    []string  `json:"events"`
	Timestamp time.Time `json:"timestamp"`
}

// MotivationIntervention suggests motivation improvements
type MotivationIntervention struct {
	Type        string   `json:"type"`
	Description string   `json:"description"`
	Timing      string   `json:"timing"`
	Expected    float64  `json:"expected_impact"`
	Actions     []string `json:"actions"`
}

// Recommendation represents an actionable recommendation
type Recommendation struct {
	ID          string              `json:"id"`
	Type        RecommendationType  `json:"type"`
	Title       string              `json:"title"`
	Description string              `json:"description"`
	Priority    int                 `json:"priority"`
	Impact      float64             `json:"impact"`
	Confidence  float64             `json:"confidence"`
	Actions     []RecommendedAction `json:"actions"`
	Timeline    string              `json:"timeline"`
	Category    string              `json:"category"`
}

// RecommendedAction represents a specific action to take
type RecommendedAction struct {
	Action      string   `json:"action"`
	Description string   `json:"description"`
	Order       int      `json:"order"`
	Duration    string   `json:"duration"`
	Resources   []string `json:"resources"`
}
