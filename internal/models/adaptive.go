package models

import (
	"github.com/google/uuid"
	"time"
)

// AdaptiveLearningEngine represents the core adaptive learning system
type AdaptiveLearningEngine struct {
	UserID           uuid.UUID            `json:"user_id"`
	CurrentAbility   float64              `json:"current_ability"` // IRT theta parameter (-4 to +4)
	AbilityHistory   []AbilityMeasurement `json:"ability_history"`
	MasteryLevels    map[int]MasteryLevel `json:"mastery_levels"`    // topic_id -> mastery
	LearningVelocity float64              `json:"learning_velocity"` // rate of improvement
	LastUpdated      time.Time            `json:"last_updated"`
}

// AbilityMeasurement tracks ability over time
type AbilityMeasurement struct {
	Timestamp  time.Time `json:"timestamp"`
	Ability    float64   `json:"ability"`
	Confidence float64   `json:"confidence"` // Standard error
	Context    string    `json:"context"`    // What triggered this measurement
}

// MasteryLevel represents student's mastery of a topic
type MasteryLevel struct {
	TopicID            int             `json:"topic_id"`
	MasteryScore       float64         `json:"mastery_score"`    // 0.0 to 1.0
	ConfidenceLevel    float64         `json:"confidence_level"` // Statistical confidence
	LastPracticed      time.Time       `json:"last_practiced"`
	PracticeCount      int             `json:"practice_count"`
	ConsecutiveCorrect int             `json:"consecutive_correct"`
	NeedsReview        bool            `json:"needs_review"` // Spaced repetition flag
	NextReviewDate     time.Time       `json:"next_review_date"`
	DifficultyRange    DifficultyRange `json:"difficulty_range"`
}

// DifficultyRange represents the range of difficulties a student can handle
type DifficultyRange struct {
	MinDifficulty     float64 `json:"min_difficulty"` // IRT difficulty parameter
	MaxDifficulty     float64 `json:"max_difficulty"`
	OptimalDifficulty float64 `json:"optimal_difficulty"` // Sweet spot for learning
}

// LearningPath represents a personalized sequence of learning activities
type LearningPath struct {
	ID             uuid.UUID      `json:"id"`
	UserID         uuid.UUID      `json:"user_id"`
	SubjectID      int            `json:"subject_id"`
	PathName       string         `json:"path_name"`
	CurrentStep    int            `json:"current_step"`
	TotalSteps     int            `json:"total_steps"`
	EstimatedTime  int            `json:"estimated_time_minutes"`
	CompletionRate float64        `json:"completion_rate"`
	Steps          []LearningStep `json:"steps"`
	AdaptiveFlags  AdaptiveFlags  `json:"adaptive_flags"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
}

// LearningStep represents a single step in the learning path
type LearningStep struct {
	StepNumber      int                  `json:"step_number"`
	StepType        LearningStepType     `json:"step_type"`
	TopicID         int                  `json:"topic_id"`
	TargetMastery   float64              `json:"target_mastery"`
	Prerequisites   []int                `json:"prerequisites"` // topic_ids
	EstimatedTime   int                  `json:"estimated_time_minutes"`
	DifficultyLevel float64              `json:"difficulty_level"`
	Resources       []LearningResource   `json:"resources"`
	Assessments     []AdaptiveAssessment `json:"assessments"`
	IsCompleted     bool                 `json:"is_completed"`
	CompletedAt     *time.Time           `json:"completed_at,omitempty"`
}

// LearningStepType defines different types of learning activities
type LearningStepType string

const (
	StepTypeIntroduction LearningStepType = "introduction"
	StepTypeConcept      LearningStepType = "concept"
	StepTypePractice     LearningStepType = "practice"
	StepTypeAssessment   LearningStepType = "assessment"
	StepTypeReview       LearningStepType = "review"
	StepTypeApplication  LearningStepType = "application"
	StepTypeSynthesis    LearningStepType = "synthesis"
	StepTypeRemediation  LearningStepType = "remediation"
)

// LearningResource represents educational content
type LearningResource struct {
	Type        ResourceType `json:"type"`
	Title       string       `json:"title"`
	URL         string       `json:"url,omitempty"`
	Content     string       `json:"content,omitempty"`
	Duration    int          `json:"duration_minutes"`
	Difficulty  float64      `json:"difficulty"`
	Interactive bool         `json:"interactive"`
}

// ResourceType defines types of learning resources
type ResourceType string

const (
	ResourceTypeVideo       ResourceType = "video"
	ResourceTypeArticle     ResourceType = "article"
	ResourceTypeInteractive ResourceType = "interactive"
	ResourceTypeSimulation  ResourceType = "simulation"
	ResourceTypePractice    ResourceType = "practice"
	ResourceTypeQuiz        ResourceType = "quiz"
)

// AdaptiveAssessment represents an assessment within a learning step
type AdaptiveAssessment struct {
	ID               uuid.UUID      `json:"id"`
	Type             AssessmentType `json:"type"`
	MinQuestions     int            `json:"min_questions"`
	MaxQuestions     int            `json:"max_questions"`
	MasteryThreshold float64        `json:"mastery_threshold"`
	AdaptiveRules    AdaptiveRules  `json:"adaptive_rules"`
}

// AssessmentType defines different assessment approaches
type AssessmentType string

const (
	AssessmentTypeAdaptive   AssessmentType = "adaptive"
	AssessmentTypeMastery    AssessmentType = "mastery"
	AssessmentTypeDiagnostic AssessmentType = "diagnostic"
	AssessmentTypeFormative  AssessmentType = "formative"
	AssessmentTypeSummative  AssessmentType = "summative"
)

// AdaptiveRules define how the assessment adapts
type AdaptiveRules struct {
	StoppingCriteria     StoppingCriteria     `json:"stopping_criteria"`
	DifficultyAdjustment DifficultyAdjustment `json:"difficulty_adjustment"`
	ContentSelection     ContentSelection     `json:"content_selection"`
}

// StoppingCriteria defines when to stop an adaptive assessment
type StoppingCriteria struct {
	MaxQuestions        int     `json:"max_questions"`
	MinQuestions        int     `json:"min_questions"`
	ConfidenceThreshold float64 `json:"confidence_threshold"`
	MasteryThreshold    float64 `json:"mastery_threshold"`
	TimeLimit           int     `json:"time_limit_minutes"`
}

// DifficultyAdjustment defines how difficulty changes
type DifficultyAdjustment struct {
	InitialDifficulty    float64 `json:"initial_difficulty"`
	AdjustmentRate       float64 `json:"adjustment_rate"`
	MaxAdjustment        float64 `json:"max_adjustment"`
	ConsecutiveThreshold int     `json:"consecutive_threshold"`
}

// ContentSelection defines how content is chosen
type ContentSelection struct {
	Strategy          SelectionStrategy  `json:"strategy"`
	TopicWeights      map[int]float64    `json:"topic_weights"`
	DifficultyWeights map[string]float64 `json:"difficulty_weights"`
	RecencyBias       float64            `json:"recency_bias"`
}

// SelectionStrategy defines content selection approaches
type SelectionStrategy string

const (
	StrategyRandom           SelectionStrategy = "random"
	StrategyAdaptive         SelectionStrategy = "adaptive"
	StrategyMasteryBased     SelectionStrategy = "mastery_based"
	StrategyWeaknessFocused  SelectionStrategy = "weakness_focused"
	StrategySpacedRepetition SelectionStrategy = "spaced_repetition"
)

// AdaptiveFlags control various adaptive behaviors
type AdaptiveFlags struct {
	EnableRealTimeAdaptation bool `json:"enable_realtime_adaptation"`
	EnableSpacedRepetition   bool `json:"enable_spaced_repetition"`
	EnableMasteryProgression bool `json:"enable_mastery_progression"`
	EnableDifficultyScaling  bool `json:"enable_difficulty_scaling"`
	EnablePersonalization    bool `json:"enable_personalization"`
}

// SpacedRepetitionSchedule manages when content should be reviewed
type SpacedRepetitionSchedule struct {
	TopicID         int       `json:"topic_id"`
	UserID          uuid.UUID `json:"user_id"`
	Interval        int       `json:"interval_days"`
	EaseFactor      float64   `json:"ease_factor"`
	RepetitionCount int       `json:"repetition_count"`
	LastReviewed    time.Time `json:"last_reviewed"`
	NextReview      time.Time `json:"next_review"`
	Quality         int       `json:"quality"` // 0-5 scale from SM-2 algorithm
}

// IRTParameters represents Item Response Theory parameters
type IRTParameters struct {
	Difficulty     float64 `json:"difficulty"`     // b parameter
	Discrimination float64 `json:"discrimination"` // a parameter
	Guessing       float64 `json:"guessing"`       // c parameter
	Ability        float64 `json:"ability"`        // theta parameter
}

// AdaptiveResponse represents a student's response with IRT analysis
type AdaptiveResponse struct {
	QuestionID      uuid.UUID     `json:"question_id"`
	UserID          uuid.UUID     `json:"user_id"`
	IsCorrect       bool          `json:"is_correct"`
	ResponseTime    int           `json:"response_time_seconds"`
	IRTParameters   IRTParameters `json:"irt_parameters"`
	AbilityEstimate float64       `json:"ability_estimate"`
	Confidence      float64       `json:"confidence"`
	Timestamp       time.Time     `json:"timestamp"`
}
