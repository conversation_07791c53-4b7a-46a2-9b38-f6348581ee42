package models

import (
	"time"

	"github.com/google/uuid"
)

// ReportType represents the type of report
type ReportType string

const (
	ReportTypePerformance ReportType = "performance"
	ReportTypeProgress    ReportType = "progress"
	ReportTypeDetailed    ReportType = "detailed"
	ReportTypeComparative ReportType = "comparative"
)

// ReportFormat represents the output format of the report
type ReportFormat string

const (
	ReportFormatPDF  ReportFormat = "pdf"
	ReportFormatHTML ReportFormat = "html"
)

// ReportRequest represents a request to generate a report
type ReportRequest struct {
	UserID          uuid.UUID    `json:"user_id"`
	ReportType      ReportType   `json:"report_type"`
	Format          ReportFormat `json:"format"`
	TimeFilter      TimeFilter   `json:"time_filter"`
	DateRange       *DateRange   `json:"date_range,omitempty"`
	IncludeSections []string     `json:"include_sections,omitempty"`
	CustomTitle     string       `json:"custom_title,omitempty"`
}

// ReportData represents the comprehensive data for report generation
type ReportData struct {
	// Header Information
	UserInfo       UserReportInfo `json:"user_info"`
	ReportMetadata ReportMetadata `json:"report_metadata"`

	// Analytics Data
	Dashboard       *DashboardSummary    `json:"dashboard"`
	Analytics       *AdvancedAnalytics   `json:"analytics"`
	Trends          []PerformanceTrend   `json:"trends"`
	Insights        []AnalyticsInsight   `json:"insights"`
	StudyPatterns   *StudyPattern        `json:"study_patterns"`
	Recommendations []RecommendationItem `json:"recommendations"`
	HeatmapData     []HeatmapData        `json:"heatmap_data"`

	// Detailed Analysis
	TopicAnalysis      []TopicReportData  `json:"topic_analysis"`
	TestHistory        []TestReportData   `json:"test_history"`
	PerformanceMetrics PerformanceMetrics `json:"performance_metrics"`

	// Charts and Visualizations (base64 encoded images)
	Charts ReportCharts `json:"charts"`
}

// UserReportInfo contains user information for the report
type UserReportInfo struct {
	Name        string    `json:"name"`
	Email       string    `json:"email"`
	Grade       string    `json:"grade,omitempty"`
	School      string    `json:"school,omitempty"`
	GeneratedAt time.Time `json:"generated_at"`
}

// ReportMetadata contains metadata about the report
type ReportMetadata struct {
	Title       string       `json:"title"`
	Type        ReportType   `json:"type"`
	Format      ReportFormat `json:"format"`
	TimeFilter  TimeFilter   `json:"time_filter"`
	DateRange   *DateRange   `json:"date_range,omitempty"`
	GeneratedAt time.Time    `json:"generated_at"`
	Version     string       `json:"version"`
}

// TopicReportData represents detailed topic analysis for reports
type TopicReportData struct {
	TopicID            int      `json:"topic_id"`
	TopicName          string   `json:"topic_name"`
	SubjectName        string   `json:"subject_name"`
	ProficiencyScore   float64  `json:"proficiency_score"`
	QuestionsAttempted int      `json:"questions_attempted"`
	CorrectAnswers     int      `json:"correct_answers"`
	AverageTime        float64  `json:"average_time_seconds"`
	Difficulty         string   `json:"difficulty"`
	Improvement        float64  `json:"improvement_percentage"`
	Recommendations    []string `json:"recommendations"`
}

// TestReportData represents test information for reports
type TestReportData struct {
	TestID          uuid.UUID `json:"test_id"`
	TestDate        time.Time `json:"test_date"`
	Subject         string    `json:"subject"`
	Difficulty      string    `json:"difficulty"`
	Score           float64   `json:"score"`
	TotalQuestions  int       `json:"total_questions"`
	CorrectAnswers  int       `json:"correct_answers"`
	TimeTaken       int       `json:"time_taken_seconds"`
	TopicsCount     int       `json:"topics_count"`
	PerformanceRank string    `json:"performance_rank"`
}

// PerformanceMetrics represents calculated performance metrics
type PerformanceMetrics struct {
	OverallScore       float64  `json:"overall_score"`
	ImprovementRate    float64  `json:"improvement_rate"`
	ConsistencyScore   float64  `json:"consistency_score"`
	StrengthsCount     int      `json:"strengths_count"`
	WeaknessesCount    int      `json:"weaknesses_count"`
	TestsCompleted     int      `json:"tests_completed"`
	StudyTimeHours     float64  `json:"study_time_hours"`
	AverageSessionTime float64  `json:"average_session_time"`
	BestSubject        string   `json:"best_subject"`
	WeakestSubject     string   `json:"weakest_subject"`
	RecommendedFocus   []string `json:"recommended_focus"`
}

// ReportCharts contains base64 encoded chart images
type ReportCharts struct {
	PerformanceTrend   string `json:"performance_trend"`
	SubjectRadar       string `json:"subject_radar"`
	TopicHeatmap       string `json:"topic_heatmap"`
	ProgressChart      string `json:"progress_chart"`
	DifficultyAnalysis string `json:"difficulty_analysis"`
}

// ReportResponse represents the response after generating a report
type ReportResponse struct {
	ReportID    uuid.UUID    `json:"report_id"`
	DownloadURL string       `json:"download_url"`
	Format      ReportFormat `json:"format"`
	Size        int64        `json:"size_bytes"`
	GeneratedAt time.Time    `json:"generated_at"`
	ExpiresAt   time.Time    `json:"expires_at"`
}

// ReportTemplate represents a report template configuration
type ReportTemplate struct {
	ID          string     `json:"id"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Type        ReportType `json:"type"`
	Sections    []string   `json:"sections"`
	IsDefault   bool       `json:"is_default"`
}

// Available report sections
var ReportSections = map[string]string{
	"summary":         "Executive Summary",
	"performance":     "Performance Overview",
	"trends":          "Performance Trends",
	"strengths":       "Strengths Analysis",
	"weaknesses":      "Areas for Improvement",
	"insights":        "AI-Generated Insights",
	"recommendations": "Personalized Recommendations",
	"study_patterns":  "Study Behavior Analysis",
	"topic_analysis":  "Detailed Topic Analysis",
	"test_history":    "Test History",
	"charts":          "Visual Analytics",
	"action_plan":     "Action Plan",
}

// Default report templates
var DefaultReportTemplates = []ReportTemplate{
	{
		ID:          "performance_summary",
		Name:        "Performance Summary",
		Description: "Quick overview of recent performance and key insights",
		Type:        ReportTypePerformance,
		Sections:    []string{"summary", "performance", "strengths", "weaknesses", "charts"},
		IsDefault:   true,
	},
	{
		ID:          "detailed_analysis",
		Name:        "Detailed Analysis",
		Description: "Comprehensive analysis with all available data and insights",
		Type:        ReportTypeDetailed,
		Sections:    []string{"summary", "performance", "trends", "strengths", "weaknesses", "insights", "recommendations", "study_patterns", "topic_analysis", "test_history", "charts", "action_plan"},
		IsDefault:   false,
	},
	{
		ID:          "progress_report",
		Name:        "Progress Report",
		Description: "Focus on learning progress and improvement trends",
		Type:        ReportTypeProgress,
		Sections:    []string{"summary", "trends", "insights", "study_patterns", "recommendations", "charts"},
		IsDefault:   false,
	},
}
