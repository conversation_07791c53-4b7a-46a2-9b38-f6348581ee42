package models

import (
	"time"

	"github.com/google/uuid"
)

// GamificationProfile represents a user's gamification data
type GamificationProfile struct {
	UserID           uuid.UUID               `json:"user_id"`
	Level            int                     `json:"level"`
	Experience       int                     `json:"experience"`
	ExperienceToNext int                     `json:"experience_to_next"`
	TotalPoints      int                     `json:"total_points"`
	AvailablePoints  int                     `json:"available_points"`
	Rank             string                  `json:"rank"`
	Title            string                  `json:"title"`
	Badges           []Badge                 `json:"badges"`
	Achievements     []Achievement           `json:"achievements"`
	Streaks          []Streak                `json:"streaks"`
	SkillTree        SkillTree               `json:"skill_tree"`
	Statistics       UserStatistics          `json:"statistics"`
	Preferences      GamificationPreferences `json:"preferences"`
	CreatedAt        time.Time               `json:"created_at"`
	UpdatedAt        time.Time               `json:"updated_at"`
}

// Badge represents an earned badge
type Badge struct {
	ID          string      `json:"id"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	IconURL     string      `json:"icon_url"`
	Rarity      BadgeRarity `json:"rarity"`
	Category    string      `json:"category"`
	EarnedAt    time.Time   `json:"earned_at"`
	Progress    int         `json:"progress"`
	MaxProgress int         `json:"max_progress"`
}

// BadgeRarity represents badge rarity levels
type BadgeRarity string

const (
	BadgeRarityCommon    BadgeRarity = "common"
	BadgeRarityUncommon  BadgeRarity = "uncommon"
	BadgeRarityRare      BadgeRarity = "rare"
	BadgeRarityEpic      BadgeRarity = "epic"
	BadgeRarityLegendary BadgeRarity = "legendary"
)

// Achievement represents a completed achievement
type Achievement struct {
	ID          string              `json:"id"`
	Name        string              `json:"name"`
	Description string              `json:"description"`
	Type        AchievementType     `json:"type"`
	Category    string              `json:"category"`
	Criteria    AchievementCriteria `json:"criteria"`
	Rewards     AchievementRewards  `json:"rewards"`
	Progress    int                 `json:"progress"`
	MaxProgress int                 `json:"max_progress"`
	IsCompleted bool                `json:"is_completed"`
	CompletedAt *time.Time          `json:"completed_at,omitempty"`
	IsSecret    bool                `json:"is_secret"`
	Difficulty  int                 `json:"difficulty"` // 1-5 scale
}

// AchievementType defines different types of achievements
type AchievementType string

const (
	AchievementTypeProgress    AchievementType = "progress"
	AchievementTypeStreak      AchievementType = "streak"
	AchievementTypeMastery     AchievementType = "mastery"
	AchievementTypeSocial      AchievementType = "social"
	AchievementTypeChallenge   AchievementType = "challenge"
	AchievementTypeExploration AchievementType = "exploration"
	AchievementTypeTime        AchievementType = "time"
	AchievementTypeSpecial     AchievementType = "special"
)

// AchievementCriteria defines what needs to be accomplished
type AchievementCriteria struct {
	Type         string                 `json:"type"`
	Target       int                    `json:"target"`
	TimeFrame    *time.Duration         `json:"time_frame,omitempty"`
	Conditions   map[string]interface{} `json:"conditions"`
	Dependencies []string               `json:"dependencies"` // Other achievement IDs
}

// AchievementRewards defines what the user gets for completing an achievement
type AchievementRewards struct {
	Experience  int      `json:"experience"`
	Points      int      `json:"points"`
	Badges      []string `json:"badges"`
	Titles      []string `json:"titles"`
	SkillPoints int      `json:"skill_points"`
	Items       []string `json:"items"`
}

// Streak represents a learning streak
type Streak struct {
	Type        StreakType `json:"type"`
	Current     int        `json:"current"`
	Longest     int        `json:"longest"`
	LastUpdated time.Time  `json:"last_updated"`
	IsActive    bool       `json:"is_active"`
	Multiplier  float64    `json:"multiplier"`
}

// StreakType defines different types of streaks
type StreakType string

const (
	StreakTypeDaily   StreakType = "daily"
	StreakTypeWeekly  StreakType = "weekly"
	StreakTypePerfect StreakType = "perfect"
	StreakTypeSubject StreakType = "subject"
)

// SkillTree represents a user's skill progression
type SkillTree struct {
	Subjects      []SubjectSkillTree `json:"subjects"`
	TotalNodes    int                `json:"total_nodes"`
	UnlockedNodes int                `json:"unlocked_nodes"`
	SkillPoints   int                `json:"skill_points"`
}

// SubjectSkillTree represents skills for a specific subject
type SubjectSkillTree struct {
	SubjectID   int         `json:"subject_id"`
	SubjectName string      `json:"subject_name"`
	Nodes       []SkillNode `json:"nodes"`
	Progress    float64     `json:"progress"`
}

// SkillNode represents a single skill in the tree
type SkillNode struct {
	ID            string      `json:"id"`
	Name          string      `json:"name"`
	Description   string      `json:"description"`
	Type          SkillType   `json:"type"`
	Level         int         `json:"level"`
	MaxLevel      int         `json:"max_level"`
	IsUnlocked    bool        `json:"is_unlocked"`
	Prerequisites []string    `json:"prerequisites"`
	Position      Position    `json:"position"`
	Rewards       NodeRewards `json:"rewards"`
	Cost          int         `json:"cost"` // Skill points required
}

// SkillType defines different types of skills
type SkillType string

const (
	SkillTypeCore     SkillType = "core"
	SkillTypeAdvanced SkillType = "advanced"
	SkillTypeSpecial  SkillType = "special"
	SkillTypeMastery  SkillType = "mastery"
)

// NodeRewards defines what unlocking a skill node provides
type NodeRewards struct {
	ExperienceBonus float64  `json:"experience_bonus"`
	PointsBonus     float64  `json:"points_bonus"`
	NewFeatures     []string `json:"new_features"`
	Abilities       []string `json:"abilities"`
}

// UserStatistics tracks detailed user performance
type UserStatistics struct {
	TotalQuestions   int                        `json:"total_questions"`
	CorrectAnswers   int                        `json:"correct_answers"`
	Accuracy         float64                    `json:"accuracy"`
	TotalStudyTime   int                        `json:"total_study_time_minutes"`
	TestsCompleted   int                        `json:"tests_completed"`
	PerfectScores    int                        `json:"perfect_scores"`
	SubjectStats     map[int]SubjectStats       `json:"subject_stats"`
	DifficultyStats  map[string]DifficultyStats `json:"difficulty_stats"`
	WeeklyActivity   []DailyActivity            `json:"weekly_activity"`
	MonthlyProgress  []MonthlyProgress          `json:"monthly_progress"`
	LearningVelocity float64                    `json:"learning_velocity"`
	ConsistencyScore float64                    `json:"consistency_score"`
}

// SubjectStats tracks performance in specific subjects
type SubjectStats struct {
	SubjectID         int       `json:"subject_id"`
	QuestionsAnswered int       `json:"questions_answered"`
	CorrectAnswers    int       `json:"correct_answers"`
	Accuracy          float64   `json:"accuracy"`
	StudyTime         int       `json:"study_time_minutes"`
	MasteryLevel      float64   `json:"mastery_level"`
	LastPracticed     time.Time `json:"last_practiced"`
}

// DifficultyStats tracks performance by difficulty level
type DifficultyStats struct {
	QuestionsAnswered int     `json:"questions_answered"`
	CorrectAnswers    int     `json:"correct_answers"`
	Accuracy          float64 `json:"accuracy"`
	AverageTime       float64 `json:"average_time_seconds"`
}

// DailyActivity tracks daily learning activity
type DailyActivity struct {
	Date              time.Time `json:"date"`
	QuestionsAnswered int       `json:"questions_answered"`
	StudyTime         int       `json:"study_time_minutes"`
	ExperienceGained  int       `json:"experience_gained"`
	TestsCompleted    int       `json:"tests_completed"`
}

// MonthlyProgress tracks monthly learning progress
type MonthlyProgress struct {
	Month              time.Time `json:"month"`
	TotalQuestions     int       `json:"total_questions"`
	Accuracy           float64   `json:"accuracy"`
	StudyTime          int       `json:"study_time_minutes"`
	LevelGained        int       `json:"levels_gained"`
	AchievementsEarned int       `json:"achievements_earned"`
}

// GamificationPreferences stores user preferences for gamification
type GamificationPreferences struct {
	ShowNotifications   bool     `json:"show_notifications"`
	ShowLeaderboards    bool     `json:"show_leaderboards"`
	ShowProgress        bool     `json:"show_progress"`
	PreferredChallenges []string `json:"preferred_challenges"`
	NotificationTypes   []string `json:"notification_types"`
	PrivacyLevel        string   `json:"privacy_level"` // "public", "friends", "private"
}

// Leaderboard represents a competitive ranking
type Leaderboard struct {
	ID        string             `json:"id"`
	Name      string             `json:"name"`
	Type      LeaderboardType    `json:"type"`
	Period    LeaderboardPeriod  `json:"period"`
	Category  string             `json:"category"`
	Entries   []LeaderboardEntry `json:"entries"`
	UpdatedAt time.Time          `json:"updated_at"`
	StartDate time.Time          `json:"start_date"`
	EndDate   *time.Time         `json:"end_date,omitempty"`
}

// LeaderboardType defines different types of leaderboards
type LeaderboardType string

const (
	LeaderboardTypeExperience LeaderboardType = "experience"
	LeaderboardTypeAccuracy   LeaderboardType = "accuracy"
	LeaderboardTypeStreak     LeaderboardType = "streak"
	LeaderboardTypeSpeed      LeaderboardType = "speed"
	LeaderboardTypeSubject    LeaderboardType = "subject"
)

// LeaderboardPeriod defines the time period for leaderboards
type LeaderboardPeriod string

const (
	LeaderboardPeriodDaily   LeaderboardPeriod = "daily"
	LeaderboardPeriodWeekly  LeaderboardPeriod = "weekly"
	LeaderboardPeriodMonthly LeaderboardPeriod = "monthly"
	LeaderboardPeriodAllTime LeaderboardPeriod = "all_time"
)

// LeaderboardEntry represents a single entry in a leaderboard
type LeaderboardEntry struct {
	UserID      uuid.UUID `json:"user_id"`
	Username    string    `json:"username"`
	DisplayName string    `json:"display_name"`
	Avatar      string    `json:"avatar"`
	Rank        int       `json:"rank"`
	Score       float64   `json:"score"`
	Change      int       `json:"change"` // Position change from last period
	Badge       *Badge    `json:"badge,omitempty"`
}

// Challenge represents a time-limited challenge
type Challenge struct {
	ID           string                `json:"id"`
	Name         string                `json:"name"`
	Description  string                `json:"description"`
	Type         ChallengeType         `json:"type"`
	Difficulty   int                   `json:"difficulty"`
	Requirements ChallengeRequirements `json:"requirements"`
	Rewards      ChallengeRewards      `json:"rewards"`
	StartDate    time.Time             `json:"start_date"`
	EndDate      time.Time             `json:"end_date"`
	Participants int                   `json:"participants"`
	IsActive     bool                  `json:"is_active"`
	IsGlobal     bool                  `json:"is_global"`
}

// ChallengeType defines different types of challenges
type ChallengeType string

const (
	ChallengeTypeDaily     ChallengeType = "daily"
	ChallengeTypeWeekly    ChallengeType = "weekly"
	ChallengeTypeMonthly   ChallengeType = "monthly"
	ChallengeTypeSpecial   ChallengeType = "special"
	ChallengeTypeCommunity ChallengeType = "community"
)

// ChallengeRequirements defines what needs to be accomplished
type ChallengeRequirements struct {
	Type       string                 `json:"type"`
	Target     int                    `json:"target"`
	Conditions map[string]interface{} `json:"conditions"`
	SubjectID  *int                   `json:"subject_id,omitempty"`
}

// ChallengeRewards defines challenge completion rewards
type ChallengeRewards struct {
	Experience  int      `json:"experience"`
	Points      int      `json:"points"`
	Badges      []string `json:"badges"`
	SkillPoints int      `json:"skill_points"`
	Title       string   `json:"title"`
	Special     []string `json:"special"`
}

// UserChallenge represents a user's participation in a challenge
type UserChallenge struct {
	UserID      uuid.UUID  `json:"user_id"`
	ChallengeID string     `json:"challenge_id"`
	Progress    int        `json:"progress"`
	IsCompleted bool       `json:"is_completed"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	Rank        int        `json:"rank"`
	JoinedAt    time.Time  `json:"joined_at"`
}

// Notification represents a gamification notification
type Notification struct {
	ID        uuid.UUID              `json:"id"`
	UserID    uuid.UUID              `json:"user_id"`
	Type      NotificationType       `json:"type"`
	Title     string                 `json:"title"`
	Message   string                 `json:"message"`
	Data      map[string]interface{} `json:"data"`
	IsRead    bool                   `json:"is_read"`
	CreatedAt time.Time              `json:"created_at"`
	ExpiresAt *time.Time             `json:"expires_at,omitempty"`
}

// NotificationType defines different types of notifications
type NotificationType string

const (
	NotificationTypeAchievement NotificationType = "achievement"
	NotificationTypeLevelUp     NotificationType = "level_up"
	NotificationTypeStreak      NotificationType = "streak"
	NotificationTypeChallenge   NotificationType = "challenge"
	NotificationTypeBadge       NotificationType = "badge"
	NotificationTypeLeaderboard NotificationType = "leaderboard"
	NotificationTypeSocial      NotificationType = "social"
)
