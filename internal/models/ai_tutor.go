package models

import (
	"time"

	"github.com/google/uuid"
)

// AITutor represents an AI-powered learning assistant
type AITutor struct {
	ID            uuid.UUID        `json:"id"`
	UserID        uuid.UUID        `json:"user_id"`
	Name          string           `json:"name"`
	Personality   TutorPersonality `json:"personality"`
	Specialties   []string         `json:"specialties"`
	LearningStyle string           `json:"learning_style"`
	Preferences   TutorPreferences `json:"preferences"`
	Knowledge     TutorKnowledge   `json:"knowledge"`
	Statistics    TutorStatistics  `json:"statistics"`
	IsActive      bool             `json:"is_active"`
	CreatedAt     time.Time        `json:"created_at"`
	UpdatedAt     time.Time        `json:"updated_at"`
}

// TutorPersonality defines the AI tutor's personality traits
type TutorPersonality struct {
	Type           PersonalityType `json:"type"`
	Encouragement  int             `json:"encouragement"`  // 1-10 scale
	Patience       int             `json:"patience"`       // 1-10 scale
	Formality      int             `json:"formality"`      // 1-10 scale
	Humor          int             `json:"humor"`          // 1-10 scale
	Directness     int             `json:"directness"`     // 1-10 scale
	Supportiveness int             `json:"supportiveness"` // 1-10 scale
}

// PersonalityType defines different tutor personality types
type PersonalityType string

const (
	PersonalityTypeMentor      PersonalityType = "mentor"
	PersonalityTypeFriend      PersonalityType = "friend"
	PersonalityTypeCoach       PersonalityType = "coach"
	PersonalityTypeProfessor   PersonalityType = "professor"
	PersonalityTypeCheerleader PersonalityType = "cheerleader"
)

// TutorPreferences stores user preferences for the AI tutor
type TutorPreferences struct {
	ExplanationStyle   ExplanationStyle `json:"explanation_style"`
	FeedbackFrequency  string           `json:"feedback_frequency"`
	HintLevel          string           `json:"hint_level"`
	MotivationStyle    string           `json:"motivation_style"`
	ErrorHandling      string           `json:"error_handling"`
	ProgressTracking   bool             `json:"progress_tracking"`
	PersonalizedGoals  bool             `json:"personalized_goals"`
	AdaptiveDifficulty bool             `json:"adaptive_difficulty"`
}

// ExplanationStyle defines how the tutor explains concepts
type ExplanationStyle string

const (
	ExplanationStyleSimple     ExplanationStyle = "simple"
	ExplanationStyleDetailed   ExplanationStyle = "detailed"
	ExplanationStyleVisual     ExplanationStyle = "visual"
	ExplanationStyleAnalogy    ExplanationStyle = "analogy"
	ExplanationStyleStepByStep ExplanationStyle = "step_by_step"
	ExplanationStyleSocratic   ExplanationStyle = "socratic"
)

// TutorKnowledge represents the tutor's knowledge base
type TutorKnowledge struct {
	SubjectExpertise map[int]float64   `json:"subject_expertise"` // subject_id -> expertise level (0-1)
	TopicMastery     map[int]float64   `json:"topic_mastery"`     // topic_id -> mastery level (0-1)
	TeachingMethods  []TeachingMethod  `json:"teaching_methods"`
	LearningPatterns []LearningPattern `json:"learning_patterns"`
	CommonMistakes   []CommonMistake   `json:"common_mistakes"`
}

// TeachingMethod represents different teaching approaches
type TeachingMethod struct {
	ID            string  `json:"id"`
	Name          string  `json:"name"`
	Description   string  `json:"description"`
	Effectiveness float64 `json:"effectiveness"`
	UsageCount    int     `json:"usage_count"`
	SubjectIDs    []int   `json:"subject_ids"`
}

// LearningPattern represents identified learning patterns
type LearningPattern struct {
	ID            string                 `json:"id"`
	Pattern       string                 `json:"pattern"`
	Frequency     int                    `json:"frequency"`
	Context       map[string]interface{} `json:"context"`
	Effectiveness float64                `json:"effectiveness"`
}

// CommonMistake represents frequently made mistakes
type CommonMistake struct {
	ID             string   `json:"id"`
	Description    string   `json:"description"`
	Category       string   `json:"category"`
	Frequency      int      `json:"frequency"`
	Solutions      []string `json:"solutions"`
	PreventionTips []string `json:"prevention_tips"`
	TopicIDs       []int    `json:"topic_ids"`
}

// TutorStatistics tracks tutor performance and usage
type TutorStatistics struct {
	TotalInteractions   int                `json:"total_interactions"`
	SuccessfulHelps     int                `json:"successful_helps"`
	AverageResponseTime float64            `json:"average_response_time"`
	UserSatisfaction    float64            `json:"user_satisfaction"`
	SubjectInteractions map[int]int        `json:"subject_interactions"`
	MethodEffectiveness map[string]float64 `json:"method_effectiveness"`
	ImprovementRate     float64            `json:"improvement_rate"`
	LastInteraction     time.Time          `json:"last_interaction"`
}

// TutorSession represents a learning session with the AI tutor
type TutorSession struct {
	ID          uuid.UUID       `json:"id"`
	UserID      uuid.UUID       `json:"user_id"`
	TutorID     uuid.UUID       `json:"tutor_id"`
	SubjectID   *int            `json:"subject_id,omitempty"`
	TopicID     *int            `json:"topic_id,omitempty"`
	SessionType SessionType     `json:"session_type"`
	Status      SessionStatus   `json:"status"`
	Messages    []TutorMessage  `json:"messages"`
	Context     SessionContext  `json:"context"`
	Goals       []SessionGoal   `json:"goals"`
	Outcomes    SessionOutcomes `json:"outcomes"`
	StartedAt   time.Time       `json:"started_at"`
	EndedAt     *time.Time      `json:"ended_at,omitempty"`
	Duration    int             `json:"duration_minutes"`
}

// SessionType defines different types of tutoring sessions
type SessionType string

const (
	SessionTypeHelp        SessionType = "help"
	SessionTypeExplanation SessionType = "explanation"
	SessionTypeReview      SessionType = "review"
	SessionTypePractice    SessionType = "practice"
	SessionTypeMotivation  SessionType = "motivation"
	SessionTypeStrategy    SessionType = "strategy"
)

// SessionStatus defines the current status of a session
type SessionStatus string

const (
	SessionStatusActive    SessionStatus = "active"
	SessionStatusCompleted SessionStatus = "completed"
	SessionStatusPaused    SessionStatus = "paused"
	SessionStatusAbandoned SessionStatus = "abandoned"
)

// TutorMessage represents a message in the conversation
type TutorMessage struct {
	ID        uuid.UUID       `json:"id"`
	Sender    MessageSender   `json:"sender"`
	Content   string          `json:"content"`
	Type      MessageType     `json:"type"`
	Metadata  MessageMetadata `json:"metadata"`
	Timestamp time.Time       `json:"timestamp"`
}

// MessageSender defines who sent the message
type MessageSender string

const (
	MessageSenderUser   MessageSender = "user"
	MessageSenderTutor  MessageSender = "tutor"
	MessageSenderSystem MessageSender = "system"
)

// MessageType defines different types of messages
type MessageType string

const (
	MessageTypeText          MessageType = "text"
	MessageTypeQuestion      MessageType = "question"
	MessageTypeExplanation   MessageType = "explanation"
	MessageTypeHint          MessageType = "hint"
	MessageTypeEncouragement MessageType = "encouragement"
	MessageTypeCorrection    MessageType = "correction"
	MessageTypeResource      MessageType = "resource"
	MessageTypeSuggestion    MessageType = "suggestion"
)

// MessageMetadata contains additional message information
type MessageMetadata struct {
	Intent     string            `json:"intent"`
	Confidence float64           `json:"confidence"`
	Entities   []Entity          `json:"entities"`
	Sentiment  string            `json:"sentiment"`
	Difficulty float64           `json:"difficulty"`
	Resources  []Resource        `json:"resources"`
	Actions    []SuggestedAction `json:"actions"`
	Tags       []string          `json:"tags"`
}

// Entity represents extracted entities from messages
type Entity struct {
	Type       string  `json:"type"`
	Value      string  `json:"value"`
	Confidence float64 `json:"confidence"`
	StartPos   int     `json:"start_pos"`
	EndPos     int     `json:"end_pos"`
}

// Resource represents educational resources
type Resource struct {
	Type        ResourceType `json:"type"`
	Title       string       `json:"title"`
	URL         string       `json:"url"`
	Description string       `json:"description"`
	Difficulty  float64      `json:"difficulty"`
	Duration    int          `json:"duration_minutes"`
}

// SuggestedAction represents actions the tutor suggests
type SuggestedAction struct {
	Type        ActionType             `json:"type"`
	Description string                 `json:"description"`
	Priority    int                    `json:"priority"`
	Data        map[string]interface{} `json:"data"`
}

// ActionType defines different types of suggested actions
type ActionType string

const (
	ActionTypePractice    ActionType = "practice"
	ActionTypeReview      ActionType = "review"
	ActionTypeStudy       ActionType = "study"
	ActionTypeBreak       ActionType = "break"
	ActionTypeAssessment  ActionType = "assessment"
	ActionTypeResource    ActionType = "resource"
	ActionTypeGoalSetting ActionType = "goal_setting"
)

// SessionContext provides context for the tutoring session
type SessionContext struct {
	CurrentTopic      *int                   `json:"current_topic,omitempty"`
	RecentPerformance []PerformanceData      `json:"recent_performance"`
	LearningGoals     []string               `json:"learning_goals"`
	Difficulties      []string               `json:"difficulties"`
	Preferences       map[string]interface{} `json:"preferences"`
	PreviousSessions  []uuid.UUID            `json:"previous_sessions"`
}

// PerformanceData represents recent performance information
type PerformanceData struct {
	TopicID    int       `json:"topic_id"`
	Accuracy   float64   `json:"accuracy"`
	Speed      float64   `json:"speed"`
	Difficulty float64   `json:"difficulty"`
	Timestamp  time.Time `json:"timestamp"`
	Mistakes   []string  `json:"mistakes"`
}

// SessionGoal represents a goal for the tutoring session
type SessionGoal struct {
	ID          string  `json:"id"`
	Description string  `json:"description"`
	Type        string  `json:"type"`
	Target      float64 `json:"target"`
	Current     float64 `json:"current"`
	IsAchieved  bool    `json:"is_achieved"`
	Priority    int     `json:"priority"`
}

// SessionOutcomes represents the results of a tutoring session
type SessionOutcomes struct {
	GoalsAchieved     []string          `json:"goals_achieved"`
	ConceptsLearned   []string          `json:"concepts_learned"`
	SkillsImproved    []string          `json:"skills_improved"`
	MistakesCorrected []string          `json:"mistakes_corrected"`
	NextSteps         []string          `json:"next_steps"`
	Satisfaction      float64           `json:"satisfaction"`
	Effectiveness     float64           `json:"effectiveness"`
	Insights          []LearningInsight `json:"insights"`
}

// LearningInsight represents insights gained during the session
type LearningInsight struct {
	Type        InsightType `json:"type"`
	Description string      `json:"description"`
	Confidence  float64     `json:"confidence"`
	Actionable  bool        `json:"actionable"`
	Impact      string      `json:"impact"`
}

// InsightType defines different types of learning insights
type InsightType string

const (
	InsightTypeStrength   InsightType = "strength"
	InsightTypeWeakness   InsightType = "weakness"
	InsightTypePattern    InsightType = "pattern"
	InsightTypeStrategy   InsightType = "strategy"
	InsightTypeMotivation InsightType = "motivation"
	InsightTypeProgress   InsightType = "progress"
)

// TutorFeedback represents feedback about the tutor's performance
type TutorFeedback struct {
	ID          uuid.UUID `json:"id"`
	UserID      uuid.UUID `json:"user_id"`
	TutorID     uuid.UUID `json:"tutor_id"`
	SessionID   uuid.UUID `json:"session_id"`
	Rating      int       `json:"rating"`      // 1-5 scale
	Helpfulness int       `json:"helpfulness"` // 1-5 scale
	Clarity     int       `json:"clarity"`     // 1-5 scale
	Patience    int       `json:"patience"`    // 1-5 scale
	Comments    string    `json:"comments"`
	Suggestions []string  `json:"suggestions"`
	CreatedAt   time.Time `json:"created_at"`
}

// TutorAnalytics represents analytics data for the AI tutor
type TutorAnalytics struct {
	TutorID              uuid.UUID          `json:"tutor_id"`
	Period               string             `json:"period"`
	TotalSessions        int                `json:"total_sessions"`
	AverageSessionLength float64            `json:"average_session_length"`
	SuccessRate          float64            `json:"success_rate"`
	UserSatisfaction     float64            `json:"user_satisfaction"`
	TopTopics            []TopicUsage       `json:"top_topics"`
	CommonQuestions      []QuestionPattern  `json:"common_questions"`
	ImprovementAreas     []ImprovementArea  `json:"improvement_areas"`
	PerformanceMetrics   map[string]float64 `json:"performance_metrics"`
	GeneratedAt          time.Time          `json:"generated_at"`
}

// TopicUsage represents usage statistics for topics
type TopicUsage struct {
	TopicID     int     `json:"topic_id"`
	TopicName   string  `json:"topic_name"`
	Sessions    int     `json:"sessions"`
	SuccessRate float64 `json:"success_rate"`
	Difficulty  float64 `json:"average_difficulty"`
}

// QuestionPattern represents common question patterns
type QuestionPattern struct {
	Pattern     string   `json:"pattern"`
	Frequency   int      `json:"frequency"`
	Category    string   `json:"category"`
	SuccessRate float64  `json:"success_rate"`
	Examples    []string `json:"examples"`
}

// ImprovementArea represents areas where the tutor can improve
type ImprovementArea struct {
	Area         string   `json:"area"`
	CurrentScore float64  `json:"current_score"`
	TargetScore  float64  `json:"target_score"`
	Priority     int      `json:"priority"`
	Suggestions  []string `json:"suggestions"`
}
