package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// InteractiveQuestion represents an enhanced question with multiple interaction types
type InteractiveQuestion struct {
	ID            uuid.UUID        `json:"id"`
	TopicID       int              `json:"topic_id"`
	QuestionType  QuestionType     `json:"question_type"`
	Title         string           `json:"title"`
	Description   string           `json:"description"`
	Content       json.RawMessage  `json:"content"` // Polymorphic content based on question type
	Difficulty    *DifficultyLevel `json:"difficulty"`
	EstimatedTime int              `json:"estimated_time_seconds"`
	MaxAttempts   int              `json:"max_attempts"`
	ScoringRules  ScoringRules     `json:"scoring_rules"`
	Metadata      QuestionMetadata `json:"metadata"`
	AuthorAIModel *string          `json:"author_ai_model"`
	CreatedAt     time.Time        `json:"created_at"`
	UpdatedAt     time.Time        `json:"updated_at"`
}

// ScoringRules defines how the question should be scored
type ScoringRules struct {
	MaxPoints       int     `json:"max_points"`
	PartialCredit   bool    `json:"partial_credit"`
	PenaltyPerWrong float64 `json:"penalty_per_wrong"`
	TimeBonus       bool    `json:"time_bonus"`
	TimeBonusMax    float64 `json:"time_bonus_max"`
}

// QuestionMetadata contains additional information about the question
type QuestionMetadata struct {
	Tags               []string             `json:"tags"`
	LearningObjectives []string             `json:"learning_objectives"`
	Prerequisites      []string             `json:"prerequisites"`
	Accessibility      AccessibilityOptions `json:"accessibility"`
	Analytics          QuestionAnalytics    `json:"analytics"`
}

// AccessibilityOptions defines accessibility features
type AccessibilityOptions struct {
	ScreenReaderText   string   `json:"screen_reader_text"`
	HighContrast       bool     `json:"high_contrast"`
	LargeText          bool     `json:"large_text"`
	AudioDescription   string   `json:"audio_description"`
	KeyboardNav        bool     `json:"keyboard_navigation"`
	AlternativeFormats []string `json:"alternative_formats"`
}

// QuestionAnalytics tracks question performance
type QuestionAnalytics struct {
	TotalAttempts    int       `json:"total_attempts"`
	CorrectAttempts  int       `json:"correct_attempts"`
	AverageTime      float64   `json:"average_time_seconds"`
	DifficultyRating float64   `json:"difficulty_rating"`
	LastCalibrated   time.Time `json:"last_calibrated"`
}

// MultipleChoiceContent represents traditional multiple choice questions
type MultipleChoiceContent struct {
	Question           string         `json:"question"`
	Options            []ChoiceOption `json:"options"`
	CorrectOptionIndex int            `json:"correct_option_index"`
	Explanation        string         `json:"explanation"`
	Randomize          bool           `json:"randomize_options"`
	Media              []MediaElement `json:"media"`
}

// ChoiceOption represents a single choice option
type ChoiceOption struct {
	Text      string         `json:"text"`
	Media     []MediaElement `json:"media"`
	Feedback  string         `json:"feedback"`
	IsCorrect bool           `json:"is_correct"`
}

// DragDropContent represents drag and drop questions
type DragDropContent struct {
	Question     string       `json:"question"`
	Instructions string       `json:"instructions"`
	DragItems    []DragItem   `json:"drag_items"`
	DropZones    []DropZone   `json:"drop_zones"`
	Explanation  string       `json:"explanation"`
	Layout       LayoutConfig `json:"layout"`
}

// DragItem represents an item that can be dragged
type DragItem struct {
	ID       string         `json:"id"`
	Content  string         `json:"content"`
	Media    []MediaElement `json:"media"`
	Category string         `json:"category"`
}

// DropZone represents a zone where items can be dropped
type DropZone struct {
	ID            string   `json:"id"`
	Label         string   `json:"label"`
	AcceptedItems []string `json:"accepted_items"` // IDs of correct drag items
	MaxItems      int      `json:"max_items"`
	Position      Position `json:"position"`
	Feedback      string   `json:"feedback"`
}

// MatchingContent represents matching questions
type MatchingContent struct {
	Question     string      `json:"question"`
	Instructions string      `json:"instructions"`
	LeftColumn   []MatchItem `json:"left_column"`
	RightColumn  []MatchItem `json:"right_column"`
	Matches      []MatchPair `json:"correct_matches"`
	Explanation  string      `json:"explanation"`
	Randomize    bool        `json:"randomize_items"`
}

// MatchItem represents an item in matching questions
type MatchItem struct {
	ID      string         `json:"id"`
	Content string         `json:"content"`
	Media   []MediaElement `json:"media"`
}

// MatchPair represents a correct match
type MatchPair struct {
	LeftID  string `json:"left_id"`
	RightID string `json:"right_id"`
}

// CodeExecutionContent represents programming questions
type CodeExecutionContent struct {
	Question         string     `json:"question"`
	Instructions     string     `json:"instructions"`
	Language         string     `json:"programming_language"`
	StarterCode      string     `json:"starter_code"`
	TestCases        []TestCase `json:"test_cases"`
	ExpectedOutput   string     `json:"expected_output"`
	Explanation      string     `json:"explanation"`
	AllowedLibraries []string   `json:"allowed_libraries"`
	TimeLimit        int        `json:"time_limit_seconds"`
	MemoryLimit      int        `json:"memory_limit_mb"`
}

// TestCase represents a test case for code execution
type TestCase struct {
	Input          string `json:"input"`
	ExpectedOutput string `json:"expected_output"`
	IsHidden       bool   `json:"is_hidden"`
	Points         int    `json:"points"`
}

// MathExpressionContent represents mathematical expression questions
type MathExpressionContent struct {
	Question         string   `json:"question"`
	Instructions     string   `json:"instructions"`
	ExpectedAnswer   string   `json:"expected_answer"`
	AlternateAnswers []string `json:"alternate_answers"`
	Tolerance        float64  `json:"tolerance"`
	Units            string   `json:"units"`
	ShowSteps        bool     `json:"show_steps"`
	Explanation      string   `json:"explanation"`
	MathRenderer     string   `json:"math_renderer"` // "latex", "mathml", "ascii"
}

// SimulationContent represents interactive simulations
type SimulationContent struct {
	Question        string                 `json:"question"`
	Instructions    string                 `json:"instructions"`
	SimulationType  string                 `json:"simulation_type"`
	Parameters      map[string]interface{} `json:"parameters"`
	ExpectedResults []SimulationResult     `json:"expected_results"`
	Explanation     string                 `json:"explanation"`
	InteractionMode InteractionType        `json:"interaction_mode"`
}

// SimulationResult represents expected simulation outcomes
type SimulationResult struct {
	Parameter string      `json:"parameter"`
	Value     interface{} `json:"value"`
	Tolerance float64     `json:"tolerance"`
}

// MultimediaContent represents questions with rich media
type MultimediaContent struct {
	Question      string         `json:"question"`
	Instructions  string         `json:"instructions"`
	MediaElements []MediaElement `json:"media_elements"`
	Interactions  []Interaction  `json:"interactions"`
	Explanation   string         `json:"explanation"`
}

// MediaElement represents a media component
type MediaElement struct {
	ID         string     `json:"id"`
	Type       MediaType  `json:"type"`
	URL        string     `json:"url"`
	Caption    string     `json:"caption"`
	AltText    string     `json:"alt_text"`
	Position   Position   `json:"position"`
	Dimensions Dimensions `json:"dimensions"`
	Autoplay   bool       `json:"autoplay"`
	Controls   bool       `json:"controls"`
}

// Interaction represents an interactive element
type Interaction struct {
	ID        string          `json:"id"`
	Type      InteractionType `json:"type"`
	Position  Position        `json:"position"`
	Target    string          `json:"target"`
	Action    string          `json:"action"`
	Feedback  string          `json:"feedback"`
	IsCorrect bool            `json:"is_correct"`
}

// Position represents 2D coordinates
type Position struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

// Dimensions represents width and height
type Dimensions struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

// LayoutConfig defines layout settings
type LayoutConfig struct {
	Type        string   `json:"type"` // "grid", "free", "linear"
	Columns     int      `json:"columns"`
	Rows        int      `json:"rows"`
	Spacing     int      `json:"spacing"`
	Constraints []string `json:"constraints"`
}

// FillInBlankContent represents fill-in-the-blank questions
type FillInBlankContent struct {
	Question      string       `json:"question"`
	Template      string       `json:"template"` // Text with {{blank}} placeholders
	Blanks        []BlankField `json:"blanks"`
	Explanation   string       `json:"explanation"`
	CaseSensitive bool         `json:"case_sensitive"`
}

// BlankField represents a single blank to fill
type BlankField struct {
	ID              string   `json:"id"`
	CorrectAnswers  []string `json:"correct_answers"`
	Hints           []string `json:"hints"`
	MaxLength       int      `json:"max_length"`
	InputType       string   `json:"input_type"` // "text", "number", "select"
	ValidationRules []string `json:"validation_rules"`
}

// SequencingContent represents questions where items must be ordered
type SequencingContent struct {
	Question     string         `json:"question"`
	Instructions string         `json:"instructions"`
	Items        []SequenceItem `json:"items"`
	CorrectOrder []string       `json:"correct_order"` // IDs in correct sequence
	Explanation  string         `json:"explanation"`
	Randomize    bool           `json:"randomize_initial"`
}

// SequenceItem represents an item to be sequenced
type SequenceItem struct {
	ID      string         `json:"id"`
	Content string         `json:"content"`
	Media   []MediaElement `json:"media"`
	Hint    string         `json:"hint"`
}
