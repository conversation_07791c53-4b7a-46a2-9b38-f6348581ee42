-- <PERSON><PERSON><PERSON> to create sample analytics data for testing Performance Trends
-- This will populate the necessary tables so the analytics page shows data

-- First, let's get the demo user ID
DO $$
DECLARE
    demo_user_id UUID;
    test_id_1 UUID := gen_random_uuid();
    test_id_2 UUID := gen_random_uuid();
    test_id_3 UUID := gen_random_uuid();
    test_id_4 UUID := gen_random_uuid();
    test_id_5 UUID := gen_random_uuid();
    result_id_1 UUID := gen_random_uuid();
    result_id_2 UUID := gen_random_uuid();
    result_id_3 UUID := gen_random_uuid();
    result_id_4 UUID := gen_random_uuid();
    result_id_5 UUID := gen_random_uuid();
BEGIN
    -- Get the demo user ID
    SELECT id INTO demo_user_id FROM users WHERE email = '<EMAIL>';
    
    IF demo_user_id IS NULL THEN
        RAISE EXCEPTION 'Demo user not found. <NAME_EMAIL> user exists.';
    END IF;
    
    -- <PERSON><PERSON> sample completed tests with different dates
    INSERT INTO tests (id, user_id, subject_id, exam_id, num_questions, difficulty_levels, grade, board, status, created_at, completed_at, test_context_subject_id)
    VALUES 
        (test_id_1, demo_user_id, 3, NULL, 5, ARRAY['easy'], '10', 'CBSE', 'completed', NOW() - INTERVAL '7 days', NOW() - INTERVAL '7 days' + INTERVAL '10 minutes', 3),
        (test_id_2, demo_user_id, 3, NULL, 5, ARRAY['medium'], '10', 'CBSE', 'completed', NOW() - INTERVAL '14 days', NOW() - INTERVAL '14 days' + INTERVAL '12 minutes', 3),
        (test_id_3, demo_user_id, 3, NULL, 5, ARRAY['easy', 'medium'], '10', 'CBSE', 'completed', NOW() - INTERVAL '21 days', NOW() - INTERVAL '21 days' + INTERVAL '15 minutes', 3),
        (test_id_4, demo_user_id, 3, NULL, 5, ARRAY['hard'], '10', 'CBSE', 'completed', NOW() - INTERVAL '28 days', NOW() - INTERVAL '28 days' + INTERVAL '18 minutes', 3),
        (test_id_5, demo_user_id, 3, NULL, 5, ARRAY['easy', 'medium'], '10', 'CBSE', 'completed', NOW() - INTERVAL '35 days', NOW() - INTERVAL '35 days' + INTERVAL '8 minutes', 3);
    
    -- Create corresponding test results with varying scores
    INSERT INTO test_results (id, test_id, user_id, score, total_questions, correct_answers, incorrect_answers, time_taken_seconds)
    VALUES 
        (result_id_1, test_id_1, demo_user_id, 85.0, 5, 4, 1, 600),  -- 85% score, 10 minutes
        (result_id_2, test_id_2, demo_user_id, 70.0, 5, 3, 2, 720),  -- 70% score, 12 minutes
        (result_id_3, test_id_3, demo_user_id, 90.0, 5, 4, 1, 900),  -- 90% score, 15 minutes
        (result_id_4, test_id_4, demo_user_id, 60.0, 5, 3, 2, 1080), -- 60% score, 18 minutes
        (result_id_5, test_id_5, demo_user_id, 95.0, 5, 5, 0, 480);  -- 95% score, 8 minutes
    
    -- Create topic performance summary data
    -- Get topic IDs for Mathematics (subject_id = 3)
    INSERT INTO topic_performance_summary (id, user_id, topic_id, total_attempted, total_correct, proficiency_score, last_tested_at, created_at, updated_at)
    SELECT 
        gen_random_uuid(),
        demo_user_id,
        t.id,
        CASE 
            WHEN t.id = 29 THEN 8  -- Algebra
            WHEN t.id = 30 THEN 6  -- Geometry  
            WHEN t.id = 31 THEN 7  -- Trigonometry
            WHEN t.id = 32 THEN 5  -- Calculus
            WHEN t.id = 33 THEN 9  -- Statistics
        END as total_attempted,
        CASE 
            WHEN t.id = 29 THEN 6  -- Algebra: 75%
            WHEN t.id = 30 THEN 4  -- Geometry: 67%
            WHEN t.id = 31 THEN 6  -- Trigonometry: 86%
            WHEN t.id = 32 THEN 3  -- Calculus: 60%
            WHEN t.id = 33 THEN 8  -- Statistics: 89%
        END as total_correct,
        CASE 
            WHEN t.id = 29 THEN 75.0  -- Algebra
            WHEN t.id = 30 THEN 67.0  -- Geometry
            WHEN t.id = 31 THEN 86.0  -- Trigonometry
            WHEN t.id = 32 THEN 60.0  -- Calculus
            WHEN t.id = 33 THEN 89.0  -- Statistics
        END as proficiency_score,
        NOW() - INTERVAL '1 day',
        NOW(),
        NOW()
    FROM topics t 
    WHERE t.subject_id = 3 AND t.id IN (29, 30, 31, 32, 33);
    
    RAISE NOTICE 'Sample analytics data created successfully for user: %', demo_user_id;
    RAISE NOTICE 'Created 5 completed tests with results';
    RAISE NOTICE 'Created topic performance summaries for 5 mathematics topics';
    
END $$;

-- Verify the data was created
SELECT 'Tests created:' as info, COUNT(*) as count FROM tests WHERE status = 'completed';
SELECT 'Test results created:' as info, COUNT(*) as count FROM test_results;
SELECT 'Topic performances created:' as info, COUNT(*) as count FROM topic_performance_summary;

-- Show sample data
SELECT 
    'Sample test results:' as info,
    tr.score,
    tr.total_questions,
    tr.correct_answers,
    t.created_at::date as test_date
FROM test_results tr
JOIN tests t ON tr.test_id = t.id
ORDER BY t.created_at DESC
LIMIT 5;
