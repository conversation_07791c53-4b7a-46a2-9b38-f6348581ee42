<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.ReportMetadata.Title}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header .subtitle {
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .section {
            background: white;
            margin-bottom: 30px;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        .topic-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .topic-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .topic-item.weak {
            border-left-color: #dc3545;
        }
        .topic-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .topic-score {
            font-size: 1.2em;
            font-weight: bold;
        }
        .recommendations {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        .recommendations ul {
            margin: 0;
            padding-left: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
        }
        @media print {
            body { background-color: white; }
            .section { box-shadow: none; border: 1px solid #ddd; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{.ReportMetadata.Title}}</h1>
        <div class="subtitle">
            Generated for {{.UserInfo.Name}} on {{.ReportMetadata.GeneratedAt.Format "January 2, 2006"}}
        </div>
    </div>

    <!-- Performance Overview -->
    <div class="section">
        <h2>Performance Overview</h2>
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">{{printf "%.1f%%" .PerformanceMetrics.OverallScore}}</div>
                <div class="metric-label">Overall Score</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{.PerformanceMetrics.TestsCompleted}}</div>
                <div class="metric-label">Tests Completed</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{printf "%.1f%%" .PerformanceMetrics.ImprovementRate}}</div>
                <div class="metric-label">Improvement Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{printf "%.1fh" .PerformanceMetrics.StudyTimeHours}}</div>
                <div class="metric-label">Study Time</div>
            </div>
        </div>
    </div>

    <!-- Strengths and Weaknesses -->
    <div class="section">
        <h2>Topic Performance Analysis</h2>
        <div class="topic-list">
            {{range .TopicAnalysis}}
            <div class="topic-item {{if lt .ProficiencyScore 70.0}}weak{{end}}">
                <div class="topic-name">{{.TopicName}}</div>
                <div class="topic-score">{{printf "%.1f%%" .ProficiencyScore}}</div>
                <div style="font-size: 0.9em; color: #666;">{{.SubjectName}}</div>
            </div>
            {{end}}
        </div>
    </div>

    <!-- Recommendations -->
    {{if .Recommendations}}
    <div class="section">
        <h2>Personalized Recommendations</h2>
        <div class="recommendations">
            <ul>
                {{range .Recommendations}}
                <li><strong>{{.Title}}:</strong> {{.Description}}</li>
                {{end}}
            </ul>
        </div>
    </div>
    {{end}}

    <!-- Study Patterns -->
    {{if .StudyPatterns}}
    <div class="section">
        <h2>Study Behavior Analysis</h2>
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">{{.StudyPatterns.WeeklyTestCount}}</div>
                <div class="metric-label">Tests per Week</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{.StudyPatterns.AverageSessionTime}}min</div>
                <div class="metric-label">Avg Session Time</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{.StudyPatterns.BestPerformanceDay}}</div>
                <div class="metric-label">Best Study Day</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{printf "%.0f%%" .StudyPatterns.ConsistencyScore}}</div>
                <div class="metric-label">Consistency Score</div>
            </div>
        </div>
    </div>
    {{end}}

    <div class="footer">
        <p>Report generated by Test-Spark Analytics Engine</p>
        <p>{{.ReportMetadata.GeneratedAt.Format "Monday, January 2, 2006 at 3:04 PM"}}</p>
    </div>
</body>
</html>
