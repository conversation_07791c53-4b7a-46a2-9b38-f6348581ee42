# CI/CD Pipeline for Test-Spark Backend
# This workflow provides automated testing, building, and deployment
# for the Test-Spark backend application with comprehensive checks.

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  GO_VERSION: '1.21'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Cache Go modules
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Download dependencies
        run: go mod download

      - name: Verify dependencies
        run: go mod verify

      - name: Run go vet
        run: go vet ./...

      - name: Run staticcheck
        uses: dominikh/staticcheck-action@v1.3.0
        with:
          version: "2023.1.6"

      - name: Run gosec security scanner
        uses: securecodewarrior/github-action-gosec@master
        with:
          args: './...'

      - name: Check formatting
        run: |
          if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
            echo "The following files are not formatted:"
            gofmt -s -l .
            exit 1
          fi

  # Unit and Integration Tests
  test:
    name: Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Cache Go modules
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Download dependencies
        run: go mod download

      - name: Set up test environment
        env:
          DATABASE_URL: postgres://testuser:testpassword@localhost:5432/testdb?sslmode=disable
          JWT_SECRET: test-secret-key-for-ci
          GROQ_API_KEY: test-groq-key
          REDIS_ADDR: localhost:6379
          USE_REDIS: true
          LOG_LEVEL: debug
          LOG_FORMAT: json
        run: |
          # Run database migrations if they exist
          if [ -d "migrations" ]; then
            echo "Running database migrations..."
            # Add migration command here when available
          fi

      - name: Run unit tests
        env:
          DATABASE_URL: postgres://testuser:testpassword@localhost:5432/testdb?sslmode=disable
          JWT_SECRET: test-secret-key-for-ci
          GROQ_API_KEY: test-groq-key
          REDIS_ADDR: localhost:6379
          USE_REDIS: true
          LOG_LEVEL: debug
          LOG_FORMAT: json
        run: |
          go test -v -race -coverprofile=coverage.out ./...

      - name: Run integration tests
        env:
          DATABASE_URL: postgres://testuser:testpassword@localhost:5432/testdb?sslmode=disable
          JWT_SECRET: test-secret-key-for-ci
          GROQ_API_KEY: test-groq-key
          REDIS_ADDR: localhost:6379
          USE_REDIS: true
          LOG_LEVEL: debug
          LOG_FORMAT: json
        run: |
          # Run integration tests with longer timeout
          go test -v -race -timeout=10m -tags=integration ./...

      - name: Generate coverage report
        run: |
          go tool cover -html=coverage.out -o coverage.html
          go tool cover -func=coverage.out

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.out
          flags: unittests
          name: codecov-umbrella

      - name: Upload coverage artifacts
        uses: actions/upload-artifact@v3
        with:
          name: coverage-report
          path: |
            coverage.out
            coverage.html

  # Build Application
  build:
    name: Build
    runs-on: ubuntu-latest
    needs: test
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Cache Go modules
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Download dependencies
        run: go mod download

      - name: Build application
        run: |
          CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/api/
          
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: test-spark-backend
          path: main

  # Docker Build and Push
  docker:
    name: Docker Build & Push
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Deployment (placeholder for future implementation)
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: [build, docker]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to production
        run: |
          echo "Deployment step - implement based on your infrastructure"
          echo "This could include:"
          echo "- Kubernetes deployment"
          echo "- Docker Compose update"
          echo "- Cloud provider deployment"
          echo "- Server restart with new image"

  # Notification
  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [code-quality, test, build, docker]
    if: always()
    
    steps:
      - name: Notify on success
        if: ${{ needs.code-quality.result == 'success' && needs.test.result == 'success' && needs.build.result == 'success' }}
        run: |
          echo "✅ CI/CD Pipeline completed successfully!"
          echo "All checks passed, build successful, and Docker image pushed."

      - name: Notify on failure
        if: ${{ needs.code-quality.result == 'failure' || needs.test.result == 'failure' || needs.build.result == 'failure' }}
        run: |
          echo "❌ CI/CD Pipeline failed!"
          echo "Check the logs for details on what went wrong."
