# =============================================================================
# TEST-SPARK FRONTEND CONFIGURATION
# =============================================================================
# Copy this file to .env and configure your settings
# All variables must be prefixed with VITE_ to be accessible in the browser

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Backend API base URL
VITE_API_BASE_URL=http://localhost:8080

# API timeout in milliseconds
VITE_API_TIMEOUT=30000

# API retry configuration
VITE_API_MAX_RETRIES=3
VITE_API_RETRY_DELAY=1000

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Application environment
VITE_NODE_ENV=development

# Application name and version
VITE_APP_NAME=Test-Spark
VITE_APP_VERSION=1.0.0

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable debug mode (shows additional logging and debug info)
VITE_DEBUG=false

# Enable performance monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Enable error boundary logging
VITE_ENABLE_ERROR_LOGGING=true

# =============================================================================
# UI/UX CONFIGURATION
# =============================================================================

# Default theme (light, dark, system)
VITE_DEFAULT_THEME=system

# Enable theme persistence
VITE_PERSIST_THEME=true

# Animation settings
VITE_ENABLE_ANIMATIONS=true
VITE_ANIMATION_DURATION=300

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable analytics features
VITE_ENABLE_ANALYTICS=true

# Enable report generation
VITE_ENABLE_REPORTS=true

# Enable AI tutor features
VITE_ENABLE_AI_TUTOR=true

# Enable gamification features
VITE_ENABLE_GAMIFICATION=true

# Enable accessibility features
VITE_ENABLE_ACCESSIBILITY=true

# =============================================================================
# ANALYTICS CONFIGURATION
# =============================================================================

# Chart refresh interval (milliseconds)
VITE_CHART_REFRESH_INTERVAL=30000

# Data cache duration (milliseconds)
VITE_DATA_CACHE_DURATION=300000

# Maximum data points for charts
VITE_MAX_CHART_DATA_POINTS=100

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# Enable test mode (for automated testing)
VITE_TEST_MODE=false

# Mock API responses in test mode
VITE_MOCK_API=false

# Test user credentials (for development only)
VITE_TEST_USER_EMAIL=<EMAIL>
VITE_TEST_USER_PASSWORD=demo1234

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Token storage method (localStorage, sessionStorage, memory)
VITE_TOKEN_STORAGE=localStorage

# Auto-logout timeout (milliseconds, 0 to disable)
VITE_AUTO_LOGOUT_TIMEOUT=3600000

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Enable service worker for caching
VITE_ENABLE_SERVICE_WORKER=false

# Image optimization settings
VITE_ENABLE_IMAGE_OPTIMIZATION=true
VITE_IMAGE_QUALITY=80

# Lazy loading settings
VITE_ENABLE_LAZY_LOADING=true

# =============================================================================
# ENVIRONMENT-SPECIFIC EXAMPLES
# =============================================================================

# Development:
# VITE_API_BASE_URL=http://localhost:8080
# VITE_DEBUG=true
# VITE_ENABLE_PERFORMANCE_MONITORING=true

# Staging:
# VITE_API_BASE_URL=https://staging-api.test-spark.com
# VITE_DEBUG=false
# VITE_ENABLE_PERFORMANCE_MONITORING=true

# Production:
# VITE_API_BASE_URL=https://api.test-spark.com
# VITE_DEBUG=false
# VITE_ENABLE_PERFORMANCE_MONITORING=false
# VITE_AUTO_LOGOUT_TIMEOUT=1800000
