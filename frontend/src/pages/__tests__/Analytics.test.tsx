import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import Analytics from '../Analytics';

// Mock the hooks and dependencies
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

vi.mock('@/hooks/useAnalyticsData', () => ({
  useAnalyticsData: vi.fn(() => ({
    dashboard: {
      total_tests_taken: 25,
      average_score: 78.5,
      overall_proficiency: 82.3,
      topic_performance: [
        {
          topic_id: 44,
          topic_name: 'Verbal Reasoning',
          subject_name: 'Logical Reasoning',
          proficiency_score: 100,
        },
        {
          topic_id: 29,
          topic_name: 'Algebra',
          subject_name: 'Mathematics',
          proficiency_score: 48.57,
        },
        {
          topic_id: 34,
          topic_name: 'Physics',
          subject_name: 'Science',
          proficiency_score: 85,
        },
      ],
    },
    insights: [
      {
        type: 'strength',
        title: 'Strong in Verbal Reasoning',
        description: 'You\'re excelling in Verbal Reasoning with 100% proficiency. Keep up the great work!',
        action: 'Continue practicing Verbal Reasoning',
        priority: 1,
      },
      {
        type: 'achievement',
        title: 'Science Mastery',
        description: 'Excellent performance in Science subjects with 85% average score!',
        action: 'Maintain your strong performance',
        priority: 2,
      },
      {
        type: 'concern',
        title: 'Algebra Needs Attention',
        description: 'Your proficiency in Algebra is 48.57%. Focus on this area to improve your overall performance.',
        action: 'Practice Algebra',
        priority: 5,
      },
    ],
    performanceTrends: [],
    studyPatterns: null,
    recommendations: [],
    heatmapData: [],
    timeFilter: 'month',
    isLoading: false,
    isLoadingInsights: false,
    hasError: false,
    error: null,
    updateTimeFilter: vi.fn(),
    refreshData: vi.fn(),
  })),
}));

vi.mock('@/lib/api', () => ({
  useAuth: vi.fn(() => ({
    isAuthenticated: true,
  })),
}));

vi.mock('@/lib/analytics-transforms', () => ({
  transformInsights: vi.fn((insights) => insights.map(insight => ({
    type: insight.type,
    title: insight.title,
    description: insight.description,
    action: insight.action,
    icon: insight.type === 'strength' ? 'TrendingUp' : 
          insight.type === 'achievement' ? 'Award' : 
          insight.type === 'concern' ? 'TrendingDown' : 'Target',
    color: insight.type === 'strength' ? 'text-success' : 
           insight.type === 'achievement' ? 'text-warning' : 
           insight.type === 'concern' ? 'text-destructive' : 'text-info',
    priority: insight.priority,
  }))),
  transformPerformanceTrends: vi.fn(() => []),
  transformSubjectComparisons: vi.fn(() => []),
  transformHeatmapData: vi.fn(() => []),
  transformStudyPatterns: vi.fn(() => null),
  transformRecommendations: vi.fn(() => []),
  formatImprovement: vi.fn(() => '+5%'),
  generateFallbackData: vi.fn(() => ({
    performanceChart: [],
    radarChart: [],
    heatmapChart: [],
  })),
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('Analytics Page', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders analytics page with insights', async () => {
    renderWithRouter(<Analytics />);
    
    await waitFor(() => {
      expect(screen.getByText('Strong in Verbal Reasoning')).toBeInTheDocument();
      expect(screen.getByText('Science Mastery')).toBeInTheDocument();
      expect(screen.getByText('Algebra Needs Attention')).toBeInTheDocument();
    });
  });

  it('displays insight action buttons', async () => {
    renderWithRouter(<Analytics />);
    
    await waitFor(() => {
      expect(screen.getByText('Continue practicing Verbal Reasoning')).toBeInTheDocument();
      expect(screen.getByText('Maintain your strong performance')).toBeInTheDocument();
      expect(screen.getByText('Practice Algebra')).toBeInTheDocument();
    });
  });

  it('handles insight button clicks correctly', async () => {
    renderWithRouter(<Analytics />);
    
    await waitFor(() => {
      const verbalReasoningButton = screen.getByText('Continue practicing Verbal Reasoning');
      expect(verbalReasoningButton).toBeInTheDocument();
    });

    const verbalReasoningButton = screen.getByText('Continue practicing Verbal Reasoning');
    fireEvent.click(verbalReasoningButton);

    expect(mockNavigate).toHaveBeenCalledWith('/tests/practice?topic=Verbal%20Reasoning');
  });

  it('handles algebra needs attention button click', async () => {
    renderWithRouter(<Analytics />);
    
    await waitFor(() => {
      const algebraButton = screen.getByText('Practice Algebra');
      expect(algebraButton).toBeInTheDocument();
    });

    const algebraButton = screen.getByText('Practice Algebra');
    fireEvent.click(algebraButton);

    expect(mockNavigate).toHaveBeenCalledWith('/tests/practice?topic=Algebra');
  });

  it('handles science mastery button click', async () => {
    renderWithRouter(<Analytics />);
    
    await waitFor(() => {
      const scienceButton = screen.getByText('Maintain your strong performance');
      expect(scienceButton).toBeInTheDocument();
    });

    const scienceButton = screen.getByText('Maintain your strong performance');
    fireEvent.click(scienceButton);

    expect(mockNavigate).toHaveBeenCalledWith('/tests/practice');
  });

  it('displays practice now buttons in weaknesses section', async () => {
    renderWithRouter(<Analytics />);
    
    // Switch to weaknesses tab
    const weaknessesTab = screen.getByText('Areas for Improvement');
    fireEvent.click(weaknessesTab);

    await waitFor(() => {
      const practiceButtons = screen.getAllByText('Practice Now');
      expect(practiceButtons.length).toBeGreaterThan(0);
    });
  });

  it('shows refresh button and handles refresh', async () => {
    const mockRefreshData = vi.fn();
    vi.mocked(require('@/hooks/useAnalyticsData').useAnalyticsData).mockReturnValue({
      dashboard: { total_tests_taken: 25 },
      insights: [],
      isLoading: false,
      refreshData: mockRefreshData,
    });

    renderWithRouter(<Analytics />);
    
    const refreshButton = screen.getByText('Refresh');
    expect(refreshButton).toBeInTheDocument();
    
    fireEvent.click(refreshButton);
    expect(mockRefreshData).toHaveBeenCalled();
  });

  it('displays loading state correctly', async () => {
    vi.mocked(require('@/hooks/useAnalyticsData').useAnalyticsData).mockReturnValue({
      dashboard: null,
      insights: null,
      isLoading: true,
      isLoadingInsights: true,
      refreshData: vi.fn(),
    });

    renderWithRouter(<Analytics />);
    
    expect(screen.getByText('Updating...')).toBeInTheDocument();
  });

  it('handles error states gracefully', async () => {
    vi.mocked(require('@/hooks/useAnalyticsData').useAnalyticsData).mockReturnValue({
      dashboard: null,
      insights: null,
      isLoading: false,
      hasError: true,
      error: 'Failed to load analytics data',
      refreshData: vi.fn(),
    });

    renderWithRouter(<Analytics />);
    
    expect(screen.getByText(/Failed to load analytics data/)).toBeInTheDocument();
  });

  it('shows fallback message when no insights available', async () => {
    vi.mocked(require('@/hooks/useAnalyticsData').useAnalyticsData).mockReturnValue({
      dashboard: { total_tests_taken: 0 },
      insights: [],
      isLoading: false,
      isLoadingInsights: false,
      refreshData: vi.fn(),
    });

    renderWithRouter(<Analytics />);
    
    await waitFor(() => {
      expect(screen.getByText('No insights available yet')).toBeInTheDocument();
    });
  });
});
