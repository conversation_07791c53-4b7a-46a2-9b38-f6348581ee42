import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Navbar } from "@/components/layout/Navbar";
import { useToast } from "@/hooks/use-toast";
import {
  Target,
  PlayCircle,
  Settings,
  Loader2,
  BookOpen,
  Brain,
  TrendingUp,
  ArrowLeft
} from "lucide-react";
import { useContent, useTests, useAuth, Subject, Exam, DifficultyLevel } from "@/lib/api";

export default function PracticeTest() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const { getSubjects, getExams, getTopicsBySubject } = useContent();
  const { createTest } = useTests();
  const { getCurrentUser } = useAuth();

  // Get URL parameters for pre-selected topics or subjects
  const topicsParam = searchParams.get('topics');
  const topicParam = searchParams.get('topic');
  const subjectParam = searchParams.get('subject');

  // State
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [exams, setExams] = useState<Exam[]>([]);
  const [topics, setTopics] = useState<any[]>([]);
  const [selectedSubject, setSelectedSubject] = useState<number | undefined>();
  const [selectedExam, setSelectedExam] = useState<number | undefined>();
  const [selectedTopics, setSelectedTopics] = useState<number[]>([]);
  const [difficulty, setDifficulty] = useState<DifficultyLevel>("medium");
  const [numQuestions, setNumQuestions] = useState("10");
  const [isLoading, setIsLoading] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [user, setUser] = useState<{
    name: string;
    email: string;
  } | null>(null);
  const [userLoading, setUserLoading] = useState(true);

  // Fetch current user data
  useEffect(() => {
    const fetchUser = async () => {
      if (getCurrentUser) {
        try {
          const userData = await getCurrentUser();
          setUser({
            name: userData.full_name || userData.email.split('@')[0],
            email: userData.email
          });
        } catch (error) {
          console.error('Failed to fetch user data:', error);
          // Fallback to basic user info if available
          setUser({
            name: "User",
            email: "<EMAIL>"
          });
        }
      } else {
        // Fallback if getCurrentUser is not available
        setUser({
          name: "Guest",
          email: "<EMAIL>"
        });
      }
      setUserLoading(false);
    };

    fetchUser();
  }, [getCurrentUser]);

  // Load subjects and exams on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const [subjectsData, examsData] = await Promise.all([
          getSubjects(),
          getExams()
        ]);
        setSubjects(Array.isArray(subjectsData) ? subjectsData : []);
        setExams(Array.isArray(examsData) ? examsData : []);

        // Pre-select subject if provided in URL
        if (subjectParam) {
          const subject = subjectsData?.find(s => s.name.toLowerCase() === subjectParam.toLowerCase());
          if (subject) {
            setSelectedSubject(subject.id);
          }
        }
      } catch (error) {
        console.error('Failed to load subjects and exams:', error);
        setSubjects([]);
        setExams([]);
        toast({
          title: "Failed to load data",
          description: "Please refresh the page to try again.",
          variant: "destructive",
        });
      } finally {
        setIsDataLoading(false);
      }
    };

    loadData();
  }, [getSubjects, getExams, subjectParam, toast]);

  // Helper function to find subject for a topic
  const findSubjectForTopic = async (topicName: string) => {
    // Try each subject to find the one containing the topic
    for (const subject of subjects) {
      try {
        const topicsData = await getTopicsBySubject(subject.id);
        const matchingTopic = topicsData?.find(topic =>
          topic.name.toLowerCase() === topicName.toLowerCase()
        );
        if (matchingTopic) {
          return { subject, topic: matchingTopic };
        }
      } catch (error) {
        // Continue to next subject if this one fails
        continue;
      }
    }
    return null;
  };

  // Auto-select subject and topic if topic is provided in URL without subject
  useEffect(() => {
    const autoSelectFromTopic = async () => {
      if (topicParam && !subjectParam && subjects.length > 0 && !selectedSubject) {
        const result = await findSubjectForTopic(topicParam);
        if (result) {
          setSelectedSubject(result.subject.id);
          // The topic will be selected in the next useEffect when topics load
        }
      }
    };

    autoSelectFromTopic();
  }, [topicParam, subjectParam, subjects, selectedSubject, getTopicsBySubject]);

  // Load topics when subject is selected
  useEffect(() => {
    const loadTopics = async () => {
      if (!selectedSubject) {
        setTopics([]);
        return;
      }

      try {
        const topicsData = await getTopicsBySubject(selectedSubject);
        setTopics(Array.isArray(topicsData) ? topicsData : []);

        // Pre-select topics if provided in URL
        if (topicsParam) {
          const topicNames = topicsParam.split(',').map(t => t.trim().toLowerCase());
          const matchingTopics = topicsData?.filter(topic => 
            topicNames.includes(topic.name.toLowerCase())
          ) || [];
          setSelectedTopics(matchingTopics.map(t => t.id));
        } else if (topicParam) {
          const matchingTopic = topicsData?.find(topic => 
            topic.name.toLowerCase() === topicParam.toLowerCase()
          );
          if (matchingTopic) {
            setSelectedTopics([matchingTopic.id]);
          }
        }
      } catch (error) {
        console.error('Failed to load topics:', error);
        setTopics([]);
        toast({
          title: "Failed to load topics",
          description: "Please try selecting the subject again.",
          variant: "destructive",
        });
      }
    };

    loadTopics();
  }, [selectedSubject, getTopicsBySubject, topicsParam, topicParam, toast]);

  const handleTopicToggle = (topicId: number) => {
    setSelectedTopics(prev => 
      prev.includes(topicId) 
        ? prev.filter(id => id !== topicId)
        : [...prev, topicId]
    );
  };

  const getSelectedSubjectName = () => {
    return subjects.find(s => s.id === selectedSubject)?.name || "Unknown Subject";
  };

  const getSelectedExamName = () => {
    return exams.find(e => e.id === selectedExam)?.name || "General Practice";
  };

  const handleStartPractice = async () => {
    if (!selectedSubject && selectedTopics.length === 0) {
      toast({
        title: "Selection Required",
        description: "Please select a subject or specific topics to practice.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    toast({
      title: "Creating Practice Test...",
      description: "Generating personalized questions for your practice session.",
      duration: 5000,
    });

    try {
      const questions = parseInt(numQuestions) || 10;

      const result = await createTest(
        selectedSubject,
        selectedExam,
        questions,
        [difficulty],
        selectedTopics.length > 0 ? selectedTopics : undefined
      );

      if (result.success) {
        toast({
          title: "Practice Test Ready! 🎯",
          description: `${questions} questions generated for focused practice. Let's improve your skills!`,
        });

        // Navigate with practice context information
        const practiceContext = {
          subject: getSelectedSubjectName(),
          exam: getSelectedExamName(),
          difficulty: difficulty,
          numQuestions: questions,
          isPractice: true,
          selectedTopics: selectedTopics.length > 0 ? selectedTopics : undefined
        };
        navigate(`/tests/${result.data.test.id}`, { state: { testContext: practiceContext } });
      } else {
        toast({
          title: "Failed to Create Practice Test",
          description: result.error || "Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isDataLoading || userLoading || !user) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar isAuthenticated={true} user={user} />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">
                {userLoading ? "Loading user profile..." : "Loading practice options..."}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar isAuthenticated={true} user={user} />
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => navigate('/dashboard')}
              className="text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>
          <div className="flex items-center gap-3 mb-2">
            <Target className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold">Practice Test</h1>
          </div>
          <p className="text-muted-foreground text-lg">
            Focus on specific topics and improve your weak areas with targeted practice
          </p>
          {topicParam && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <Target className="h-4 w-4 inline mr-1" />
                Recommended practice for: <strong>{topicParam}</strong>
              </p>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Configuration Panel */}
          <div className="lg:col-span-2 space-y-6">
            {/* Subject Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Subject & Exam
                </CardTitle>
                <CardDescription>
                  Choose the subject and exam context for your practice
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Subject</label>
                    <Select 
                      value={selectedSubject?.toString() || ""} 
                      onValueChange={(value) => setSelectedSubject(parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a subject" />
                      </SelectTrigger>
                      <SelectContent>
                        {subjects.map((subject) => (
                          <SelectItem key={subject.id} value={subject.id.toString()}>
                            {subject.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Exam (Optional)</label>
                    <Select
                      value={selectedExam?.toString() || "general"}
                      onValueChange={(value) => setSelectedExam(value === "general" ? undefined : parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select an exam" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="general">General Practice</SelectItem>
                        {exams.map((exam) => (
                          <SelectItem key={exam.id} value={exam.id.toString()}>
                            {exam.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Topic Selection */}
            {selectedSubject && topics.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5" />
                    Focus Topics
                  </CardTitle>
                  <CardDescription>
                    Select specific topics to practice (leave empty for all topics)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {topics.map((topic) => (
                      <Button
                        key={topic.id}
                        variant={selectedTopics.includes(topic.id) ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleTopicToggle(topic.id)}
                        className="justify-start text-left h-auto py-2 px-3"
                      >
                        {topic.name}
                      </Button>
                    ))}
                  </div>
                  {selectedTopics.length > 0 && (
                    <div className="mt-4 p-3 bg-muted rounded-lg">
                      <p className="text-sm font-medium mb-2">Selected Topics:</p>
                      <div className="flex flex-wrap gap-1">
                        {selectedTopics.map(topicId => {
                          const topic = topics.find(t => t.id === topicId);
                          return topic ? (
                            <Badge key={topicId} variant="secondary" className="text-xs">
                              {topic.name}
                            </Badge>
                          ) : null;
                        })}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Test Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Practice Settings
                </CardTitle>
                <CardDescription>
                  Configure your practice session parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Number of Questions</label>
                    <Select value={numQuestions} onValueChange={setNumQuestions}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5 Questions (Quick Practice)</SelectItem>
                        <SelectItem value="10">10 Questions (Standard)</SelectItem>
                        <SelectItem value="15">15 Questions (Extended)</SelectItem>
                        <SelectItem value="20">20 Questions (Comprehensive)</SelectItem>
                        <SelectItem value="25">25 Questions (Full Practice)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Difficulty Level</label>
                    <Select value={difficulty} onValueChange={(value: DifficultyLevel) => setDifficulty(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="easy">Easy - Build Confidence</SelectItem>
                        <SelectItem value="medium">Medium - Balanced Challenge</SelectItem>
                        <SelectItem value="hard">Hard - Push Your Limits</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Practice Summary & Start */}
          <div className="space-y-6">
            <Card className="border-primary/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-primary" />
                  Practice Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Subject:</span>
                    <span className="font-medium">
                      {selectedSubject ? getSelectedSubjectName() : "Not selected"}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Exam Context:</span>
                    <span className="font-medium">{getSelectedExamName()}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Questions:</span>
                    <span className="font-medium">{numQuestions}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Difficulty:</span>
                    <Badge variant={
                      difficulty === 'easy' ? 'secondary' :
                      difficulty === 'medium' ? 'default' : 'destructive'
                    }>
                      {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
                    </Badge>
                  </div>

                  {selectedTopics.length > 0 && (
                    <div className="pt-2 border-t">
                      <span className="text-sm text-muted-foreground block mb-2">Focus Topics:</span>
                      <div className="flex flex-wrap gap-1">
                        {selectedTopics.slice(0, 3).map(topicId => {
                          const topic = topics.find(t => t.id === topicId);
                          return topic ? (
                            <Badge key={topicId} variant="outline" className="text-xs">
                              {topic.name}
                            </Badge>
                          ) : null;
                        })}
                        {selectedTopics.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{selectedTopics.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <Button
                  onClick={handleStartPractice}
                  disabled={isLoading || (!selectedSubject && selectedTopics.length === 0)}
                  className="w-full btn-gradient h-12 text-lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                      Creating Practice Test...
                    </>
                  ) : (
                    <>
                      <PlayCircle className="h-5 w-5 mr-2" />
                      Start Practice Session
                    </>
                  )}
                </Button>

                {(!selectedSubject && selectedTopics.length === 0) && (
                  <p className="text-sm text-muted-foreground text-center">
                    Please select a subject or topics to start practicing
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Practice Tips */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Practice Tips</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-3">
                  <Target className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium">Focus on Weak Areas</p>
                    <p className="text-xs text-muted-foreground">
                      Practice topics where you scored lower in previous tests
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Brain className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium">Regular Practice</p>
                    <p className="text-xs text-muted-foreground">
                      Consistent daily practice leads to better retention
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <TrendingUp className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium">Track Progress</p>
                    <p className="text-xs text-muted-foreground">
                      Review your analytics to see improvement over time
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
