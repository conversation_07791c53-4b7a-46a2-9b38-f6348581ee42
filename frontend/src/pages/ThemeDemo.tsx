import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ThemeToggle, SimpleThemeToggle, FloatingThemeToggle, useTheme } from '@/components/theme';
import { Sun, Moon, Monitor, Palette, Sparkles } from 'lucide-react';

export default function ThemeDemo() {
  const { theme } = useTheme();

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Floating Theme Toggle for Demo */}
      <FloatingThemeToggle position="top-right" />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Palette className="h-8 w-8 text-primary mr-3" />
            <h1 className="text-4xl font-bold font-poppins bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Theme System Demo
            </h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Experience Test-Spark's beautiful dark and light themes with smooth transitions and educational-focused design.
          </p>
          <Badge variant="secondary" className="mt-4">
            Current Theme: {theme}
          </Badge>
        </div>

        {/* Theme Controls */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Sparkles className="h-5 w-5 mr-2 text-primary" />
              Theme Controls
            </CardTitle>
            <CardDescription>
              Try different theme toggle components
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex flex-col items-center space-y-2">
                <ThemeToggle />
                <span className="text-sm text-muted-foreground">Dropdown Toggle</span>
              </div>
              <div className="flex flex-col items-center space-y-2">
                <SimpleThemeToggle />
                <span className="text-sm text-muted-foreground">Simple Toggle</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Color Palette */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Color Palette</CardTitle>
            <CardDescription>
              Educational color scheme that adapts to both themes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <div className="h-16 bg-primary rounded-lg border border-border"></div>
                <p className="text-sm font-medium">Primary</p>
                <p className="text-xs text-muted-foreground">Educational Blue</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-secondary rounded-lg border border-border"></div>
                <p className="text-sm font-medium">Secondary</p>
                <p className="text-xs text-muted-foreground">Academic Purple</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-success rounded-lg border border-border"></div>
                <p className="text-sm font-medium">Success</p>
                <p className="text-xs text-muted-foreground">Progress Green</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-warning rounded-lg border border-border"></div>
                <p className="text-sm font-medium">Warning</p>
                <p className="text-xs text-muted-foreground">Attention Orange</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Component Examples */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <Card className="card-gradient">
            <CardHeader>
              <CardTitle>Interactive Elements</CardTitle>
              <CardDescription>
                Buttons and interactive components
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Button variant="default">Primary Button</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="ghost">Ghost</Button>
              </div>
              <div className="flex flex-wrap gap-2">
                <Badge>Default Badge</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="outline">Outline</Badge>
                <Badge variant="destructive">Destructive</Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="glass">
            <CardHeader>
              <CardTitle>Glass Effect Card</CardTitle>
              <CardDescription>
                Modern glassmorphism design
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                This card demonstrates the glass effect with backdrop blur and transparency.
                It adapts beautifully to both light and dark themes.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Educational Content Example */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Educational Content Preview</CardTitle>
            <CardDescription>
              How educational content looks in both themes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-primary/10 border border-primary/20 rounded-lg">
                <h3 className="font-semibold text-primary mb-2">Mathematics Question</h3>
                <p className="text-foreground">
                  What is the derivative of f(x) = x² + 3x - 5?
                </p>
                <div className="mt-3 space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center text-sm">A</div>
                    <span>2x + 3</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 rounded-full bg-success text-success-foreground flex items-center justify-center text-sm">B</div>
                    <span>2x + 3 ✓</span>
                  </div>
                </div>
              </div>
              
              <div className="p-4 bg-success/10 border border-success/20 rounded-lg">
                <h3 className="font-semibold text-success mb-2">Correct Answer!</h3>
                <p className="text-foreground">
                  Great job! The derivative of x² is 2x, and the derivative of 3x is 3.
                  Constants disappear when taking derivatives.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Theme Features */}
        <Card>
          <CardHeader>
            <CardTitle>Theme Features</CardTitle>
            <CardDescription>
              What makes our theme system special
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="text-center p-4">
                <Sun className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold mb-1">Light Theme</h3>
                <p className="text-sm text-muted-foreground">
                  Clean and bright for daytime studying
                </p>
              </div>
              <div className="text-center p-4">
                <Moon className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold mb-1">Dark Theme</h3>
                <p className="text-sm text-muted-foreground">
                  Easy on the eyes for night sessions
                </p>
              </div>
              <div className="text-center p-4">
                <Monitor className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold mb-1">System Theme</h3>
                <p className="text-sm text-muted-foreground">
                  Automatically follows your device
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
