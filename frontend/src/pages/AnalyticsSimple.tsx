import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Navbar } from "@/components/layout/Navbar";
import { useAuth } from "@/lib/api";
import { analyticsApi } from "@/lib/api/analytics";
import { Loader2, BarChart3, AlertTriangle } from "lucide-react";

export default function Analytics() {
  const navigate = useNavigate();
  const { isAuthenticated, getCurrentUser } = useAuth();
  
  const [user, setUser] = useState<any>(null);
  const [userLoading, setUserLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch user data
  useEffect(() => {
    const fetchUser = async () => {
      if (isAuthenticated && getCurrentUser) {
        try {
          const userData = await getCurrentUser();
          setUser({
            name: userData.full_name || userData.email.split('@')[0],
            email: userData.email,
            avatar: ""
          });
        } catch (error) {
          console.error('Failed to fetch user data:', error);
          setUser({
            name: "User",
            email: "<EMAIL>",
            avatar: ""
          });
        }
      }
      setUserLoading(false);
    };

    fetchUser();
  }, [isAuthenticated, getCurrentUser]);

  // Fetch analytics data
  const fetchAnalyticsData = async () => {
    if (!isAuthenticated) {
      setError("Authentication required");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log("Fetching analytics data...");
      
      // Fetch all analytics data
      const [
        dashboard,
        advancedAnalytics,
        trends,
        insights,
        studyPatterns,
        recommendations,
        heatmapData
      ] = await Promise.all([
        analyticsApi.getDashboard(),
        analyticsApi.getAdvancedAnalytics('month'),
        analyticsApi.getPerformanceTrends('month'),
        analyticsApi.getInsights(),
        analyticsApi.getStudyPatterns(),
        analyticsApi.getRecommendations(),
        analyticsApi.getHeatmapData()
      ]);

      console.log("Analytics data fetched successfully:", {
        dashboard,
        advancedAnalytics,
        trends,
        insights,
        studyPatterns,
        recommendations,
        heatmapData
      });

      setAnalyticsData({
        dashboard,
        advancedAnalytics,
        trends,
        insights,
        studyPatterns,
        recommendations,
        heatmapData
      });

    } catch (error) {
      console.error("Error fetching analytics data:", error);
      setError(error instanceof Error ? error.message : "Failed to load analytics data");
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on mount
  useEffect(() => {
    if (isAuthenticated && !userLoading) {
      fetchAnalyticsData();
    }
  }, [isAuthenticated, userLoading]);

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <Navbar isAuthenticated={false} />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="p-6 text-center">
              <div className="flex flex-col items-center space-y-4">
                <BarChart3 className="h-12 w-12 text-muted-foreground" />
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Login Required</h3>
                  <p className="text-muted-foreground">
                    Please log in to view your analytics dashboard.
                  </p>
                </div>
                <Button
                  onClick={() => navigate('/login')}
                  className="mt-4"
                >
                  Go to Login
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Show loading state while fetching user data
  if (userLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar isAuthenticated={true} />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">Loading user profile...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar isAuthenticated={true} user={user} />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
            <p className="text-muted-foreground">
              Track your learning progress and performance insights
            </p>
          </div>
          <Button onClick={fetchAnalyticsData} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Refresh Data"
            )}
          </Button>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">Loading analytics data...</p>
            </div>
          </div>
        )}

        {/* Analytics Data Display */}
        {analyticsData && !loading && (
          <div className="space-y-6">
            {/* Dashboard Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Dashboard Summary</CardTitle>
                <CardDescription>Your overall performance metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {analyticsData.dashboard?.total_tests_taken || 0}
                    </div>
                    <div className="text-sm text-muted-foreground">Total Tests</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {analyticsData.dashboard?.average_score?.toFixed(1) || 0}%
                    </div>
                    <div className="text-sm text-muted-foreground">Average Score</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {analyticsData.dashboard?.overall_proficiency?.toFixed(1) || 0}%
                    </div>
                    <div className="text-sm text-muted-foreground">Overall Proficiency</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {analyticsData.dashboard?.strongest_subject || "N/A"}
                    </div>
                    <div className="text-sm text-muted-foreground">Strongest Subject</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* API Call Status */}
            <Card>
              <CardHeader>
                <CardTitle>API Call Status</CardTitle>
                <CardDescription>Status of all analytics API endpoints</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Dashboard:</span>
                    <span className="text-green-600">✓ Success</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Advanced Analytics:</span>
                    <span className="text-green-600">✓ Success</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Performance Trends:</span>
                    <span className="text-green-600">✓ Success</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Insights:</span>
                    <span className="text-green-600">✓ Success</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Study Patterns:</span>
                    <span className="text-green-600">✓ Success</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Recommendations:</span>
                    <span className="text-green-600">✓ Success</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Heatmap Data:</span>
                    <span className="text-green-600">✓ Success</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Raw Data Display */}
            <Card>
              <CardHeader>
                <CardTitle>Raw Analytics Data</CardTitle>
                <CardDescription>All fetched analytics data (for debugging)</CardDescription>
              </CardHeader>
              <CardContent>
                <pre className="text-xs overflow-auto max-h-96 bg-gray-50 p-4 rounded">
                  {JSON.stringify(analyticsData, null, 2)}
                </pre>
              </CardContent>
            </Card>
          </div>
        )}

        {/* No Data State */}
        {!analyticsData && !loading && !error && (
          <Card className="max-w-2xl mx-auto">
            <CardContent className="p-6 text-center">
              <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Analytics Data</h3>
              <p className="text-muted-foreground mb-4">
                Click "Refresh Data" to load your analytics information.
              </p>
              <Button onClick={fetchAnalyticsData}>
                Load Analytics Data
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
