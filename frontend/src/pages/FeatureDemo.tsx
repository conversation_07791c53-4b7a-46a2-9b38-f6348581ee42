import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Navbar } from '@/components/layout/Navbar';
import { usePerformanceMonitor, useDebounce } from '@/hooks/usePerformance';
import { XPProgress } from '@/components/gamification/XPProgress';
import { AchievementGrid } from '@/components/gamification/AchievementCard';
import { GamificationNotifications } from '@/components/gamification/GamificationNotifications';
// Use lazy-loaded components for better performance
import {
  LiveActivityFeed,
  LiveLeaderboard,
  AIChatInterface,
  LiveAnalyticsDashboard,
  ProgressSummary,
  InteractiveDragDrop,
  InteractiveMathDemo,
  InteractiveCodeDemo,
  usePreloadOnHover
} from '@/components/lazy/LazyComponents';
import ErrorBoundary from '@/components/error/ErrorBoundary';
import { LoadingSpinner, NetworkStatus } from '@/components/ui/loading-states';
import { useGamificationStore } from '@/stores/gamificationStore';
import {
  Brain,
  Gamepad2,
  Bot,
  BarChart3,
  Zap,
  Trophy,
  Target,
  Lightbulb,
  TrendingUp,
  Users,
  Star,
  Award,
  BookOpen,
  Code,
  Calculator,
  Puzzle,
  CheckCircle,
  AlertCircle,
  Clock,
  Activity
} from 'lucide-react';

// Interactive demo components are now imported from separate files

export function FeatureDemo() {
  // Performance monitoring
  usePerformanceMonitor('FeatureDemo');

  const [activeFeature, setActiveFeature] = useState('adaptive');
  const [adaptiveData, setAdaptiveData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const { achievements, userStats } = useGamificationStore();

  // Preload components on tab hover
  const preloadAnalytics = usePreloadOnHover('analytics');
  const preloadAIChat = usePreloadOnHover('aiChat');
  const preloadProgress = usePreloadOnHover('progress');

  const handleLogin = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'demo1234'
        })
      });

      if (response.ok) {
        const data = await response.json();
        localStorage.setItem('access_token', data.access_token);
        setIsLoggedIn(true);
        fetchAdaptiveData(); // Refresh data with token
      }
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  // Test API connectivity
  const testAPIConnection = async () => {
    try {
      const response = await fetch('http://localhost:8080/health');
      const data = await response.json();
      console.log('API Health:', data);
    } catch (error) {
      console.error('API connection failed:', error);
    }
  };

  const fetchAdaptiveData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch('http://localhost:8080/api/v1/adaptive/insights', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setAdaptiveData(data);
    } catch (error) {
      console.error('Failed to fetch adaptive data:', error);
      // Use mock data for demo
      setAdaptiveData({
        insights: [
          {
            title: 'Strong Performance in Algebra',
            description: "You're excelling in algebraic concepts with 85% accuracy",
            type: 'strength',
            priority: 1,
            action: 'Continue practicing advanced algebra topics'
          },
          {
            title: 'Geometry Needs Attention',
            description: 'Your geometry performance is below average at 45%',
            type: 'weakness',
            priority: 5,
            action: 'Focus on basic geometry principles and practice more'
          },
          {
            title: 'Optimal Study Time',
            description: 'Your performance is best between 2-4 PM',
            type: 'recommendation',
            priority: 3,
            action: 'Schedule important practice sessions during this time'
          }
        ],
        overall_progress: {
          mastery_percentage: 67.5,
          topics_mastered: 8,
          topics_in_progress: 5,
          topics_to_review: 3,
          current_ability: 0.2,
          learning_velocity: 0.15
        }
      });
    }
    setLoading(false);
  };

  useEffect(() => {
    // Check if user is already logged in
    const token = localStorage.getItem('access_token');
    if (token) {
      setIsLoggedIn(true);
    }

    fetchAdaptiveData();
    testAPIConnection();
  }, []);

  return (
    <ErrorBoundary showDetails={process.env.NODE_ENV === 'development'}>
      <div className="min-h-screen bg-background">
        <NetworkStatus />
        <Navbar isAuthenticated={true} />
        <GamificationNotifications />

        <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gradient mb-4">
            🎓 Test-Spark Enhanced Features Demo
          </h1>
          <p className="text-xl text-muted-foreground mb-6">
            Experience the next-generation learning platform with AI-powered adaptive learning
          </p>

          {!isLoggedIn && (
            <div className="mb-6">
              <Button onClick={handleLogin} className="mb-4">
                Login as Demo User (<EMAIL>)
              </Button>
              <p className="text-sm text-muted-foreground">
                Login to see real adaptive learning data from the API
              </p>
            </div>
          )}

          {isLoggedIn && (
            <div className="mb-6">
              <Badge className="bg-green-500 text-white">
                ✅ Logged in as Demo User - Real data loaded!
              </Badge>
            </div>
          )}

          <div className="flex flex-wrap justify-center gap-4">
            <Badge variant="secondary" className="text-sm">
              <Brain className="h-4 w-4 mr-1" />
              Adaptive Learning
            </Badge>
            <Badge variant="secondary" className="text-sm">
              <Gamepad2 className="h-4 w-4 mr-1" />
              Gamification
            </Badge>
            <Badge variant="secondary" className="text-sm">
              <Bot className="h-4 w-4 mr-1" />
              AI Tutoring
            </Badge>
            <Badge variant="secondary" className="text-sm">
              <BarChart3 className="h-4 w-4 mr-1" />
              Advanced Analytics
            </Badge>
          </div>
        </div>

        {/* Feature Navigation */}
        <Tabs value={activeFeature} onValueChange={setActiveFeature} className="w-full">
          <TabsList className="grid w-full grid-cols-6 mb-8">
            <TabsTrigger value="adaptive" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Adaptive Learning
            </TabsTrigger>
            <TabsTrigger value="interactive" className="flex items-center gap-2">
              <Puzzle className="h-4 w-4" />
              Interactive Questions
            </TabsTrigger>
            <TabsTrigger value="gamification" className="flex items-center gap-2">
              <Trophy className="h-4 w-4" />
              Gamification
            </TabsTrigger>
            <TabsTrigger
              value="ai-tutor"
              className="flex items-center gap-2"
              {...preloadAIChat}
            >
              <Bot className="h-4 w-4" />
              AI Tutor
            </TabsTrigger>
            <TabsTrigger
              value="analytics"
              className="flex items-center gap-2"
              {...preloadAnalytics}
            >
              <BarChart3 className="h-4 w-4" />
              Analytics
            </TabsTrigger>
            <TabsTrigger
              value="progress"
              className="flex items-center gap-2"
              {...preloadProgress}
            >
              <Target className="h-4 w-4" />
              Progress
            </TabsTrigger>
          </TabsList>

          {/* Adaptive Learning Demo */}
          <TabsContent value="adaptive" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-6 w-6 text-blue-500" />
                  Adaptive Learning Engine
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">Loading adaptive insights...</div>
                ) : adaptiveData ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-semibold mb-4">Learning Progress</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span>Mastery Level:</span>
                          <Badge>{adaptiveData.overall_progress.mastery_percentage}%</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Topics Mastered:</span>
                          <Badge variant="outline">{adaptiveData.overall_progress.topics_mastered}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Learning Velocity:</span>
                          <Badge variant="secondary">+{(adaptiveData.overall_progress.learning_velocity * 100).toFixed(1)}%/week</Badge>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h3 className="font-semibold mb-4">Personalized Insights</h3>
                      <div className="space-y-2">
                        {adaptiveData.insights.map((insight, index) => (
                          <div key={index} className="p-3 bg-muted/50 rounded-lg">
                            <div className="font-medium text-sm">{insight.title}</div>
                            <div className="text-xs text-muted-foreground">{insight.description}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">Failed to load adaptive data</div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Interactive Questions Demo */}
          <TabsContent value="interactive" className="space-y-6">
            <div className="space-y-8">
              {/* Drag & Drop Demo */}
              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <Puzzle className="h-5 w-5" />
                  Interactive Drag & Drop Questions
                </h3>
                <InteractiveDragDrop />
              </div>

              {/* Math Demo */}
              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Math Expression Solver
                </h3>
                <InteractiveMathDemo />
              </div>

              {/* Code Demo */}
              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  Code Execution Challenges
                </h3>
                <InteractiveCodeDemo />
              </div>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Interactive Question Features</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h4 className="font-medium">✨ Enhanced Question Types</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Drag & Drop with visual feedback</li>
                      <li>• Real-time code execution</li>
                      <li>• LaTeX math expression rendering</li>
                      <li>• Interactive simulations</li>
                      <li>• Multimedia integration</li>
                    </ul>
                  </div>
                  <div className="space-y-3">
                    <h4 className="font-medium">🎯 Smart Features</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Instant validation and feedback</li>
                      <li>• Progressive hint system</li>
                      <li>• Adaptive difficulty adjustment</li>
                      <li>• Performance tracking</li>
                      <li>• Accessibility support</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Gamification Demo */}
          <TabsContent value="gamification" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* XP Progress */}
              <XPProgress showDetails={true} />

              {/* Achievements */}
              <AchievementGrid
                achievements={achievements}
                title="Your Achievements"
                maxItems={6}
              />

            </div>

            {/* Live Leaderboard */}
            <LiveLeaderboard
              maxEntries={8}
              showRankChanges={true}
              autoRefresh={true}
              refreshInterval={30}
            />

            {/* Live Activity Feed */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <LiveActivityFeed
                showGlobalActivity={false}
                maxItems={5}
              />

              <LiveActivityFeed
                showGlobalActivity={true}
                maxItems={5}
              />
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Gamification Features</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <Trophy className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                    <h4 className="font-medium">Achievements</h4>
                    <p className="text-sm text-muted-foreground">Unlock badges for various accomplishments</p>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <Users className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                    <h4 className="font-medium">Leaderboards</h4>
                    <p className="text-sm text-muted-foreground">Compete with peers and track rankings</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <Zap className="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <h4 className="font-medium">Experience Points</h4>
                    <p className="text-sm text-muted-foreground">Earn XP and level up your profile</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* AI Tutor Demo */}
          <TabsContent value="ai-tutor" className="space-y-6">
            {/* Functional AI Chat Interface */}
            <AIChatInterface
              context="general"
              maxHeight="500px"
            />

            <Card>
              <CardHeader>
                <CardTitle>AI Tutor Features</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h4 className="font-medium">🤖 Smart Tutoring</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Socratic method questioning</li>
                      <li>• Personalized explanations</li>
                      <li>• Adaptive difficulty</li>
                      <li>• Context-aware responses</li>
                      <li>• Multi-language support</li>
                    </ul>
                  </div>
                  <div className="space-y-3">
                    <h4 className="font-medium">💡 Learning Support</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• 24/7 availability</li>
                      <li>• Instant feedback</li>
                      <li>• Progress tracking</li>
                      <li>• Customizable personality</li>
                      <li>• Learning path guidance</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Demo */}
          <TabsContent value="analytics" className="space-y-6">
            <LiveAnalyticsDashboard />
          </TabsContent>

          {/* Progress Demo */}
          <TabsContent value="progress" className="space-y-6">
            <ProgressSummary />
          </TabsContent>
        </Tabs>
        </div>
      </div>
    </ErrorBoundary>
  );
}
