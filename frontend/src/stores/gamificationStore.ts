import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  xpReward: number;
  unlocked: boolean;
  unlockedAt?: Date;
  category: 'demo' | 'learning' | 'social' | 'milestone';
}

export interface UserStats {
  totalXP: number;
  level: number;
  currentStreak: number;
  longestStreak: number;
  demosCompleted: number;
  perfectScores: number;
  totalAttempts: number;
  averageScore: number;
  timeSpent: number; // in minutes
}

export interface LeaderboardEntry {
  id: string;
  username: string;
  avatar?: string;
  xp: number;
  level: number;
  rank: number;
  isCurrentUser?: boolean;
}

interface GamificationState {
  userStats: UserStats;
  achievements: Achievement[];
  leaderboard: LeaderboardEntry[];
  recentActivity: Array<{
    id: string;
    type: 'xp_gained' | 'achievement_unlocked' | 'level_up' | 'demo_completed';
    message: string;
    timestamp: Date;
    xp?: number;
    achievement?: Achievement;
  }>;

  // State management
  isLoading: boolean;
  error: string | null;

  // Actions
  awardXP: (amount: number, reason: string) => void;
  completeDemo: (demoType: string, score: number, attempts: number) => void;
  checkAchievements: () => Achievement[];
  updateLeaderboard: () => void;
  addActivity: (activity: any) => void;
  resetProgress: () => void;
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
}

const INITIAL_ACHIEVEMENTS: Achievement[] = [
  {
    id: 'first_demo',
    name: 'First Steps',
    description: 'Complete your first interactive demo',
    icon: '🎯',
    xpReward: 50,
    unlocked: false,
    category: 'demo'
  },
  {
    id: 'perfect_score',
    name: 'Perfectionist',
    description: 'Get a perfect score on any demo',
    icon: '⭐',
    xpReward: 100,
    unlocked: false,
    category: 'learning'
  },
  {
    id: 'speed_demon',
    name: 'Speed Demon',
    description: 'Complete a demo on first attempt',
    icon: '⚡',
    xpReward: 75,
    unlocked: false,
    category: 'learning'
  },
  {
    id: 'persistent',
    name: 'Never Give Up',
    description: 'Complete a demo after 3+ attempts',
    icon: '💪',
    xpReward: 60,
    unlocked: false,
    category: 'learning'
  },
  {
    id: 'explorer',
    name: 'Explorer',
    description: 'Try all three types of demos',
    icon: '🗺️',
    xpReward: 150,
    unlocked: false,
    category: 'demo'
  },
  {
    id: 'math_master',
    name: 'Math Master',
    description: 'Complete 3 math problems',
    icon: '🧮',
    xpReward: 120,
    unlocked: false,
    category: 'learning'
  },
  {
    id: 'code_ninja',
    name: 'Code Ninja',
    description: 'Complete 3 coding challenges',
    icon: '👨‍💻',
    xpReward: 120,
    unlocked: false,
    category: 'learning'
  },
  {
    id: 'level_5',
    name: 'Rising Star',
    description: 'Reach level 5',
    icon: '🌟',
    xpReward: 200,
    unlocked: false,
    category: 'milestone'
  }
];

const MOCK_LEADERBOARD: LeaderboardEntry[] = [
  { id: '1', username: 'Alice Johnson', xp: 2450, level: 12, rank: 1 },
  { id: '2', username: 'Bob Smith', xp: 2380, level: 11, rank: 2 },
  { id: '3', username: 'Carol Davis', xp: 2320, level: 11, rank: 3 },
  { id: '4', username: 'David Wilson', xp: 2180, level: 10, rank: 4 },
  { id: '5', username: 'Emma Brown', xp: 2050, level: 10, rank: 5 },
];

const calculateLevel = (xp: number): number => {
  // Level formula: level = floor(sqrt(xp / 100)) + 1
  return Math.floor(Math.sqrt(xp / 100)) + 1;
};

const getXPForNextLevel = (currentLevel: number): number => {
  // XP needed for next level
  return Math.pow(currentLevel, 2) * 100;
};

export const useGamificationStore = create<GamificationState>()(
  persist(
    (set, get) => ({
      userStats: {
        totalXP: 0,
        level: 1,
        currentStreak: 0,
        longestStreak: 0,
        demosCompleted: 0,
        perfectScores: 0,
        totalAttempts: 0,
        averageScore: 0,
        timeSpent: 0,
      },
      achievements: INITIAL_ACHIEVEMENTS,
      leaderboard: MOCK_LEADERBOARD,
      recentActivity: [],
      isLoading: false,
      error: null,

      awardXP: (amount: number, reason: string) => {
        set((state) => {
          const newXP = state.userStats.totalXP + amount;
          const oldLevel = state.userStats.level;
          const newLevel = calculateLevel(newXP);
          const leveledUp = newLevel > oldLevel;

          const newActivity = {
            id: Date.now().toString(),
            type: 'xp_gained' as const,
            message: `+${amount} XP: ${reason}`,
            timestamp: new Date(),
            xp: amount,
          };

          let activities = [newActivity, ...state.recentActivity.slice(0, 9)];

          if (leveledUp) {
            const levelUpActivity = {
              id: (Date.now() + 1).toString(),
              type: 'level_up' as const,
              message: `Level up! You're now level ${newLevel}`,
              timestamp: new Date(),
            };
            activities = [levelUpActivity, ...activities.slice(0, 9)];
          }

          return {
            userStats: {
              ...state.userStats,
              totalXP: newXP,
              level: newLevel,
            },
            recentActivity: activities,
          };
        });
      },

      completeDemo: (demoType: string, score: number, attempts: number) => {
        set((state) => {
          const newStats = {
            ...state.userStats,
            demosCompleted: state.userStats.demosCompleted + 1,
            totalAttempts: state.userStats.totalAttempts + attempts,
            perfectScores: score === 100 ? state.userStats.perfectScores + 1 : state.userStats.perfectScores,
          };

          // Calculate new average score
          const totalScore = (state.userStats.averageScore * (state.userStats.demosCompleted || 1)) + score;
          newStats.averageScore = Math.round(totalScore / newStats.demosCompleted);

          const activity = {
            id: Date.now().toString(),
            type: 'demo_completed' as const,
            message: `Completed ${demoType} demo with ${score}% score`,
            timestamp: new Date(),
          };

          return {
            userStats: newStats,
            recentActivity: [activity, ...state.recentActivity.slice(0, 9)],
          };
        });

        // Check for achievements after updating stats
        get().checkAchievements();
      },

      checkAchievements: () => {
        const state = get();
        const newlyUnlocked: Achievement[] = [];

        set((currentState) => {
          const updatedAchievements = currentState.achievements.map((achievement) => {
            if (achievement.unlocked) return achievement;

            let shouldUnlock = false;

            switch (achievement.id) {
              case 'first_demo':
                shouldUnlock = currentState.userStats.demosCompleted >= 1;
                break;
              case 'perfect_score':
                shouldUnlock = currentState.userStats.perfectScores >= 1;
                break;
              case 'speed_demon':
                // This would be checked when completing a demo with 1 attempt
                break;
              case 'persistent':
                // This would be checked when completing a demo with 3+ attempts
                break;
              case 'level_5':
                shouldUnlock = currentState.userStats.level >= 5;
                break;
              // Add more achievement logic here
            }

            if (shouldUnlock) {
              const unlockedAchievement = {
                ...achievement,
                unlocked: true,
                unlockedAt: new Date(),
              };
              newlyUnlocked.push(unlockedAchievement);
              
              // Award XP for achievement
              setTimeout(() => {
                get().awardXP(achievement.xpReward, `Achievement: ${achievement.name}`);
              }, 100);

              return unlockedAchievement;
            }

            return achievement;
          });

          // Add achievement unlock activities
          const achievementActivities = newlyUnlocked.map((achievement) => ({
            id: (Date.now() + Math.random()).toString(),
            type: 'achievement_unlocked' as const,
            message: `🏆 Achievement unlocked: ${achievement.name}`,
            timestamp: new Date(),
            achievement,
          }));

          return {
            achievements: updatedAchievements,
            recentActivity: [
              ...achievementActivities,
              ...currentState.recentActivity.slice(0, 10 - achievementActivities.length),
            ],
          };
        });

        return newlyUnlocked;
      },

      updateLeaderboard: () => {
        set((state) => {
          // Add current user to leaderboard
          const currentUser: LeaderboardEntry = {
            id: 'current',
            username: 'You',
            xp: state.userStats.totalXP,
            level: state.userStats.level,
            rank: 0,
            isCurrentUser: true,
          };

          // Merge with mock data and sort
          const allEntries = [...MOCK_LEADERBOARD, currentUser]
            .sort((a, b) => b.xp - a.xp)
            .map((entry, index) => ({ ...entry, rank: index + 1 }));

          return {
            leaderboard: allEntries,
          };
        });
      },

      addActivity: (activity) => {
        set((state) => ({
          recentActivity: [activity, ...state.recentActivity.slice(0, 9)],
        }));
      },

      resetProgress: () => {
        set({
          userStats: {
            totalXP: 0,
            level: 1,
            currentStreak: 0,
            longestStreak: 0,
            demosCompleted: 0,
            perfectScores: 0,
            totalAttempts: 0,
            averageScore: 0,
            timeSpent: 0,
          },
          achievements: INITIAL_ACHIEVEMENTS,
          recentActivity: [],
          isLoading: false,
          error: null,
        });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'test-spark-gamification',
      version: 1,
    }
  )
);

// Helper functions
export const getXPProgress = (currentXP: number, level: number) => {
  const currentLevelXP = Math.pow(level - 1, 2) * 100;
  const nextLevelXP = Math.pow(level, 2) * 100;
  const progressXP = currentXP - currentLevelXP;
  const neededXP = nextLevelXP - currentLevelXP;
  
  return {
    current: progressXP,
    needed: neededXP,
    percentage: Math.round((progressXP / neededXP) * 100),
  };
};
