import { useCallback } from 'react';
import { httpClient } from '../api-client';
import {
  DashboardSummary,
  TestAnalysis,
  TopicPerformanceWithName,
  AdvancedAnalytics,
  PerformanceTrend,
  AnalyticsInsight,
  StudyPattern,
  RecommendationItem,
  HeatmapData,
  TimeFilter,
  DateRange,
} from '../api-types';

// Analytics API functions
export const analyticsApi = {
  // Get dashboard summary data
  getDashboard: async (): Promise<DashboardSummary> => {
    return httpClient.get<DashboardSummary>('/analysis/dashboard');
  },

  // Get detailed analysis for a specific test
  getTestAnalysis: async (testId: string): Promise<TestAnalysis> => {
    return httpClient.get<TestAnalysis>(`/analysis/tests/${testId}`);
  },

  // Get performance analysis for a specific topic
  getTopicAnalysis: async (topicId: number): Promise<TopicPerformanceWithName> => {
    return httpClient.get<TopicPerformanceWithName>(`/analysis/topics/${topicId}`);
  },

  // New Advanced Analytics API functions

  // Get comprehensive analytics data
  getAdvancedAnalytics: async (timeFilter: TimeFilter = 'month', dateRange?: DateRange): Promise<AdvancedAnalytics> => {
    let url = `/analytics/advanced?period=${timeFilter}`;
    if (timeFilter === 'custom' && dateRange) {
      url += `&start_date=${dateRange.start_date}&end_date=${dateRange.end_date}`;
    }
    return httpClient.get<AdvancedAnalytics>(url);
  },

  // Get performance trends over time
  getPerformanceTrends: async (timeFilter: TimeFilter = 'month', dateRange?: DateRange): Promise<PerformanceTrend[]> => {
    let url = `/analytics/trends?period=${timeFilter}`;
    if (timeFilter === 'custom' && dateRange) {
      url += `&start_date=${dateRange.start_date}&end_date=${dateRange.end_date}`;
    }
    return httpClient.get<PerformanceTrend[]>(url);
  },

  // Get AI-generated insights
  getInsights: async (): Promise<AnalyticsInsight[]> => {
    return httpClient.get<AnalyticsInsight[]>('/analytics/insights');
  },

  // Get study behavior patterns
  getStudyPatterns: async (): Promise<StudyPattern> => {
    return httpClient.get<StudyPattern>('/analytics/study-patterns');
  },

  // Get personalized recommendations
  getRecommendations: async (): Promise<RecommendationItem[]> => {
    return httpClient.get<RecommendationItem[]>('/analytics/recommendations');
  },

  // Get heatmap data for topic performance visualization
  getHeatmapData: async (): Promise<HeatmapData[]> => {
    return httpClient.get<HeatmapData[]>('/analytics/heatmap-data');
  },
};

// React hooks for analytics data
export const useAnalytics = () => {
  const getDashboard = useCallback(async () => {
    try {
      const response = await analyticsApi.getDashboard();
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get dashboard data',
      };
    }
  }, []);

  const getTestAnalysis = useCallback(async (testId: string) => {
    try {
      const response = await analyticsApi.getTestAnalysis(testId);
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get test analysis',
      };
    }
  }, []);

  const getTopicAnalysis = useCallback(async (topicId: number) => {
    try {
      const response = await analyticsApi.getTopicAnalysis(topicId);
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get topic analysis',
      };
    }
  }, []);

  const getAdvancedAnalytics = useCallback(async (timeFilter: TimeFilter = 'month') => {
    try {
      const response = await analyticsApi.getAdvancedAnalytics(timeFilter);
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get advanced analytics',
      };
    }
  }, []);

  const getPerformanceTrends = useCallback(async (timeFilter: TimeFilter = 'month') => {
    try {
      const response = await analyticsApi.getPerformanceTrends(timeFilter);
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get performance trends',
      };
    }
  }, []);

  const getInsights = useCallback(async () => {
    try {
      const response = await analyticsApi.getInsights();
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get insights',
      };
    }
  }, []);

  const getStudyPatterns = useCallback(async () => {
    try {
      const response = await analyticsApi.getStudyPatterns();
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get study patterns',
      };
    }
  }, []);

  const getRecommendations = useCallback(async () => {
    try {
      const response = await analyticsApi.getRecommendations();
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get recommendations',
      };
    }
  }, []);

  const getHeatmapData = useCallback(async () => {
    try {
      const response = await analyticsApi.getHeatmapData();
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get heatmap data',
      };
    }
  }, []);

  return {
    getDashboard,
    getTestAnalysis,
    getTopicAnalysis,
    getAdvancedAnalytics,
    getPerformanceTrends,
    getInsights,
    getStudyPatterns,
    getRecommendations,
    getHeatmapData,
  };
};
