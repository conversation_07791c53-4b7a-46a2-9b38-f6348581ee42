import { useCallback } from 'react';
import { httpClient } from '../api-client';

// Report types and interfaces
export interface ReportRequest {
  report_type: 'performance' | 'progress' | 'detailed' | 'comparative';
  format: 'pdf' | 'html';
  time_filter: 'week' | 'month' | 'quarter' | 'year' | 'custom';
  date_range?: {
    start_date: string;
    end_date: string;
  };
  include_sections?: string[];
  custom_title?: string;
}

export interface ReportResponse {
  report_id: string;
  download_url: string;
  format: 'pdf' | 'html';
  size_bytes: number;
  generated_at: string;
  expires_at: string;
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: string;
  sections: string[];
  is_default: boolean;
}

export interface ReportData {
  user_info: {
    name: string;
    email: string;
    grade?: string;
    school?: string;
    generated_at: string;
  };
  report_metadata: {
    title: string;
    type: string;
    format: string;
    time_filter: string;
    date_range?: {
      start_date: string;
      end_date: string;
    };
    generated_at: string;
    version: string;
  };
  dashboard: any;
  analytics: any;
  trends: any[];
  insights: any[];
  study_patterns: any;
  recommendations: any[];
  heatmap_data: any[];
  topic_analysis: any[];
  test_history: any[];
  performance_metrics: {
    overall_score: number;
    improvement_rate: number;
    consistency_score: number;
    strengths_count: number;
    weaknesses_count: number;
    tests_completed: number;
    study_time_hours: number;
    average_session_time: number;
    best_subject: string;
    weakest_subject: string;
    recommended_focus: string[];
  };
  charts: {
    performance_trend: string;
    subject_radar: string;
    topic_heatmap: string;
    progress_chart: string;
    difficulty_analysis: string;
  };
}

export interface ReportHistoryItem {
  filename: string;
  format: string;
  size: number;
  generated_at: string;
  download_url: string;
}

// Report API functions
export const reportsApi = {
  // Generate a new report
  generateReport: async (request: ReportRequest): Promise<ReportResponse> => {
    return httpClient.post<ReportResponse>('/reports/generate', request);
  },

  // Download a generated report
  downloadReport: async (reportId: string): Promise<Blob> => {
    const token = localStorage.getItem('test_spark_access_token');
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`/api/v1/reports/download/${reportId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to download report: ${response.status} ${errorText}`);
    }

    return response.blob();
  },

  // Get available report templates
  getTemplates: async (): Promise<{
    templates: ReportTemplate[];
    sections: Record<string, string>;
  }> => {
    return httpClient.get<{
      templates: ReportTemplate[];
      sections: Record<string, string>;
    }>('/reports/templates');
  },

  // Preview report data without generating file
  previewReport: async (request: ReportRequest): Promise<string> => {
    const token = localStorage.getItem('test_spark_access_token');
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch('/api/v1/reports/preview', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to preview report: ${response.status} ${errorText}`);
    }

    return response.text(); // Return HTML content as string
  },

  // Get user's report history
  getHistory: async (): Promise<{ reports: ReportHistoryItem[] }> => {
    return httpClient.get<{ reports: ReportHistoryItem[] }>('/reports/history');
  },
};

// Helper functions for report generation
export const reportHelpers = {
  // Create a basic performance report request
  createPerformanceReport: (
    format: 'pdf' | 'html' = 'html',
    timeFilter: 'week' | 'month' | 'quarter' | 'year' = 'month'
  ): ReportRequest => ({
    report_type: 'performance',
    format,
    time_filter: timeFilter,
    include_sections: ['summary', 'performance', 'strengths', 'weaknesses', 'charts'],
  }),

  // Create a detailed analysis report request
  createDetailedReport: (
    format: 'pdf' | 'html' = 'html',
    timeFilter: 'week' | 'month' | 'quarter' | 'year' = 'month'
  ): ReportRequest => ({
    report_type: 'detailed',
    format,
    time_filter: timeFilter,
    include_sections: [
      'summary',
      'performance',
      'trends',
      'strengths',
      'weaknesses',
      'insights',
      'recommendations',
      'study_patterns',
      'topic_analysis',
      'test_history',
      'charts',
      'action_plan',
    ],
  }),

  // Create a progress report request
  createProgressReport: (
    format: 'pdf' | 'html' = 'html',
    timeFilter: 'week' | 'month' | 'quarter' | 'year' = 'month'
  ): ReportRequest => ({
    report_type: 'progress',
    format,
    time_filter: timeFilter,
    include_sections: ['summary', 'trends', 'insights', 'study_patterns', 'recommendations', 'charts'],
  }),

  // Create a custom report request
  createCustomReport: (
    type: 'performance' | 'progress' | 'detailed' | 'comparative',
    format: 'pdf' | 'html',
    timeFilter: 'week' | 'month' | 'quarter' | 'year' | 'custom',
    sections: string[],
    customTitle?: string,
    dateRange?: { start_date: string; end_date: string }
  ): ReportRequest => ({
    report_type: type,
    format,
    time_filter: timeFilter,
    include_sections: sections,
    custom_title: customTitle,
    date_range: dateRange,
  }),

  // Format file size for display
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Format date for display
  formatDate: (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  },

  // Get report type display name
  getReportTypeDisplayName: (type: string): string => {
    const typeNames: Record<string, string> = {
      performance: 'Performance Analysis',
      progress: 'Learning Progress',
      detailed: 'Detailed Analysis',
      comparative: 'Comparative Analysis',
    };
    return typeNames[type] || type;
  },

  // Get time filter display name
  getTimeFilterDisplayName: (filter: string): string => {
    const filterNames: Record<string, string> = {
      week: 'Last Week',
      month: 'Last Month',
      quarter: 'Last Quarter',
      year: 'Last Year',
      custom: 'Custom Range',
    };
    return filterNames[filter] || filter;
  },

  // Download file from blob
  downloadBlob: (blob: Blob, filename: string): void => {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },

  // Generate filename for report
  generateFilename: (type: string, format: string, date?: Date): string => {
    const timestamp = (date || new Date()).toISOString().split('T')[0];
    return `${type}_report_${timestamp}.${format}`;
  },
};

// React hooks for reports
export const useReports = () => {
  const generateReport = useCallback(async (request: ReportRequest) => {
    try {
      const response = await reportsApi.generateReport(request);
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate report',
      };
    }
  }, []);

  const downloadReport = useCallback(async (reportId: string, filename?: string) => {
    try {
      const blob = await reportsApi.downloadReport(reportId);
      const downloadFilename = filename || reportHelpers.generateFilename('report', 'pdf');
      reportHelpers.downloadBlob(blob, downloadFilename);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to download report',
      };
    }
  }, []);

  const getTemplates = useCallback(async () => {
    try {
      const response = await reportsApi.getTemplates();
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get templates',
      };
    }
  }, []);

  const previewReport = useCallback(async (request: ReportRequest) => {
    try {
      const response = await reportsApi.previewReport(request);
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to preview report',
      };
    }
  }, []);

  const getHistory = useCallback(async () => {
    try {
      const response = await reportsApi.getHistory();
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get report history',
      };
    }
  }, []);

  return {
    generateReport,
    downloadReport,
    getTemplates,
    previewReport,
    getHistory,
  };
};
