import { httpClient } from '../api-client';
import { Subject, Exam, Topic } from '../api-types';

// Content API functions
export const contentApi = {
  // Get all subjects
  getSubjects: async (): Promise<Subject[]> => {
    return httpClient.get<Subject[]>('/content/subjects');
  },

  // Get all exams
  getExams: async (): Promise<Exam[]> => {
    return httpClient.get<Exam[]>('/content/exams');
  },

  // Get topics by subject ID
  getTopicsBySubject: async (subjectId: number): Promise<Topic[]> => {
    return httpClient.get<Topic[]>(`/content/subjects/${subjectId}/topics`);
  },
};

// React hooks for content data
export const useContent = () => {
  return {
    getSubjects: contentApi.getSubjects,
    getExams: contentApi.getExams,
    getTopicsBySubject: contentApi.getTopicsBySubject,
  };
};
