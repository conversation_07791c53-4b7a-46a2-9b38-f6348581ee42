import { useCallback } from 'react';
import { httpClient } from '../api-client';
import {
  CreateTestRequest,
  TestWithFirstQuestion,
  TestQuestionWithDetails,
  SubmitAnswerRequest,
  SubmitAnswerResponse,
  CompleteTestResponse,
  DifficultyLevel,
} from '../api-types';

// Test API functions
export const testApi = {
  // Create a new test
  createTest: async (data: CreateTestRequest): Promise<TestWithFirstQuestion> => {
    return httpClient.post<TestWithFirstQuestion>('/tests', data);
  },

  // Regenerate test for weak areas
  regenerateTest: async (data: {
    num_questions: number;
  }): Promise<TestWithFirstQuestion> => {
    return httpClient.post<TestWithFirstQuestion>('/tests/regenerate', data);
  },

  // Get a specific question from a test
  getQuestion: async (
    testId: string,
    questionOrder: number
  ): Promise<TestQuestionWithDetails> => {
    return httpClient.get<TestQuestionWithDetails>(
      `/tests/${testId}/questions/${questionOrder}`
    );
  },

  // Submit an answer for a question
  submitAnswer: async (
    testId: string,
    data: SubmitAnswerRequest
  ): Promise<SubmitAnswerResponse> => {
    return httpClient.post<SubmitAnswerResponse>(`/tests/${testId}/answers`, data);
  },

  // Complete a test
  completeTest: async (
    testId: string,
    timeTakenSeconds: number
  ): Promise<CompleteTestResponse> => {
    return httpClient.post<CompleteTestResponse>(`/tests/${testId}/complete`, {
      time_taken_seconds: timeTakenSeconds,
    });
  },
};

// React hooks for test management
export const useTests = () => {
  const createTest = useCallback(async (
    subjectId?: number,
    examId?: number,
    numQuestions: number = 10,
    difficultyLevels: DifficultyLevel[] = ['easy', 'medium'],
    topicIds?: number[]
  ) => {
    try {
      const data: CreateTestRequest = {
        subject_id: subjectId,
        exam_id: examId,
        num_questions: numQuestions,
        difficulty_levels: difficultyLevels,
        topic_ids: topicIds,
      };

      const response = await testApi.createTest(data);
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create test',
      };
    }
  }, []);

  const regenerateTest = useCallback(async (numQuestions: number = 10) => {
    try {
      const response = await testApi.regenerateTest({ num_questions: numQuestions });
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to regenerate test',
      };
    }
  }, []);

  const getQuestion = useCallback(async (testId: string, questionOrder: number) => {
    try {
      const response = await testApi.getQuestion(testId, questionOrder);
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get question',
      };
    }
  }, []);

  const submitAnswer = useCallback(async (
    testId: string,
    questionId: string,
    selectedOptionIndex: number
  ) => {
    try {
      const response = await testApi.submitAnswer(testId, {
        question_id: questionId,
        selected_option_index: selectedOptionIndex,
      });
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to submit answer',
      };
    }
  }, []);

  const completeTest = useCallback(async (testId: string, timeTakenSeconds: number) => {
    try {
      const response = await testApi.completeTest(testId, timeTakenSeconds);
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to complete test',
      };
    }
  }, []);

  return {
    createTest,
    regenerateTest,
    getQuestion,
    submitAnswer,
    completeTest,
  };
};
