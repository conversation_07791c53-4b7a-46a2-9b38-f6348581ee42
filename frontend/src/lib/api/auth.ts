import { useCallback } from 'react';
import { httpClient, TokenManager } from '../api-client';
import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  RefreshTokenResponse,
  UserWithProfile,
} from '../api-types';

// Authentication API functions
export const authApi = {
  // Register a new user
  register: async (data: RegisterRequest): Promise<RegisterResponse> => {
    const response = await httpClient.post<RegisterResponse>('/auth/register', data);
    return response;
  },

  // Login user
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    const response = await httpClient.post<LoginResponse>('/auth/login', data);
    
    // Store tokens after successful login
    TokenManager.setTokens(response.access_token, response.refresh_token);
    
    return response;
  },

  // Refresh access token
  refreshToken: async (refreshToken?: string): Promise<RefreshTokenResponse> => {
    const token = refreshToken || TokenManager.getRefreshToken();
    if (!token) {
      throw new Error('No refresh token available');
    }

    const response = await httpClient.post<RefreshTokenResponse>('/auth/refresh-token', {
      refresh_token: token,
    });

    // Update stored access token
    TokenManager.setAccessToken(response.access_token);
    
    return response;
  },

  // Logout user
  logout: async (): Promise<void> => {
    const refreshToken = TokenManager.getRefreshToken();
    
    if (refreshToken) {
      try {
        await httpClient.post('/auth/logout', {
          refresh_token: refreshToken,
        });
      } catch (error) {
        // Even if logout fails on server, clear local tokens
        console.warn('Logout request failed:', error);
      }
    }

    // Clear tokens from local storage
    TokenManager.clearTokens();
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    return TokenManager.isAuthenticated();
  },

  // Get current user profile
  getCurrentUser: async (): Promise<UserWithProfile> => {
    return httpClient.get<UserWithProfile>('/users/me');
  },

  // Update user profile
  updateProfile: async (data: Partial<UserWithProfile>): Promise<UserWithProfile> => {
    return httpClient.put<UserWithProfile>('/users/me', data);
  },
};

// Authentication hook for React components
export const useAuth = () => {
  const isAuthenticated = authApi.isAuthenticated();

  const login = useCallback(async (email: string, password: string) => {
    try {
      const response = await authApi.login({ email, password });
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed'
      };
    }
  }, []);

  const register = useCallback(async (data: RegisterRequest) => {
    try {
      const response = await authApi.register(data);
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Registration failed'
      };
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      await authApi.logout();
      // Redirect to login page
      window.location.href = '/login';
    } catch (error) {
      console.error('Logout error:', error);
      // Still redirect even if logout fails
      window.location.href = '/login';
    }
  }, []);

  return {
    isAuthenticated,
    login,
    register,
    logout,
    getCurrentUser: authApi.getCurrentUser,
    updateProfile: authApi.updateProfile,
  };
};
