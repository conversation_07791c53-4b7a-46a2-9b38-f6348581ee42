import { ApiError, ApiResponse } from './api-types';

// Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
const API_VERSION = '/api/v1';
const API_TIMEOUT = parseInt(import.meta.env.VITE_API_TIMEOUT || '30000');
const DEBUG_MODE = import.meta.env.VITE_DEBUG === 'true';

// Token management
class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'test_spark_access_token';
  private static readonly REFRESH_TOKEN_KEY = 'test_spark_refresh_token';

  static getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  static setAccessToken(token: string): void {
    localStorage.setItem(this.ACCESS_TOKEN_KEY, token);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static setRefreshToken(token: string): void {
    localStorage.setItem(this.REFRESH_TOKEN_KEY, token);
  }

  static setTokens(accessToken: string, refreshToken: string): void {
    this.setAccessToken(accessToken);
    this.setRefreshToken(refreshToken);
  }

  static clearTokens(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }

  static isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }
}

// HTTP Client class
class HttpClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    // Default headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    // Add authorization header if token exists
    const accessToken = TokenManager.getAccessToken();
    if (accessToken) {
      headers.Authorization = `Bearer ${accessToken}`;
    }

    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

    const config: RequestInit = {
      ...options,
      headers,
      signal: controller.signal,
    };

    // Debug logging
    if (DEBUG_MODE) {
      console.log(`API Request: ${options.method || 'GET'} ${url}`, {
        headers,
        body: options.body,
      });
    }

    try {
      const response = await fetch(url, config);

      // Clear timeout on successful request
      clearTimeout(timeoutId);

      // Handle different response types
      if (!response.ok) {
        await this.handleErrorResponse(response);
      }

      // Handle empty responses (like 204 No Content)
      if (response.status === 204) {
        return {} as T;
      }

      const data = await response.json();

      // Debug logging for response
      if (DEBUG_MODE) {
        console.log(`API Response: ${options.method || 'GET'} ${url}`, {
          status: response.status,
          data: data,
        });
      }

      return data;
    } catch (error) {
      // Clear timeout on error
      clearTimeout(timeoutId);

      if (error instanceof Error) {
        throw new ApiError(error.message, 0);
      }
      throw new ApiError('Network error occurred', 0);
    }
  }

  private async handleErrorResponse(response: Response): Promise<never> {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    
    try {
      const errorData = await response.json();
      if (errorData.error) {
        errorMessage = errorData.error;
      } else if (errorData.message) {
        errorMessage = errorData.message;
      }
    } catch {
      // If we can't parse the error response, use the default message
    }

    // Handle 401 Unauthorized - try to refresh token
    if (response.status === 401) {
      const refreshed = await this.tryRefreshToken();
      if (!refreshed) {
        TokenManager.clearTokens();
        // Redirect to login or emit auth event
        window.location.href = '/login';
      }
    }

    throw new ApiError(errorMessage, response.status);
  }

  private async tryRefreshToken(): Promise<boolean> {
    const refreshToken = TokenManager.getRefreshToken();
    if (!refreshToken) {
      return false;
    }

    try {
      const response = await fetch(`${this.baseURL}/auth/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: refreshToken }),
      });

      if (response.ok) {
        const data = await response.json();
        TokenManager.setAccessToken(data.access_token);
        return true;
      }
    } catch {
      // Refresh failed
    }

    return false;
  }

  // HTTP methods
  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// Create HTTP client instance
const httpClient = new HttpClient(`${API_BASE_URL}${API_VERSION}`);

// Export utilities
export { TokenManager, httpClient, ApiError };

// Health check function
export const checkHealth = async (): Promise<{ status: string; service: string }> => {
  return httpClient.get('/health');
};

// Export API base URL for other uses
export const getApiBaseUrl = () => `${API_BASE_URL}${API_VERSION}`;
