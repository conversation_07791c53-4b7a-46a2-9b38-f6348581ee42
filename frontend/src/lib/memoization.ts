import { useMemo, useCallback, useRef, useEffect, useState } from 'react';

// Generic memoization utility for expensive calculations
export function useMemoizedCalculation<T, Args extends any[]>(
  calculation: (...args: Args) => T,
  dependencies: Args,
  options?: {
    maxCacheSize?: number;
    ttl?: number; // Time to live in milliseconds
  }
): T {
  const { maxCacheSize = 10, ttl = 5 * 60 * 1000 } = options || {}; // Default 5 minutes TTL
  
  const cacheRef = useRef<Map<string, { value: T; timestamp: number }>>(new Map());
  
  return useMemo(() => {
    const key = JSON.stringify(dependencies);
    const now = Date.now();
    const cached = cacheRef.current.get(key);
    
    // Check if cached value exists and is not expired
    if (cached && (now - cached.timestamp) < ttl) {
      return cached.value;
    }
    
    // Calculate new value
    const result = calculation(...dependencies);
    
    // Store in cache
    cacheRef.current.set(key, { value: result, timestamp: now });
    
    // Clean up old entries if cache is too large
    if (cacheRef.current.size > maxCacheSize) {
      const entries = Array.from(cacheRef.current.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      // Remove oldest entries
      const toRemove = entries.slice(0, entries.length - maxCacheSize);
      toRemove.forEach(([key]) => cacheRef.current.delete(key));
    }
    
    return result;
  }, dependencies);
}

// Memoized analytics data transformations
export const useAnalyticsTransforms = () => {
  // Memoize performance trend calculations
  const calculateTrendDirection = useCallback((trends: any[]) => {
    if (trends.length < 2) return 'stable';
    
    const recent = trends.slice(-3);
    const older = trends.slice(0, 3);
    
    const recentAvg = recent.reduce((sum, t) => sum + (t.avg_score || 0), 0) / recent.length;
    const olderAvg = older.reduce((sum, t) => sum + (t.avg_score || 0), 0) / older.length;
    
    const difference = recentAvg - olderAvg;
    
    if (difference > 5) return 'improving';
    if (difference < -5) return 'declining';
    return 'stable';
  }, []);

  // Memoize subject performance calculations
  const calculateSubjectRankings = useCallback((subjectData: any[]) => {
    return subjectData
      .map(subject => ({
        ...subject,
        rank: subjectData.filter(s => s.avg_score > subject.avg_score).length + 1
      }))
      .sort((a, b) => b.avg_score - a.avg_score);
  }, []);

  // Memoize topic difficulty analysis
  const analyzeTopicDifficulty = useCallback((topics: any[]) => {
    const analysis = {
      easy: topics.filter(t => t.proficiency_score >= 80).length,
      medium: topics.filter(t => t.proficiency_score >= 60 && t.proficiency_score < 80).length,
      hard: topics.filter(t => t.proficiency_score < 60).length,
      total: topics.length
    };

    return {
      ...analysis,
      distribution: {
        easy: (analysis.easy / analysis.total) * 100,
        medium: (analysis.medium / analysis.total) * 100,
        hard: (analysis.hard / analysis.total) * 100
      }
    };
  }, []);

  // Memoize study pattern insights
  const generateStudyInsights = useCallback((patterns: any) => {
    if (!patterns) return [];

    const insights = [];

    // Consistency insights
    if (patterns.consistency_score < 50) {
      insights.push({
        type: 'warning',
        message: 'Your study consistency could be improved',
        suggestion: 'Try to study at the same time each day'
      });
    } else if (patterns.consistency_score > 80) {
      insights.push({
        type: 'success',
        message: 'Excellent study consistency!',
        suggestion: 'Keep up the great routine'
      });
    }

    // Session length insights
    if (patterns.average_session_time < 15) {
      insights.push({
        type: 'info',
        message: 'Consider longer study sessions',
        suggestion: 'Research shows 25-45 minute sessions are optimal'
      });
    } else if (patterns.average_session_time > 90) {
      insights.push({
        type: 'warning',
        message: 'Your study sessions might be too long',
        suggestion: 'Try breaking them into smaller chunks with breaks'
      });
    }

    // Streak insights
    if (patterns.current_streak >= 7) {
      insights.push({
        type: 'success',
        message: `Amazing ${patterns.current_streak}-day streak!`,
        suggestion: 'Your dedication is paying off'
      });
    } else if (patterns.current_streak === 0 && patterns.longest_streak > 5) {
      insights.push({
        type: 'motivation',
        message: 'Time to restart your streak',
        suggestion: `You previously achieved ${patterns.longest_streak} days`
      });
    }

    return insights;
  }, []);

  return {
    calculateTrendDirection,
    calculateSubjectRankings,
    analyzeTopicDifficulty,
    generateStudyInsights
  };
};

// Performance monitoring for analytics components
export const usePerformanceMonitor = (componentName: string) => {
  const renderCountRef = useRef(0);
  const lastRenderTimeRef = useRef(Date.now());
  
  renderCountRef.current += 1;
  
  const logPerformance = useCallback((operation: string, duration: number) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${componentName}] ${operation}: ${duration}ms`);
    }
  }, [componentName]);

  const measureOperation = useCallback(<T>(
    operation: string,
    fn: () => T
  ): T => {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    
    logPerformance(operation, duration);
    
    return result;
  }, [logPerformance]);

  const measureAsyncOperation = useCallback(async <T>(
    operation: string,
    fn: () => Promise<T>
  ): Promise<T> => {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    
    logPerformance(operation, duration);
    
    return result;
  }, [logPerformance]);

  // Log render performance in development
  if (process.env.NODE_ENV === 'development') {
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTimeRef.current;
    
    if (renderCountRef.current > 1) {
      console.log(`[${componentName}] Render #${renderCountRef.current}, ${timeSinceLastRender}ms since last render`);
    }
    
    lastRenderTimeRef.current = now;
  }

  return {
    renderCount: renderCountRef.current,
    measureOperation,
    measureAsyncOperation,
    logPerformance
  };
};

// Debounced analytics data fetching
export const useDebouncedAnalytics = (
  fetchFunction: () => Promise<any>,
  dependencies: any[],
  delay: number = 300
) => {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);

  const debouncedFetch = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const result = await fetchFunction();
        setData(result);
      } catch (err) {
        setError(err);
      } finally {
        setIsLoading(false);
      }
    }, delay);
  }, [fetchFunction, delay]);

  useEffect(() => {
    debouncedFetch();
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, dependencies);

  return { data, isLoading, error };
};

// Local storage cache for analytics data
export const useAnalyticsCache = (key: string, ttl: number = 5 * 60 * 1000) => {
  const getCachedData = useCallback(() => {
    try {
      const cached = localStorage.getItem(`analytics_${key}`);
      if (!cached) return null;

      const { data, timestamp } = JSON.parse(cached);
      const now = Date.now();

      if (now - timestamp > ttl) {
        localStorage.removeItem(`analytics_${key}`);
        return null;
      }

      return data;
    } catch {
      return null;
    }
  }, [key, ttl]);

  const setCachedData = useCallback((data: any) => {
    try {
      const cacheEntry = {
        data,
        timestamp: Date.now()
      };
      localStorage.setItem(`analytics_${key}`, JSON.stringify(cacheEntry));
    } catch {
      // Ignore localStorage errors
    }
  }, [key]);

  const clearCache = useCallback(() => {
    localStorage.removeItem(`analytics_${key}`);
  }, [key]);

  return {
    getCachedData,
    setCachedData,
    clearCache
  };
};

// Optimized chart data preparation
export const useOptimizedChartData = (rawData: any[], chartType: string) => {
  return useMemoizedCalculation(
    (data, type) => {
      switch (type) {
        case 'line':
          return data.map(item => ({
            x: item.date || item.period,
            y: item.score || item.value,
            label: item.label || item.name
          }));
        
        case 'bar':
          return data.map(item => ({
            category: item.category || item.name,
            value: item.value || item.score,
            color: item.color || '#3b82f6'
          }));
        
        case 'pie':
          const total = data.reduce((sum, item) => sum + (item.value || 0), 0);
          return data.map(item => ({
            label: item.label || item.name,
            value: item.value || 0,
            percentage: total > 0 ? ((item.value || 0) / total) * 100 : 0
          }));
        
        default:
          return data;
      }
    },
    [rawData, chartType],
    { maxCacheSize: 5, ttl: 2 * 60 * 1000 } // 2 minutes TTL for chart data
  );
};
