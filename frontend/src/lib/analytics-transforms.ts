import {
  PerformanceTrend,
  HeatmapData,
  SubjectComparison,
  AnalyticsInsight,
  StudyPattern,
  RecommendationItem,
} from './api-types';

// Transform performance trends for PerformanceChart component
export interface PerformanceChartData {
  date: string;
  Mathematics: number;
  Physics: number;
  Chemistry: number;
  Biology?: number;
  English?: number;
  [key: string]: string | number | undefined;
}

export const transformPerformanceTrends = (trends: PerformanceTrend[]): PerformanceChartData[] => {
  return trends.map(trend => {
    const data: PerformanceChartData = {
      date: trend.period,
      Mathematics: trend.subjects.Mathematics || 0,
      Physics: trend.subjects.Physics || 0,
      Chemistry: trend.subjects.Chemistry || 0,
    };

    // Add optional subjects if they exist
    if (trend.subjects.Biology) data.Biology = trend.subjects.Biology;
    if (trend.subjects.English) data.English = trend.subjects.English;

    return data;
  });
};

// Transform subject comparisons for SubjectRadarChart component
export interface RadarChartData {
  subject: string;
  current: number;
  target: number;
}

export const transformSubjectComparisons = (comparisons: SubjectComparison[]): RadarChartData[] => {
  return comparisons.map(comparison => ({
    subject: comparison.subject_name,
    current: Math.round(comparison.current_score),
    target: Math.round(comparison.target_score),
  }));
};

// Transform heatmap data for WeaknessHeatmap component
export interface HeatmapChartData {
  id: string;
  data: Array<{ x: string; y: number }>;
}

export const transformHeatmapData = (heatmapData: HeatmapData[]): HeatmapChartData[] => {
  return heatmapData.map(subject => ({
    id: subject.subject_name,
    data: subject.topics.map(topic => ({
      x: topic.topic_name,
      y: Math.round(topic.proficiency_score),
    })),
  }));
};

// Transform insights for display
export interface InsightDisplay {
  type: 'improvement' | 'concern' | 'achievement' | 'recommendation';
  title: string;
  description: string;
  action: string;
  icon: string; // Icon name for Lucide React
  color: string; // CSS class for color
  priority: number;
}

export const transformInsights = (insights: AnalyticsInsight[]): InsightDisplay[] => {
  return insights.map(insight => {
    let icon = 'Info';
    let color = 'text-primary';

    switch (insight.type) {
      case 'improvement':
        icon = 'TrendingUp';
        color = 'text-success';
        break;
      case 'concern':
        icon = 'TrendingDown';
        color = 'text-destructive';
        break;
      case 'achievement':
        icon = 'Award';
        color = 'text-warning';
        break;
      case 'recommendation':
        icon = 'Target';
        color = 'text-info';
        break;
    }

    return {
      type: insight.type,
      title: insight.title,
      description: insight.description,
      action: insight.action,
      icon,
      color,
      priority: insight.priority,
    };
  }).sort((a, b) => b.priority - a.priority); // Sort by priority descending
};

// Transform study patterns for display
export interface StudyPatternDisplay {
  optimalStudyTime: string;
  averageSessionTime: string;
  consistencyLevel: string;
  bestDay: string;
  currentStreak: number;
  longestStreak: number;
  weeklyFrequency: string;
  consistencyScore: number;
}

export const transformStudyPatterns = (patterns: StudyPattern): StudyPatternDisplay => {
  // Convert hour to readable time
  const formatHour = (hour: number): string => {
    if (hour === 0) return '12:00 AM';
    if (hour < 12) return `${hour}:00 AM`;
    if (hour === 12) return '12:00 PM';
    return `${hour - 12}:00 PM`;
  };

  // Convert minutes to readable duration
  const formatDuration = (minutes: number): string => {
    if (minutes < 60) return `${minutes} minutes`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
    return `${hours}h ${remainingMinutes}m`;
  };

  // Determine consistency level
  const getConsistencyLevel = (score: number): string => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Needs Improvement';
  };

  // Format weekly frequency
  const formatFrequency = (count: number): string => {
    if (count < 1) return 'Less than 1 test per week';
    if (count < 2) return 'About 1 test per week';
    return `${count.toFixed(1)} tests per week`;
  };

  return {
    optimalStudyTime: formatHour(patterns.optimal_study_hour),
    averageSessionTime: formatDuration(patterns.average_session_time),
    consistencyLevel: getConsistencyLevel(patterns.consistency_score),
    bestDay: patterns.best_performance_day,
    currentStreak: patterns.current_streak,
    longestStreak: patterns.longest_streak,
    weeklyFrequency: formatFrequency(patterns.weekly_test_count),
    consistencyScore: patterns.consistency_score,
  };
};

// Transform recommendations for display
export interface RecommendationDisplay {
  type: 'topic' | 'difficulty' | 'schedule';
  title: string;
  description: string;
  priority: number;
  reason: string;
  actionable: boolean;
  icon: string;
  color: string;
}

export const transformRecommendations = (recommendations: RecommendationItem[]): RecommendationDisplay[] => {
  return recommendations.map(rec => {
    let icon = 'Lightbulb';
    let color = 'text-primary';
    let actionable = true;

    switch (rec.type) {
      case 'topic':
        icon = 'BookOpen';
        color = 'text-blue-600';
        break;
      case 'difficulty':
        icon = 'Target';
        color = 'text-orange-600';
        break;
      case 'schedule':
        icon = 'Calendar';
        color = 'text-green-600';
        break;
    }

    return {
      type: rec.type,
      title: rec.title,
      description: rec.description,
      priority: rec.priority,
      reason: rec.reason,
      actionable,
      icon,
      color,
    };
  }).sort((a, b) => b.priority - a.priority);
};

// Calculate improvement percentage with proper formatting
export const formatImprovement = (improvement: number): string => {
  const abs = Math.abs(improvement);
  const sign = improvement >= 0 ? '+' : '-';
  return `${sign}${abs.toFixed(1)}%`;
};

// Format percentage with proper rounding
export const formatPercentage = (value: number): string => {
  return `${Math.round(value)}%`;
};

// Format large numbers for display
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
};

// Calculate time ago from date string
export const timeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  return date.toLocaleDateString();
};

// Generate fallback data for when API data is not available
export const generateFallbackData = () => ({
  performanceChart: [
    { date: "Week 1", Mathematics: 65, Physics: 72, Chemistry: 68 },
    { date: "Week 2", Mathematics: 68, Physics: 75, Chemistry: 71 },
    { date: "Week 3", Mathematics: 72, Physics: 78, Chemistry: 74 },
    { date: "Week 4", Mathematics: 75, Physics: 82, Chemistry: 78 }
  ],
  radarChart: [
    { subject: 'Mathematics', current: 75, target: 90 },
    { subject: 'Physics', current: 82, target: 85 },
    { subject: 'Chemistry', current: 78, target: 88 },
    { subject: 'Biology', current: 65, target: 80 },
    { subject: 'English', current: 88, target: 90 }
  ],
  heatmapChart: [
    {
      id: 'Mathematics',
      data: [
        { x: 'Algebra', y: 85 },
        { x: 'Geometry', y: 72 },
        { x: 'Trigonometry', y: 45 },
        { x: 'Calculus', y: 68 }
      ]
    },
    {
      id: 'Physics',
      data: [
        { x: 'Mechanics', y: 78 },
        { x: 'Thermodynamics', y: 52 },
        { x: 'Optics', y: 65 },
        { x: 'Electricity', y: 82 }
      ]
    },
    {
      id: 'Chemistry',
      data: [
        { x: 'Inorganic', y: 88 },
        { x: 'Organic', y: 38 },
        { x: 'Physical', y: 74 },
        { x: 'Analytical', y: 69 }
      ]
    }
  ]
});
