import { useState, useEffect } from 'react';

// Breakpoint definitions matching Tailwind CSS
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export type Breakpoint = keyof typeof breakpoints;

// Hook to get current screen size
export function useScreenSize() {
  const [screenSize, setScreenSize] = useState<{
    width: number;
    height: number;
    breakpoint: Breakpoint | 'xs';
  }>({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
    breakpoint: 'lg',
  });

  useEffect(() => {
    function updateScreenSize() {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      let breakpoint: Breakpoint | 'xs' = 'xs';
      if (width >= breakpoints['2xl']) breakpoint = '2xl';
      else if (width >= breakpoints.xl) breakpoint = 'xl';
      else if (width >= breakpoints.lg) breakpoint = 'lg';
      else if (width >= breakpoints.md) breakpoint = 'md';
      else if (width >= breakpoints.sm) breakpoint = 'sm';

      setScreenSize({ width, height, breakpoint });
    }

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  return screenSize;
}

// Hook to check if screen is at least a certain breakpoint
export function useBreakpoint(breakpoint: Breakpoint) {
  const { width } = useScreenSize();
  return width >= breakpoints[breakpoint];
}

// Hook for responsive values
export function useResponsiveValue<T>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
}): T | undefined {
  const { breakpoint } = useScreenSize();
  
  // Return the most specific value for current breakpoint
  if (breakpoint === '2xl' && values['2xl'] !== undefined) return values['2xl'];
  if ((breakpoint === '2xl' || breakpoint === 'xl') && values.xl !== undefined) return values.xl;
  if ((breakpoint === '2xl' || breakpoint === 'xl' || breakpoint === 'lg') && values.lg !== undefined) return values.lg;
  if ((breakpoint === '2xl' || breakpoint === 'xl' || breakpoint === 'lg' || breakpoint === 'md') && values.md !== undefined) return values.md;
  if (breakpoint !== 'xs' && values.sm !== undefined) return values.sm;
  return values.xs;
}

// Responsive grid configurations for analytics
export const analyticsGridConfigs = {
  dashboard: {
    xs: 'grid-cols-1',
    sm: 'grid-cols-2',
    md: 'grid-cols-2',
    lg: 'grid-cols-3',
    xl: 'grid-cols-4',
    '2xl': 'grid-cols-4',
  },
  charts: {
    xs: 'grid-cols-1',
    sm: 'grid-cols-1',
    md: 'grid-cols-2',
    lg: 'grid-cols-2',
    xl: 'grid-cols-3',
    '2xl': 'grid-cols-3',
  },
  insights: {
    xs: 'grid-cols-1',
    sm: 'grid-cols-1',
    md: 'grid-cols-1',
    lg: 'grid-cols-2',
    xl: 'grid-cols-2',
    '2xl': 'grid-cols-3',
  },
} as const;

// Hook to get responsive grid classes
export function useResponsiveGrid(type: keyof typeof analyticsGridConfigs) {
  const { breakpoint } = useScreenSize();
  const config = analyticsGridConfigs[type];
  
  return config[breakpoint] || config.xs;
}

// Responsive spacing utilities
export const responsiveSpacing = {
  container: {
    xs: 'px-4 py-4',
    sm: 'px-6 py-6',
    md: 'px-8 py-8',
    lg: 'px-12 py-12',
    xl: 'px-16 py-16',
    '2xl': 'px-20 py-20',
  },
  section: {
    xs: 'space-y-4',
    sm: 'space-y-6',
    md: 'space-y-8',
    lg: 'space-y-10',
    xl: 'space-y-12',
    '2xl': 'space-y-16',
  },
  card: {
    xs: 'p-4',
    sm: 'p-6',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-8',
    '2xl': 'p-10',
  },
} as const;

// Hook to get responsive spacing
export function useResponsiveSpacing(type: keyof typeof responsiveSpacing) {
  return useResponsiveValue(responsiveSpacing[type]);
}

// Chart responsive configurations
export const chartResponsiveConfigs = {
  height: {
    xs: 200,
    sm: 250,
    md: 300,
    lg: 350,
    xl: 400,
    '2xl': 450,
  },
  fontSize: {
    xs: 10,
    sm: 11,
    md: 12,
    lg: 13,
    xl: 14,
    '2xl': 15,
  },
  margin: {
    xs: { top: 20, right: 20, bottom: 40, left: 40 },
    sm: { top: 25, right: 25, bottom: 50, left: 50 },
    md: { top: 30, right: 30, bottom: 60, left: 60 },
    lg: { top: 35, right: 35, bottom: 70, left: 70 },
    xl: { top: 40, right: 40, bottom: 80, left: 80 },
    '2xl': { top: 45, right: 45, bottom: 90, left: 90 },
  },
} as const;

// Hook for responsive chart configuration
export function useResponsiveChart() {
  const height = useResponsiveValue(chartResponsiveConfigs.height) || 300;
  const fontSize = useResponsiveValue(chartResponsiveConfigs.fontSize) || 12;
  const margin = useResponsiveValue(chartResponsiveConfigs.margin) || chartResponsiveConfigs.margin.md;

  return { height, fontSize, margin };
}

// Mobile-first responsive utilities
export function isMobile() {
  return typeof window !== 'undefined' && window.innerWidth < breakpoints.md;
}

export function isTablet() {
  return typeof window !== 'undefined' && 
         window.innerWidth >= breakpoints.md && 
         window.innerWidth < breakpoints.lg;
}

export function isDesktop() {
  return typeof window !== 'undefined' && window.innerWidth >= breakpoints.lg;
}

// Touch device detection
export function isTouchDevice() {
  return typeof window !== 'undefined' && 
         ('ontouchstart' in window || navigator.maxTouchPoints > 0);
}

// Responsive text sizes for analytics
export const responsiveTextSizes = {
  title: {
    xs: 'text-lg',
    sm: 'text-xl',
    md: 'text-2xl',
    lg: 'text-3xl',
    xl: 'text-4xl',
    '2xl': 'text-5xl',
  },
  subtitle: {
    xs: 'text-sm',
    sm: 'text-base',
    md: 'text-lg',
    lg: 'text-xl',
    xl: 'text-2xl',
    '2xl': 'text-3xl',
  },
  body: {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-base',
    xl: 'text-lg',
    '2xl': 'text-xl',
  },
  caption: {
    xs: 'text-xs',
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-sm',
    xl: 'text-base',
    '2xl': 'text-base',
  },
} as const;

// Hook to get responsive text size
export function useResponsiveTextSize(type: keyof typeof responsiveTextSizes) {
  return useResponsiveValue(responsiveTextSizes[type]);
}

// Responsive layout helpers for analytics pages
export const analyticsLayouts = {
  sidebar: {
    // Whether to show sidebar
    show: {
      xs: false,
      sm: false,
      md: false,
      lg: true,
      xl: true,
      '2xl': true,
    },
    // Sidebar width
    width: {
      xs: 'w-0',
      sm: 'w-0',
      md: 'w-0',
      lg: 'w-64',
      xl: 'w-72',
      '2xl': 'w-80',
    },
  },
  content: {
    // Content area padding
    padding: {
      xs: 'p-4',
      sm: 'p-6',
      md: 'p-8',
      lg: 'p-10',
      xl: 'p-12',
      '2xl': 'p-16',
    },
    // Max width for content
    maxWidth: {
      xs: 'max-w-full',
      sm: 'max-w-full',
      md: 'max-w-full',
      lg: 'max-w-6xl',
      xl: 'max-w-7xl',
      '2xl': 'max-w-8xl',
    },
  },
} as const;

// Hook for responsive analytics layout
export function useAnalyticsLayout() {
  const showSidebar = useResponsiveValue(analyticsLayouts.sidebar.show) || false;
  const sidebarWidth = useResponsiveValue(analyticsLayouts.sidebar.width) || 'w-0';
  const contentPadding = useResponsiveValue(analyticsLayouts.content.padding) || 'p-4';
  const contentMaxWidth = useResponsiveValue(analyticsLayouts.content.maxWidth) || 'max-w-full';

  return {
    showSidebar,
    sidebarWidth,
    contentPadding,
    contentMaxWidth,
  };
}

// Responsive animation durations
export const responsiveAnimations = {
  duration: {
    xs: 150, // Faster on mobile for better perceived performance
    sm: 200,
    md: 250,
    lg: 300,
    xl: 350,
    '2xl': 400,
  },
  easing: {
    xs: 'ease-out',
    sm: 'ease-out',
    md: 'ease-in-out',
    lg: 'ease-in-out',
    xl: 'ease-in-out',
    '2xl': 'ease-in-out',
  },
} as const;

// Hook for responsive animations
export function useResponsiveAnimation() {
  const duration = useResponsiveValue(responsiveAnimations.duration) || 250;
  const easing = useResponsiveValue(responsiveAnimations.easing) || 'ease-in-out';

  return { duration, easing };
}
