// API Types - TypeScript definitions matching the Go backend models

export type DifficultyLevel = 'easy' | 'medium' | 'hard';
export type TestStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled';

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  full_name?: string;
  age?: number;
  board?: string;
  class?: string;
}

export interface RegisterResponse {
  user_id: string;
  email: string;
  full_name?: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface RefreshTokenResponse {
  access_token: string;
}

// User Types
export interface User {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  user_id: string;
  full_name?: string;
  age?: number;
  board?: string;
  class?: string;
  created_at: string;
  updated_at: string;
}

export interface UserWithProfile {
  user_id: string;
  email: string;
  full_name?: string;
  age?: number;
  board?: string;
  class?: string;
  created_at: string;
  updated_at: string;
}

// Content Types
export interface Subject {
  id: number;
  name: string;
  description?: string;
}

export interface Exam {
  id: number;
  name: string;
  description?: string;
}

export interface Topic {
  id: number;
  subject_id: number;
  name: string;
  description?: string;
}

// Question Types
export interface QuestionContent {
  question: string;
  options: string[];
  correct_option_index: number;
  explanation: string;
}

export interface Question {
  id: string;
  topic_id: number;
  content: QuestionContent;
  difficulty?: DifficultyLevel;
  author_ai_model?: string;
  created_at: string;
}

export interface QuestionWithTopic {
  id: string;
  topic_id: number;
  topic_name: string;
  content: QuestionContent;
  difficulty?: DifficultyLevel;
  author_ai_model?: string;
  created_at: string;
}

// Test Types
export interface Test {
  id: string;
  user_id: string;
  test_context_subject_id?: number;
  test_context_exam_id?: number;
  status: TestStatus;
  created_at: string;
  completed_at?: string;
}

export interface CreateTestRequest {
  subject_id?: number;
  exam_id?: number;
  num_questions: number;
  difficulty_levels: DifficultyLevel[];
  topic_ids?: number[];
  // Grade/Class information for age-appropriate question generation
  grade?: string;  // Student's grade/class (e.g., "5", "Class 10", "Grade 12")
  board?: string;  // Educational board (e.g., "CBSE", "ICSE", "State Board")
}

export interface TestWithFirstQuestion {
  test: Test;
  first_question: TestQuestionWithDetails;
}

export interface TestQuestionWithDetails {
  id: number;
  test_id: string;
  question_id: string;
  question_order: number;
  topic_id: number;
  topic_name: string;
  question: QuestionContent;
  difficulty?: DifficultyLevel;
}

export interface SubmitAnswerRequest {
  question_id: string;
  selected_option_index: number;
}

export interface SubmitAnswerResponse {
  is_correct: boolean;
  correct_option_index: number;
  explanation: string;
}

export interface CompleteTestResponse {
  test_id: string;
  message: string;
  analysis_url: string;
}

// Analytics Types
export interface TestResult {
  id: string;
  test_id: string;
  user_id: string;
  score: number;
  total_questions: number;
  correct_answers: number;
  incorrect_answers: number;
  time_taken_seconds: number;
}

export interface TopicPerformanceDetail {
  topic_id: number;
  topic_name: string;
  total_questions: number;
  correct_answers: number;
  accuracy_rate: number;
  proficiency_score?: number;
}

export interface QuestionAnalysisDetail {
  question_id: string;
  question_order: number;
  topic_name: string;
  difficulty?: DifficultyLevel;
  user_answer: number;
  correct_answer: number;
  is_correct: boolean;
  question: string;
  options: string[];
  explanation: string;
}

export interface TestAnalysis {
  test_id: string;
  score: number;
  total_questions: number;
  correct_answers: number;
  incorrect_answers: number;
  time_taken_seconds: number;
  subject?: string;
  exam?: string;
  strong_topics: TopicPerformanceDetail[];
  weak_topics: TopicPerformanceDetail[];
  question_breakdown: QuestionAnalysisDetail[];
}

export interface UserTestHistory {
  test_id: string;
  score: number;
  total_questions: number;
  completed_at: string;
  subject_name?: string;
  exam_name?: string;
}

export interface TopicPerformanceWithName {
  topic_id: number;
  topic_name: string;
  subject_name: string;
  total_attempted: number;
  total_correct: number;
  proficiency_score: number;
  last_tested_at?: string;
}

export interface DashboardSummary {
  overall_proficiency: number;
  recent_tests: UserTestHistory[];
  strongest_subject?: string;
  weakest_subject?: string;
  total_tests_taken: number;
  average_score: number;
  topic_performance: TopicPerformanceWithName[];
}

// New Advanced Analytics Types
export type TimeFilter = 'week' | 'month' | 'quarter' | 'year' | 'custom';

export interface DateRange {
  start_date: string; // YYYY-MM-DD format
  end_date: string;   // YYYY-MM-DD format
}

export interface TimeFilterRequest {
  filter: TimeFilter;
  date_range?: DateRange; // Only used when filter is "custom"
}

export interface PerformanceTrend {
  period: string;
  date: string;
  subjects: Record<string, number>;
  test_count: number;
}

export interface AnalyticsInsight {
  type: 'improvement' | 'concern' | 'achievement' | 'recommendation';
  title: string;
  description: string;
  action: string;
  priority: number;
  created_at: string;
  topic_id?: number;
  subject_name?: string;
}

export interface StudyPattern {
  optimal_study_hour: number;
  average_session_time: number;
  consistency_score: number;
  best_performance_day: string;
  preferred_difficulty: string;
  current_streak: number;
  longest_streak: number;
  weekly_test_count: number;
}

export interface RecommendationItem {
  type: 'topic' | 'difficulty' | 'schedule';
  title: string;
  description: string;
  priority: number;
  topic_id?: number;
  difficulty?: string;
  reason: string;
}

export interface HeatmapTopicPerformance {
  topic_name: string;
  proficiency_score: number;
  total_attempted: number;
  last_tested_at?: string;
}

export interface HeatmapData {
  subject_name: string;
  topics: HeatmapTopicPerformance[];
}

export interface SubjectComparison {
  subject_name: string;
  current_score: number;
  target_score: number;
  test_count: number;
  improvement: number;
  rank: number;
}

export interface AdvancedAnalytics {
  performance_trends: PerformanceTrend[];
  insights: AnalyticsInsight[];
  study_patterns: StudyPattern;
  recommendations: RecommendationItem[];
  heatmap_data: HeatmapData[];
  subject_comparisons: SubjectComparison[];
  overall_improvement: number;
  practice_frequency: number;
  best_subject: string;
}

// API Response wrapper
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// Error types
export class ApiError extends Error {
  public status: number;

  constructor(message: string, status: number) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
  }
}
