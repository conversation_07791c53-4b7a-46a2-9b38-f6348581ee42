import { QueryClient } from '@tanstack/react-query';

// Create a query client with optimized settings for analytics data
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache analytics data for 5 minutes by default
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: 2,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});

// Query keys for analytics data
export const analyticsQueryKeys = {
  all: ['analytics'] as const,
  dashboard: () => [...analyticsQueryKeys.all, 'dashboard'] as const,
  advanced: (timeFilter: string, dateRange?: string) => 
    [...analyticsQueryKeys.all, 'advanced', timeFilter, dateRange].filter(Boolean) as const,
  trends: (timeFilter: string, dateRange?: string) => 
    [...analyticsQueryKeys.all, 'trends', timeFilter, dateRange].filter(Boolean) as const,
  insights: () => [...analyticsQueryKeys.all, 'insights'] as const,
  patterns: () => [...analyticsQueryKeys.all, 'patterns'] as const,
  recommendations: () => [...analyticsQueryKeys.all, 'recommendations'] as const,
  heatmap: () => [...analyticsQueryKeys.all, 'heatmap'] as const,
  testAnalysis: (testId: string) => [...analyticsQueryKeys.all, 'test', testId] as const,
  topicAnalysis: (topicId: number) => [...analyticsQueryKeys.all, 'topic', topicId] as const,
};

// Cache invalidation helpers
export const invalidateAnalytics = {
  all: () => queryClient.invalidateQueries({ queryKey: analyticsQueryKeys.all }),
  dashboard: () => queryClient.invalidateQueries({ queryKey: analyticsQueryKeys.dashboard() }),
  advanced: () => queryClient.invalidateQueries({ 
    queryKey: analyticsQueryKeys.all, 
    predicate: (query) => query.queryKey.includes('advanced')
  }),
  trends: () => queryClient.invalidateQueries({ 
    queryKey: analyticsQueryKeys.all, 
    predicate: (query) => query.queryKey.includes('trends')
  }),
  insights: () => queryClient.invalidateQueries({ queryKey: analyticsQueryKeys.insights() }),
  patterns: () => queryClient.invalidateQueries({ queryKey: analyticsQueryKeys.patterns() }),
  recommendations: () => queryClient.invalidateQueries({ queryKey: analyticsQueryKeys.recommendations() }),
  heatmap: () => queryClient.invalidateQueries({ queryKey: analyticsQueryKeys.heatmap() }),
};

// Prefetch helpers for common analytics data
export const prefetchAnalytics = {
  dashboard: () => queryClient.prefetchQuery({
    queryKey: analyticsQueryKeys.dashboard(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  }),
  
  trendsForPeriod: (timeFilter: string, dateRange?: string) => queryClient.prefetchQuery({
    queryKey: analyticsQueryKeys.trends(timeFilter, dateRange),
    staleTime: 5 * 60 * 1000, // 5 minutes
  }),
};

// Cache management utilities
export const cacheUtils = {
  // Clear all analytics cache
  clearAnalyticsCache: () => {
    queryClient.removeQueries({ queryKey: analyticsQueryKeys.all });
  },
  
  // Get cached data without triggering a fetch
  getCachedDashboard: () => {
    return queryClient.getQueryData(analyticsQueryKeys.dashboard());
  },
  
  getCachedTrends: (timeFilter: string, dateRange?: string) => {
    return queryClient.getQueryData(analyticsQueryKeys.trends(timeFilter, dateRange));
  },
  
  // Set cache data manually (useful for optimistic updates)
  setCachedDashboard: (data: any) => {
    queryClient.setQueryData(analyticsQueryKeys.dashboard(), data);
  },
  
  // Check if data is cached and fresh
  isCached: (queryKey: readonly unknown[]) => {
    const query = queryClient.getQueryState(queryKey);
    return query?.status === 'success' && !query.isStale;
  },
  
  // Get cache statistics
  getCacheStats: () => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    const analyticsQueries = queries.filter(q => 
      q.queryKey[0] === 'analytics'
    );
    
    return {
      totalQueries: queries.length,
      analyticsQueries: analyticsQueries.length,
      staleQueries: analyticsQueries.filter(q => q.isStale()).length,
      errorQueries: analyticsQueries.filter(q => q.state.status === 'error').length,
      loadingQueries: analyticsQueries.filter(q => q.state.status === 'pending').length,
    };
  },
};
