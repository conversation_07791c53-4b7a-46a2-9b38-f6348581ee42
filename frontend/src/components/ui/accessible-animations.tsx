import React, { useEffect, useState } from 'react';
import { useReducedMotion } from '@/hooks/useAccessibility';
import { cn } from '@/lib/utils';

// Accessible fade transition
export const FadeTransition: React.FC<{
  show: boolean;
  children: React.ReactNode;
  duration?: number;
  className?: string;
}> = ({ show, children, duration = 300, className }) => {
  const prefersReducedMotion = useReducedMotion();
  const [shouldRender, setShouldRender] = useState(show);

  useEffect(() => {
    if (show) {
      setShouldRender(true);
    } else {
      const timer = setTimeout(() => setShouldRender(false), prefersReducedMotion ? 0 : duration);
      return () => clearTimeout(timer);
    }
  }, [show, duration, prefersReducedMotion]);

  if (!shouldRender) return null;

  return (
    <div
      className={cn(
        'transition-opacity',
        prefersReducedMotion ? '' : `duration-${duration}`,
        show ? 'opacity-100' : 'opacity-0',
        className
      )}
      style={{
        transitionDuration: prefersReducedMotion ? '0ms' : `${duration}ms`,
      }}
    >
      {children}
    </div>
  );
};

// Accessible slide transition
export const SlideTransition: React.FC<{
  show: boolean;
  children: React.ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right';
  duration?: number;
  className?: string;
}> = ({ show, children, direction = 'up', duration = 300, className }) => {
  const prefersReducedMotion = useReducedMotion();
  const [shouldRender, setShouldRender] = useState(show);

  useEffect(() => {
    if (show) {
      setShouldRender(true);
    } else {
      const timer = setTimeout(() => setShouldRender(false), prefersReducedMotion ? 0 : duration);
      return () => clearTimeout(timer);
    }
  }, [show, duration, prefersReducedMotion]);

  if (!shouldRender) return null;

  const getTransform = () => {
    if (prefersReducedMotion) return '';
    
    const transforms = {
      up: show ? 'translateY(0)' : 'translateY(100%)',
      down: show ? 'translateY(0)' : 'translateY(-100%)',
      left: show ? 'translateX(0)' : 'translateX(100%)',
      right: show ? 'translateX(0)' : 'translateX(-100%)',
    };
    
    return transforms[direction];
  };

  return (
    <div
      className={cn(
        'transition-transform',
        prefersReducedMotion ? '' : `duration-${duration}`,
        className
      )}
      style={{
        transform: getTransform(),
        transitionDuration: prefersReducedMotion ? '0ms' : `${duration}ms`,
      }}
    >
      {children}
    </div>
  );
};

// Accessible scale transition
export const ScaleTransition: React.FC<{
  show: boolean;
  children: React.ReactNode;
  duration?: number;
  className?: string;
}> = ({ show, children, duration = 200, className }) => {
  const prefersReducedMotion = useReducedMotion();
  const [shouldRender, setShouldRender] = useState(show);

  useEffect(() => {
    if (show) {
      setShouldRender(true);
    } else {
      const timer = setTimeout(() => setShouldRender(false), prefersReducedMotion ? 0 : duration);
      return () => clearTimeout(timer);
    }
  }, [show, duration, prefersReducedMotion]);

  if (!shouldRender) return null;

  return (
    <div
      className={cn(
        'transition-transform origin-center',
        prefersReducedMotion ? '' : `duration-${duration}`,
        className
      )}
      style={{
        transform: prefersReducedMotion ? 'scale(1)' : show ? 'scale(1)' : 'scale(0.95)',
        transitionDuration: prefersReducedMotion ? '0ms' : `${duration}ms`,
      }}
    >
      {children}
    </div>
  );
};

// Accessible progress animation
export const ProgressAnimation: React.FC<{
  value: number;
  max?: number;
  duration?: number;
  className?: string;
  showLabel?: boolean;
  label?: string;
}> = ({ value, max = 100, duration = 1000, className, showLabel = true, label }) => {
  const prefersReducedMotion = useReducedMotion();
  const [animatedValue, setAnimatedValue] = useState(0);

  useEffect(() => {
    if (prefersReducedMotion) {
      setAnimatedValue(value);
      return;
    }

    const startTime = Date.now();
    const startValue = animatedValue;
    const targetValue = Math.min(value, max);
    const difference = targetValue - startValue;

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Easing function for smooth animation
      const easeOutCubic = 1 - Math.pow(1 - progress, 3);
      const currentValue = startValue + (difference * easeOutCubic);
      
      setAnimatedValue(currentValue);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [value, max, duration, prefersReducedMotion, animatedValue]);

  const percentage = (animatedValue / max) * 100;

  return (
    <div className={cn('w-full', className)}>
      {showLabel && (
        <div className="flex justify-between text-sm mb-1">
          <span>{label || 'Progress'}</span>
          <span>{Math.round(percentage)}%</span>
        </div>
      )}
      <div
        className="w-full bg-gray-200 rounded-full h-2 overflow-hidden"
        role="progressbar"
        aria-valuenow={Math.round(animatedValue)}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-label={label || 'Progress indicator'}
      >
        <div
          className="h-full bg-blue-600 rounded-full transition-all ease-out"
          style={{
            width: `${percentage}%`,
            transitionDuration: prefersReducedMotion ? '0ms' : '300ms',
          }}
        />
      </div>
    </div>
  );
};

// Accessible loading spinner
export const AccessibleSpinner: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  label?: string;
  className?: string;
}> = ({ size = 'md', label = 'Loading', className }) => {
  const prefersReducedMotion = useReducedMotion();
  
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return (
    <div
      className={cn(
        'inline-flex items-center justify-center',
        sizeClasses[size],
        className
      )}
      role="status"
      aria-label={label}
    >
      <div
        className={cn(
          'border-2 border-gray-300 border-t-blue-600 rounded-full',
          sizeClasses[size],
          prefersReducedMotion ? '' : 'animate-spin'
        )}
        style={{
          animation: prefersReducedMotion ? 'none' : undefined,
        }}
      />
      <span className="sr-only">{label}</span>
    </div>
  );
};

// Accessible notification animation
export const NotificationAnimation: React.FC<{
  show: boolean;
  children: React.ReactNode;
  position?: 'top' | 'bottom';
  onAnimationComplete?: () => void;
}> = ({ show, children, position = 'top', onAnimationComplete }) => {
  const prefersReducedMotion = useReducedMotion();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (show) {
      setIsVisible(true);
    } else {
      const timer = setTimeout(() => {
        setIsVisible(false);
        onAnimationComplete?.();
      }, prefersReducedMotion ? 0 : 300);
      return () => clearTimeout(timer);
    }
  }, [show, prefersReducedMotion, onAnimationComplete]);

  if (!isVisible) return null;

  const slideDirection = position === 'top' ? 'down' : 'up';

  return (
    <div
      className={cn(
        'fixed z-50 left-1/2 transform -translate-x-1/2',
        position === 'top' ? 'top-4' : 'bottom-4',
        'transition-all duration-300 ease-in-out',
        prefersReducedMotion ? '' : show ? 'translate-y-0 opacity-100' : 
          position === 'top' ? '-translate-y-full opacity-0' : 'translate-y-full opacity-0'
      )}
      style={{
        transitionDuration: prefersReducedMotion ? '0ms' : '300ms',
      }}
      role="alert"
      aria-live="polite"
    >
      {children}
    </div>
  );
};

// Accessible hover effects
export const AccessibleHover: React.FC<{
  children: React.ReactNode;
  className?: string;
  hoverClassName?: string;
  focusClassName?: string;
  disabled?: boolean;
}> = ({ children, className, hoverClassName, focusClassName, disabled = false }) => {
  const prefersReducedMotion = useReducedMotion();
  
  return (
    <div
      className={cn(
        className,
        !disabled && !prefersReducedMotion && 'transition-all duration-200',
        !disabled && hoverClassName && `hover:${hoverClassName}`,
        !disabled && focusClassName && `focus-within:${focusClassName}`,
        disabled && 'opacity-50 cursor-not-allowed'
      )}
      style={{
        transitionDuration: prefersReducedMotion ? '0ms' : '200ms',
      }}
    >
      {children}
    </div>
  );
};

// Accessible card animation
export const AnimatedCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  delay?: number;
  index?: number;
}> = ({ children, className, delay = 0, index = 0 }) => {
  const prefersReducedMotion = useReducedMotion();
  const [isVisible, setIsVisible] = useState(prefersReducedMotion);

  useEffect(() => {
    if (prefersReducedMotion) return;

    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay + (index * 100));

    return () => clearTimeout(timer);
  }, [delay, index, prefersReducedMotion]);

  return (
    <div
      className={cn(
        'transition-all duration-500 ease-out',
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4',
        className
      )}
      style={{
        transitionDuration: prefersReducedMotion ? '0ms' : '500ms',
      }}
    >
      {children}
    </div>
  );
};

// Accessible focus ring
export const FocusRing: React.FC<{
  children: React.ReactNode;
  className?: string;
  visible?: boolean;
}> = ({ children, className, visible = true }) => {
  return (
    <div
      className={cn(
        visible && 'focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2',
        'rounded transition-shadow duration-200',
        className
      )}
    >
      {children}
    </div>
  );
};
