import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { 
  Loader2, 
  AlertCircle, 
  Refresh<PERSON>w, 
  Wifi,
  WifiOff,
  Clock
} from 'lucide-react';

// Loading Spinner Component
export const LoadingSpinner: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}> = ({ size = 'md', text, className = '' }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  return (
    <div className={`flex items-center justify-center gap-2 ${className}`}>
      <Loader2 className={`animate-spin ${sizeClasses[size]}`} />
      {text && <span className="text-sm text-muted-foreground">{text}</span>}
    </div>
  );
};

// Card Loading Skeleton
export const CardSkeleton: React.FC<{
  showHeader?: boolean;
  lines?: number;
  className?: string;
}> = ({ showHeader = true, lines = 3, className = '' }) => (
  <Card className={className}>
    {showHeader && (
      <CardHeader>
        <Skeleton className="h-6 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </CardHeader>
    )}
    <CardContent className="space-y-3">
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton key={i} className="h-4 w-full" />
      ))}
    </CardContent>
  </Card>
);

// Dashboard Loading State
export const DashboardSkeleton: React.FC = () => (
  <div className="space-y-6">
    {/* Header Skeleton */}
    <div className="space-y-2">
      <Skeleton className="h-8 w-1/3" />
      <Skeleton className="h-4 w-1/2" />
    </div>

    {/* Metrics Grid Skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <CardSkeleton key={i} showHeader={false} lines={2} />
      ))}
    </div>

    {/* Charts Skeleton */}
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <CardSkeleton lines={6} />
      <CardSkeleton lines={6} />
    </div>
  </div>
);

// Chat Loading State
export const ChatSkeleton: React.FC = () => (
  <div className="space-y-4">
    {/* AI Message */}
    <div className="flex gap-3">
      <Skeleton className="h-8 w-8 rounded-full" />
      <div className="space-y-2 flex-1">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </div>
    </div>

    {/* User Message */}
    <div className="flex gap-3 justify-end">
      <div className="space-y-2 flex-1 max-w-xs">
        <Skeleton className="h-4 w-full ml-auto" />
        <Skeleton className="h-4 w-2/3 ml-auto" />
      </div>
      <Skeleton className="h-8 w-8 rounded-full" />
    </div>

    {/* AI Message */}
    <div className="flex gap-3">
      <Skeleton className="h-8 w-8 rounded-full" />
      <div className="space-y-2 flex-1">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/3" />
      </div>
    </div>
  </div>
);

// Error Fallback Component
export const ErrorFallback: React.FC<{
  error?: Error;
  resetError?: () => void;
  title?: string;
  description?: string;
  showRetry?: boolean;
}> = ({ 
  error, 
  resetError, 
  title = "Something went wrong",
  description = "An unexpected error occurred. Please try again.",
  showRetry = true 
}) => (
  <Card className="w-full max-w-md mx-auto">
    <CardContent className="p-6 text-center">
      <div className="p-3 bg-red-100 rounded-full w-fit mx-auto mb-4">
        <AlertCircle className="h-6 w-6 text-red-600" />
      </div>
      
      <h3 className="font-semibold text-lg mb-2">{title}</h3>
      <p className="text-muted-foreground text-sm mb-4">{description}</p>
      
      {error && process.env.NODE_ENV === 'development' && (
        <details className="text-left mb-4">
          <summary className="cursor-pointer text-sm font-medium">
            Error Details
          </summary>
          <pre className="text-xs bg-gray-100 p-2 rounded mt-2 overflow-auto">
            {error.message}
          </pre>
        </details>
      )}
      
      {showRetry && resetError && (
        <Button onClick={resetError} className="w-full">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      )}
    </CardContent>
  </Card>
);

// Network Status Component
export const NetworkStatus: React.FC<{
  isOnline?: boolean;
  onRetry?: () => void;
}> = ({ isOnline = navigator.onLine, onRetry }) => {
  if (isOnline) return null;

  return (
    <div className="fixed top-0 left-0 right-0 bg-red-600 text-white p-2 text-center text-sm z-50">
      <div className="flex items-center justify-center gap-2">
        <WifiOff className="h-4 w-4" />
        <span>You're offline. Some features may not work.</span>
        {onRetry && (
          <Button 
            size="sm" 
            variant="ghost" 
            onClick={onRetry}
            className="text-white hover:bg-red-700 h-6 px-2"
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
};

// Timeout Component
export const TimeoutFallback: React.FC<{
  onRetry?: () => void;
  timeout?: number;
}> = ({ onRetry, timeout = 30 }) => (
  <Card className="w-full max-w-md mx-auto">
    <CardContent className="p-6 text-center">
      <div className="p-3 bg-orange-100 rounded-full w-fit mx-auto mb-4">
        <Clock className="h-6 w-6 text-orange-600" />
      </div>
      
      <h3 className="font-semibold text-lg mb-2">Request Timeout</h3>
      <p className="text-muted-foreground text-sm mb-4">
        The request took longer than {timeout} seconds to complete.
      </p>
      
      {onRetry && (
        <Button onClick={onRetry} className="w-full">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      )}
    </CardContent>
  </Card>
);

// Empty State Component
export const EmptyState: React.FC<{
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: React.ReactNode;
}> = ({ icon, title, description, action }) => (
  <div className="text-center py-12">
    {icon && (
      <div className="p-3 bg-muted rounded-full w-fit mx-auto mb-4">
        {icon}
      </div>
    )}
    
    <h3 className="font-semibold text-lg mb-2">{title}</h3>
    {description && (
      <p className="text-muted-foreground text-sm mb-4 max-w-sm mx-auto">
        {description}
      </p>
    )}
    
    {action}
  </div>
);

// Retry Wrapper Component
export const RetryWrapper: React.FC<{
  children: React.ReactNode;
  onRetry: () => void;
  maxRetries?: number;
  retryDelay?: number;
}> = ({ children, onRetry, maxRetries = 3, retryDelay = 1000 }) => {
  const [retryCount, setRetryCount] = React.useState(0);
  const [isRetrying, setIsRetrying] = React.useState(false);

  const handleRetry = async () => {
    if (retryCount >= maxRetries) return;
    
    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    
    await new Promise(resolve => setTimeout(resolve, retryDelay));
    
    try {
      await onRetry();
    } finally {
      setIsRetrying(false);
    }
  };

  if (isRetrying) {
    return <LoadingSpinner text={`Retrying... (${retryCount}/${maxRetries})`} />;
  }

  return (
    <div>
      {children}
      {retryCount > 0 && retryCount < maxRetries && (
        <div className="text-center mt-4">
          <Button variant="outline" onClick={handleRetry} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry ({retryCount}/{maxRetries})
          </Button>
        </div>
      )}
    </div>
  );
};
