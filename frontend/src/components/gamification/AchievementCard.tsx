import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, Lock } from 'lucide-react';
import { Achievement } from '@/stores/gamificationStore';

interface AchievementCardProps {
  achievement: Achievement;
  size?: 'sm' | 'md' | 'lg';
  showProgress?: boolean;
  progress?: number;
}

export const AchievementCard: React.FC<AchievementCardProps> = ({
  achievement,
  size = 'md',
  showProgress = false,
  progress = 0,
}) => {
  const sizeClasses = {
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
  };

  const iconSizes = {
    sm: 'text-2xl',
    md: 'text-3xl',
    lg: 'text-4xl',
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'demo': return 'bg-blue-100 text-blue-800';
      case 'learning': return 'bg-green-100 text-green-800';
      case 'social': return 'bg-purple-100 text-purple-800';
      case 'milestone': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className={`
      relative transition-all duration-200 hover:shadow-md
      ${achievement.unlocked 
        ? 'border-green-200 bg-green-50/50' 
        : 'border-border bg-muted/20'
      }
    `}>
      <CardContent className={sizeClasses[size]}>
        <div className="flex items-start gap-3">
          {/* Icon */}
          <div className={`
            flex-shrink-0 ${iconSizes[size]}
            ${achievement.unlocked ? 'grayscale-0' : 'grayscale opacity-50'}
          `}>
            {achievement.icon}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className={`
                font-semibold truncate
                ${size === 'sm' ? 'text-sm' : size === 'md' ? 'text-base' : 'text-lg'}
                ${achievement.unlocked ? 'text-foreground' : 'text-muted-foreground'}
              `}>
                {achievement.name}
              </h3>
              
              {achievement.unlocked && (
                <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
              )}
              
              {!achievement.unlocked && (
                <Lock className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              )}
            </div>

            <p className={`
              text-muted-foreground mb-2
              ${size === 'sm' ? 'text-xs' : 'text-sm'}
            `}>
              {achievement.description}
            </p>

            {/* Progress bar for locked achievements */}
            {!achievement.unlocked && showProgress && progress > 0 && (
              <div className="mb-2">
                <Progress value={progress} className="h-2" />
                <p className="text-xs text-muted-foreground mt-1">
                  {progress}% complete
                </p>
              </div>
            )}

            {/* Footer */}
            <div className="flex items-center justify-between">
              <Badge className={getCategoryColor(achievement.category)}>
                {achievement.category}
              </Badge>
              
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  +{achievement.xpReward} XP
                </Badge>
                
                {achievement.unlocked && achievement.unlockedAt && (
                  <span className="text-xs text-muted-foreground">
                    {new Date(achievement.unlockedAt).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Unlock animation overlay */}
        {achievement.unlocked && (
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-2 right-2">
              <div className="animate-pulse">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

interface AchievementGridProps {
  achievements: Achievement[];
  title?: string;
  showUnlockedOnly?: boolean;
  maxItems?: number;
}

export const AchievementGrid: React.FC<AchievementGridProps> = ({
  achievements,
  title = "Achievements",
  showUnlockedOnly = false,
  maxItems,
}) => {
  const filteredAchievements = showUnlockedOnly 
    ? achievements.filter(a => a.unlocked)
    : achievements;

  const displayAchievements = maxItems 
    ? filteredAchievements.slice(0, maxItems)
    : filteredAchievements;

  const unlockedCount = achievements.filter(a => a.unlocked).length;
  const totalCount = achievements.length;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{title}</h3>
        <Badge variant="outline">
          {unlockedCount}/{totalCount} unlocked
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {displayAchievements.map((achievement) => (
          <AchievementCard
            key={achievement.id}
            achievement={achievement}
            size="md"
          />
        ))}
      </div>

      {maxItems && filteredAchievements.length > maxItems && (
        <p className="text-center text-muted-foreground text-sm">
          And {filteredAchievements.length - maxItems} more...
        </p>
      )}
    </div>
  );
};

interface RecentAchievementsProps {
  achievements: Achievement[];
  maxItems?: number;
}

export const RecentAchievements: React.FC<RecentAchievementsProps> = ({
  achievements,
  maxItems = 3,
}) => {
  const recentUnlocked = achievements
    .filter(a => a.unlocked && a.unlockedAt)
    .sort((a, b) => new Date(b.unlockedAt!).getTime() - new Date(a.unlockedAt!).getTime())
    .slice(0, maxItems);

  if (recentUnlocked.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <Lock className="h-8 w-8 mx-auto mb-2 opacity-50" />
        <p>No achievements unlocked yet</p>
        <p className="text-sm">Complete demos to start earning achievements!</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <h4 className="font-medium">Recently Unlocked</h4>
      {recentUnlocked.map((achievement) => (
        <AchievementCard
          key={achievement.id}
          achievement={achievement}
          size="sm"
        />
      ))}
    </div>
  );
};
