import React from 'react';
import { render, screen, fireEvent } from '@/test/test-utils';
import { AchievementCard, AchievementGrid } from '../AchievementCard';
import { createMockAchievement } from '@/test/test-utils';

describe('AchievementCard', () => {
  const mockAchievement = createMockAchievement({
    name: 'Test Achievement',
    description: 'A test achievement description',
    icon: '🏆',
    unlocked: true,
  });

  it('renders achievement information correctly', () => {
    render(<AchievementCard achievement={mockAchievement} />);
    
    expect(screen.getByText('Test Achievement')).toBeInTheDocument();
    expect(screen.getByText('A test achievement description')).toBeInTheDocument();
    expect(screen.getByText('🏆')).toBeInTheDocument();
  });

  it('shows unlocked state correctly', () => {
    render(<AchievementCard achievement={mockAchievement} />);
    
    expect(screen.getByText('Unlocked!')).toBeInTheDocument();
  });

  it('shows locked state correctly', () => {
    const lockedAchievement = createMockAchievement({
      unlocked: false,
    });
    
    render(<AchievementCard achievement={lockedAchievement} />);
    
    expect(screen.getByText('Locked')).toBeInTheDocument();
  });

  it('displays XP reward', () => {
    render(<AchievementCard achievement={mockAchievement} />);
    
    expect(screen.getByText('100 XP')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const onClickMock = jest.fn();
    render(<AchievementCard achievement={mockAchievement} onClick={onClickMock} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(onClickMock).toHaveBeenCalledWith(mockAchievement);
  });
});

describe('AchievementGrid', () => {
  const mockAchievements = [
    createMockAchievement({ id: '1', name: 'Achievement 1', unlocked: true }),
    createMockAchievement({ id: '2', name: 'Achievement 2', unlocked: false }),
    createMockAchievement({ id: '3', name: 'Achievement 3', unlocked: true }),
  ];

  it('renders all achievements', () => {
    render(<AchievementGrid achievements={mockAchievements} />);
    
    expect(screen.getByText('Achievement 1')).toBeInTheDocument();
    expect(screen.getByText('Achievement 2')).toBeInTheDocument();
    expect(screen.getByText('Achievement 3')).toBeInTheDocument();
  });

  it('shows correct unlock count', () => {
    render(<AchievementGrid achievements={mockAchievements} />);
    
    expect(screen.getByText('2/3 Unlocked')).toBeInTheDocument();
  });

  it('filters achievements by category', () => {
    const achievementsWithCategories = [
      createMockAchievement({ id: '1', name: 'Demo Achievement', category: 'demo' }),
      createMockAchievement({ id: '2', name: 'Learning Achievement', category: 'learning' }),
    ];
    
    render(<AchievementGrid achievements={achievementsWithCategories} filterCategory="demo" />);
    
    expect(screen.getByText('Demo Achievement')).toBeInTheDocument();
    expect(screen.queryByText('Learning Achievement')).not.toBeInTheDocument();
  });

  it('limits displayed achievements when maxItems is set', () => {
    render(<AchievementGrid achievements={mockAchievements} maxItems={2} />);
    
    expect(screen.getByText('Achievement 1')).toBeInTheDocument();
    expect(screen.getByText('Achievement 2')).toBeInTheDocument();
    expect(screen.queryByText('Achievement 3')).not.toBeInTheDocument();
  });

  it('shows empty state when no achievements', () => {
    render(<AchievementGrid achievements={[]} />);
    
    expect(screen.getByText('No achievements yet')).toBeInTheDocument();
  });
});
