import React from 'react';
import { render, screen } from '@/test/test-utils';
import { XPProgress } from '../XPProgress';

describe('XPProgress', () => {
  it('renders XP progress correctly', () => {
    render(<XPProgress />);
    
    expect(screen.getByText('Level 5')).toBeInTheDocument();
    expect(screen.getByText('1,000 XP')).toBeInTheDocument();
  });

  it('shows detailed view when showDetails is true', () => {
    render(<XPProgress showDetails={true} />);
    
    expect(screen.getByText('Experience Points')).toBeInTheDocument();
    expect(screen.getByText('Current Level')).toBeInTheDocument();
  });

  it('calculates progress percentage correctly', () => {
    render(<XPProgress showDetails={true} />);
    
    // Should show progress bar
    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toBeInTheDocument();
  });

  it('displays next level information', () => {
    render(<XPProgress showDetails={true} />);
    
    // Should show XP needed for next level
    expect(screen.getByText(/XP to Level/)).toBeInTheDocument();
  });

  it('handles compact mode', () => {
    render(<XPProgress compact={true} />);
    
    // In compact mode, should still show level and XP
    expect(screen.getByText('Level 5')).toBeInTheDocument();
    expect(screen.getByText('1,000 XP')).toBeInTheDocument();
  });

  it('shows level up animation when appropriate', () => {
    // This would test the animation trigger
    // In a real test, you might mock the animation or check for specific classes
    render(<XPProgress showDetails={true} />);
    
    expect(screen.getByText('Level 5')).toBeInTheDocument();
  });
});
