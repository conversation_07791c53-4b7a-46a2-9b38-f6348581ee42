import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Trophy, Zap, Star, X, CheckCircle } from 'lucide-react';
import { useGamificationStore, Achievement } from '@/stores/gamificationStore';
import { useToast } from '@/hooks/use-toast';

interface NotificationProps {
  id: string;
  type: 'xp_gained' | 'achievement_unlocked' | 'level_up';
  title: string;
  message: string;
  xp?: number;
  achievement?: Achievement;
  newLevel?: number;
  onClose: (id: string) => void;
}

const NotificationItem: React.FC<NotificationProps> = ({
  id,
  type,
  title,
  message,
  xp,
  achievement,
  newLevel,
  onClose,
}) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Auto-close after different durations based on type
    const duration = type === 'achievement_unlocked' ? 8000 : type === 'level_up' ? 6000 : 4000;

    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(() => onClose(id), 300);
    }, duration);

    return () => clearTimeout(timer);
  }, [id, onClose, type]);

  const getIcon = () => {
    switch (type) {
      case 'xp_gained':
        return <Zap className="h-5 w-5 text-blue-500" />;
      case 'achievement_unlocked':
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 'level_up':
        return <Star className="h-5 w-5 text-purple-500" />;
      default:
        return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'xp_gained':
        return 'bg-blue-50 border-blue-200 shadow-lg shadow-blue-100';
      case 'achievement_unlocked':
        return 'bg-gradient-to-r from-yellow-50 via-orange-50 to-yellow-50 border-yellow-200 shadow-lg shadow-yellow-100 animate-pulse';
      case 'level_up':
        return 'bg-gradient-to-r from-purple-50 via-pink-50 to-purple-50 border-purple-200 shadow-lg shadow-purple-100';
      default:
        return 'bg-green-50 border-green-200 shadow-lg shadow-green-100';
    }
  };

  return (
    <div
      className={`
        transition-all duration-300 transform
        ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
      `}
    >
      <Card className={`${getBackgroundColor()} shadow-lg`}>
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              {getIcon()}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-semibold text-sm">{title}</h4>
                
                {xp && (
                  <Badge className="bg-blue-100 text-blue-800 text-xs">
                    +{xp} XP
                  </Badge>
                )}
                
                {newLevel && (
                  <Badge className="bg-purple-100 text-purple-800 text-xs">
                    Level {newLevel}
                  </Badge>
                )}
              </div>
              
              <p className="text-sm text-muted-foreground">{message}</p>
              
              {achievement && (
                <div className="flex items-center gap-2 mt-2 p-2 bg-white/50 rounded border">
                  <span className="text-2xl animate-bounce">{achievement.icon}</span>
                  <div>
                    <p className="font-bold text-sm text-yellow-800">{achievement.name}</p>
                    <p className="text-xs text-yellow-700">
                      {achievement.description}
                    </p>
                    <Badge className="bg-yellow-500 text-white text-xs mt-1">
                      +{achievement.xpReward} XP Bonus!
                    </Badge>
                  </div>
                </div>
              )}
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setIsVisible(false);
                setTimeout(() => onClose(id), 300);
              }}
              className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export const GamificationNotifications: React.FC = () => {
  const [notifications, setNotifications] = useState<NotificationProps[]>([]);
  const { recentActivity } = useGamificationStore();

  useEffect(() => {
    // Convert recent activity to notifications
    const newNotifications = recentActivity
      .slice(0, 3) // Show only the 3 most recent
      .map((activity) => ({
        id: activity.id,
        type: activity.type,
        title: getNotificationTitle(activity.type),
        message: activity.message,
        xp: activity.xp,
        achievement: activity.achievement,
        newLevel: activity.type === 'level_up' ? extractLevelFromMessage(activity.message) : undefined,
        onClose: removeNotification,
      }));

    setNotifications(newNotifications);
  }, [recentActivity]);

  const getNotificationTitle = (type: string): string => {
    switch (type) {
      case 'xp_gained':
        return 'XP Gained!';
      case 'achievement_unlocked':
        return 'Achievement Unlocked!';
      case 'level_up':
        return 'Level Up!';
      case 'demo_completed':
        return 'Demo Completed!';
      default:
        return 'Notification';
    }
  };

  const extractLevelFromMessage = (message: string): number | undefined => {
    const match = message.match(/level (\d+)/i);
    return match ? parseInt(match[1], 10) : undefined;
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-3 max-w-sm">
      {notifications.map((notification) => (
        <NotificationItem key={notification.id} {...notification} />
      ))}
    </div>
  );
};

// Hook for triggering notifications programmatically
export const useGamificationNotifications = () => {
  const { awardXP, completeDemo, checkAchievements } = useGamificationStore();
  const { toast } = useToast();

  const awardXPWithNotification = (amount: number, reason: string) => {
    awardXP(amount, reason);
    
    toast({
      title: "XP Gained!",
      description: `+${amount} XP: ${reason}`,
      duration: 3000,
    });
  };

  const completeDemoWithNotification = (demoType: string, score: number, attempts: number) => {
    completeDemo(demoType, score, attempts);
    
    // Check for new achievements
    const newAchievements = checkAchievements();
    
    // Show achievement notifications
    newAchievements.forEach((achievement) => {
      toast({
        title: "🏆 Achievement Unlocked!",
        description: `${achievement.name}: ${achievement.description}`,
        duration: 5000,
      });
    });

    // Award XP based on score and attempts
    let xpAmount = Math.round(score * 0.5); // Base XP from score
    if (attempts === 1) xpAmount += 25; // Bonus for first try
    if (score === 100) xpAmount += 50; // Bonus for perfect score

    awardXPWithNotification(xpAmount, `Completed ${demoType} demo`);
  };

  return {
    awardXPWithNotification,
    completeDemoWithNotification,
  };
};
