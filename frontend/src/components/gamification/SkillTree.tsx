import React, { useState, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Lock, 
  Unlock, 
  Star, 
  Zap, 
  Trophy,
  BookOpen,
  Brain,
  Target,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SkillNode {
  id: string;
  name: string;
  description: string;
  type: 'core' | 'advanced' | 'special' | 'mastery';
  level: number;
  maxLevel: number;
  isUnlocked: boolean;
  prerequisites: string[];
  position: { x: number; y: number };
  cost: number;
  rewards: {
    experienceBonus: number;
    pointsBonus: number;
    newFeatures: string[];
    abilities: string[];
  };
}

interface SubjectSkillTree {
  subjectId: number;
  subjectName: string;
  nodes: Ski<PERSON>Node[];
  progress: number;
}

interface SkillTreeProps {
  skillTrees: SubjectSkillTree[];
  availableSkillPoints: number;
  onUnlockNode: (subjectId: number, nodeId: string) => void;
  onUpgradeNode: (subjectId: number, nodeId: string) => void;
}

export function SkillTree({
  skillTrees,
  availableSkillPoints,
  onUnlockNode,
  onUpgradeNode
}: SkillTreeProps) {
  const [selectedSubject, setSelectedSubject] = useState(skillTrees[0]?.subjectId || 0);
  const [selectedNode, setSelectedNode] = useState<SkillNode | null>(null);
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);

  const currentTree = skillTrees.find(tree => tree.subjectId === selectedSubject);

  const getNodeIcon = (type: string) => {
    switch (type) {
      case 'core': return <BookOpen className="h-4 w-4" />;
      case 'advanced': return <Brain className="h-4 w-4" />;
      case 'special': return <Sparkles className="h-4 w-4" />;
      case 'mastery': return <Trophy className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const getNodeColor = (node: SkillNode) => {
    if (!node.isUnlocked) return 'bg-gray-300 border-gray-400';
    
    switch (node.type) {
      case 'core': return 'bg-blue-500 border-blue-600';
      case 'advanced': return 'bg-purple-500 border-purple-600';
      case 'special': return 'bg-yellow-500 border-yellow-600';
      case 'mastery': return 'bg-red-500 border-red-600';
      default: return 'bg-gray-500 border-gray-600';
    }
  };

  const canUnlockNode = useCallback((node: SkillNode) => {
    if (node.isUnlocked) return false;
    if (availableSkillPoints < node.cost) return false;
    
    // Check prerequisites
    return node.prerequisites.every(prereqId => {
      const prereqNode = currentTree?.nodes.find(n => n.id === prereqId);
      return prereqNode?.isUnlocked;
    });
  }, [availableSkillPoints, currentTree]);

  const canUpgradeNode = useCallback((node: SkillNode) => {
    return node.isUnlocked && 
           node.level < node.maxLevel && 
           availableSkillPoints >= node.cost;
  }, [availableSkillPoints]);

  const handleNodeClick = useCallback((node: SkillNode) => {
    setSelectedNode(node);
  }, []);

  const handleUnlockNode = useCallback((node: SkillNode) => {
    if (canUnlockNode(node)) {
      onUnlockNode(selectedSubject, node.id);
    }
  }, [canUnlockNode, onUnlockNode, selectedSubject]);

  const handleUpgradeNode = useCallback((node: SkillNode) => {
    if (canUpgradeNode(node)) {
      onUpgradeNode(selectedSubject, node.id);
    }
  }, [canUpgradeNode, onUpgradeNode, selectedSubject]);

  const renderSkillNode = (node: SkillNode) => {
    const isHovered = hoveredNode === node.id;
    const isSelected = selectedNode?.id === node.id;
    const canUnlock = canUnlockNode(node);
    const canUpgrade = canUpgradeNode(node);

    return (
      <div
        key={node.id}
        className={cn(
          "absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer",
          "transition-all duration-200 hover:scale-110"
        )}
        style={{
          left: `${node.position.x}%`,
          top: `${node.position.y}%`
        }}
        onMouseEnter={() => setHoveredNode(node.id)}
        onMouseLeave={() => setHoveredNode(null)}
        onClick={() => handleNodeClick(node)}
      >
        <div
          className={cn(
            "w-16 h-16 rounded-full border-4 flex items-center justify-center",
            "shadow-lg transition-all duration-200",
            getNodeColor(node),
            isSelected && "ring-4 ring-primary ring-opacity-50",
            isHovered && "shadow-xl",
            !node.isUnlocked && "opacity-60"
          )}
        >
          {node.isUnlocked ? (
            <div className="text-white">
              {getNodeIcon(node.type)}
            </div>
          ) : (
            <Lock className="h-4 w-4 text-gray-600" />
          )}
        </div>
        
        {/* Level indicator */}
        {node.isUnlocked && node.maxLevel > 1 && (
          <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
            <Badge variant="secondary" className="text-xs px-1 py-0">
              {node.level}/{node.maxLevel}
            </Badge>
          </div>
        )}
        
        {/* Upgrade indicator */}
        {canUpgrade && (
          <div className="absolute -top-2 -right-2">
            <div className="w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
              <Star className="h-2 w-2 text-yellow-800" />
            </div>
          </div>
        )}
        
        {/* Tooltip */}
        {isHovered && (
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-10">
            <div className="bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap">
              {node.name}
              {!node.isUnlocked && ` (${node.cost} SP)`}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderConnections = () => {
    if (!currentTree) return null;

    return currentTree.nodes.map(node => 
      node.prerequisites.map(prereqId => {
        const prereqNode = currentTree.nodes.find(n => n.id === prereqId);
        if (!prereqNode) return null;

        const isActive = node.isUnlocked && prereqNode.isUnlocked;

        return (
          <svg
            key={`${prereqId}-${node.id}`}
            className="absolute inset-0 pointer-events-none"
            style={{ width: '100%', height: '100%' }}
          >
            <line
              x1={`${prereqNode.position.x}%`}
              y1={`${prereqNode.position.y}%`}
              x2={`${node.position.x}%`}
              y2={`${node.position.y}%`}
              stroke={isActive ? '#3b82f6' : '#d1d5db'}
              strokeWidth="2"
              strokeDasharray={isActive ? '0' : '5,5'}
            />
          </svg>
        );
      })
    ).flat();
  };

  if (!currentTree) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-gray-500">No skill trees available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Skill Tree</h2>
          <p className="text-gray-600">Unlock new abilities and bonuses</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-right">
            <div className="text-sm text-gray-500">Available Points</div>
            <div className="text-2xl font-bold text-primary flex items-center">
              <Zap className="h-5 w-5 mr-1" />
              {availableSkillPoints}
            </div>
          </div>
        </div>
      </div>

      {/* Subject Tabs */}
      <Tabs value={selectedSubject.toString()} onValueChange={(value) => setSelectedSubject(parseInt(value))}>
        <TabsList className="grid w-full grid-cols-3">
          {skillTrees.map(tree => (
            <TabsTrigger key={tree.subjectId} value={tree.subjectId.toString()}>
              <div className="flex items-center space-x-2">
                <span>{tree.subjectName}</span>
                <Badge variant="outline" className="text-xs">
                  {Math.round(tree.progress)}%
                </Badge>
              </div>
            </TabsTrigger>
          ))}
        </TabsList>

        {skillTrees.map(tree => (
          <TabsContent key={tree.subjectId} value={tree.subjectId.toString()} className="space-y-6">
            {/* Progress */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Subject Progress</span>
                  <span className="text-sm text-gray-500">{Math.round(tree.progress)}%</span>
                </div>
                <Progress value={tree.progress} className="h-2" />
              </CardContent>
            </Card>

            {/* Skill Tree Visualization */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Tree Canvas */}
              <div className="lg:col-span-2">
                <Card>
                  <CardContent className="p-6">
                    <div className="relative h-96 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg overflow-hidden">
                      {renderConnections()}
                      {tree.nodes.map(renderSkillNode)}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Node Details */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      {selectedNode ? selectedNode.name : 'Select a Node'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {selectedNode ? (
                      <>
                        <div>
                          <Badge variant="outline" className="capitalize">
                            {selectedNode.type}
                          </Badge>
                          {selectedNode.isUnlocked && (
                            <Badge variant="secondary" className="ml-2">
                              Level {selectedNode.level}/{selectedNode.maxLevel}
                            </Badge>
                          )}
                        </div>

                        <p className="text-sm text-gray-600">
                          {selectedNode.description}
                        </p>

                        {/* Prerequisites */}
                        {selectedNode.prerequisites.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium mb-2">Prerequisites:</h4>
                            <div className="space-y-1">
                              {selectedNode.prerequisites.map(prereqId => {
                                const prereqNode = tree.nodes.find(n => n.id === prereqId);
                                return prereqNode ? (
                                  <div key={prereqId} className="flex items-center text-xs">
                                    {prereqNode.isUnlocked ? (
                                      <Unlock className="h-3 w-3 text-green-500 mr-1" />
                                    ) : (
                                      <Lock className="h-3 w-3 text-red-500 mr-1" />
                                    )}
                                    {prereqNode.name}
                                  </div>
                                ) : null;
                              })}
                            </div>
                          </div>
                        )}

                        {/* Rewards */}
                        {selectedNode.rewards && (
                          <div>
                            <h4 className="text-sm font-medium mb-2">Rewards:</h4>
                            <div className="space-y-1 text-xs">
                              {selectedNode.rewards.experienceBonus > 0 && (
                                <div>+{selectedNode.rewards.experienceBonus}% Experience</div>
                              )}
                              {selectedNode.rewards.pointsBonus > 0 && (
                                <div>+{selectedNode.rewards.pointsBonus}% Points</div>
                              )}
                              {selectedNode.rewards.newFeatures.map((feature, index) => (
                                <div key={index} className="text-blue-600">• {feature}</div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Actions */}
                        <div className="space-y-2">
                          {!selectedNode.isUnlocked && (
                            <Button
                              onClick={() => handleUnlockNode(selectedNode)}
                              disabled={!canUnlockNode(selectedNode)}
                              className="w-full"
                            >
                              <Unlock className="h-4 w-4 mr-2" />
                              Unlock ({selectedNode.cost} SP)
                            </Button>
                          )}
                          
                          {selectedNode.isUnlocked && selectedNode.level < selectedNode.maxLevel && (
                            <Button
                              onClick={() => handleUpgradeNode(selectedNode)}
                              disabled={!canUpgradeNode(selectedNode)}
                              className="w-full"
                              variant="outline"
                            >
                              <Star className="h-4 w-4 mr-2" />
                              Upgrade ({selectedNode.cost} SP)
                            </Button>
                          )}
                          
                          {selectedNode.isUnlocked && selectedNode.level === selectedNode.maxLevel && (
                            <div className="text-center text-green-600 text-sm font-medium">
                              <Trophy className="h-4 w-4 inline mr-1" />
                              Mastered
                            </div>
                          )}
                        </div>
                      </>
                    ) : (
                      <p className="text-gray-500 text-sm">
                        Click on a skill node to view details and unlock new abilities.
                      </p>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
