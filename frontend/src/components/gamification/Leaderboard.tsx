import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Trophy, 
  Medal, 
  Crown, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Star,
  Zap,
  Target,
  Clock,
  Users,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface LeaderboardEntry {
  userId: string;
  username: string;
  displayName: string;
  avatar?: string;
  rank: number;
  score: number;
  change: number; // Position change from last period
  badge?: {
    name: string;
    rarity: string;
  };
  isCurrentUser?: boolean;
}

interface Leaderboard {
  id: string;
  name: string;
  type: 'experience' | 'accuracy' | 'streak' | 'speed' | 'subject';
  period: 'daily' | 'weekly' | 'monthly' | 'all_time';
  category?: string;
  entries: LeaderboardEntry[];
  updatedAt: string;
}

interface LeaderboardProps {
  leaderboards: Leaderboard[];
  currentUserId: string;
  onRefresh: () => void;
}

export function Leaderboard({
  leaderboards,
  currentUserId,
  onRefresh
}: LeaderboardProps) {
  const [selectedType, setSelectedType] = useState<string>('experience');
  const [selectedPeriod, setSelectedPeriod] = useState<string>('weekly');
  const [showFriendsOnly, setShowFriendsOnly] = useState(false);

  const currentLeaderboard = leaderboards.find(
    lb => lb.type === selectedType && lb.period === selectedPeriod
  );

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Crown className="h-5 w-5 text-yellow-500" />;
      case 2: return <Medal className="h-5 w-5 text-gray-400" />;
      case 3: return <Medal className="h-5 w-5 text-amber-600" />;
      default: return <span className="text-sm font-bold text-gray-500">#{rank}</span>;
    }
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (change < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'experience': return <Zap className="h-4 w-4" />;
      case 'accuracy': return <Target className="h-4 w-4" />;
      case 'streak': return <Star className="h-4 w-4" />;
      case 'speed': return <Clock className="h-4 w-4" />;
      case 'subject': return <Trophy className="h-4 w-4" />;
      default: return <Trophy className="h-4 w-4" />;
    }
  };

  const formatScore = (score: number, type: string) => {
    switch (type) {
      case 'experience':
        return `${score.toLocaleString()} XP`;
      case 'accuracy':
        return `${score.toFixed(1)}%`;
      case 'streak':
        return `${Math.floor(score)} days`;
      case 'speed':
        return `${score.toFixed(1)}s avg`;
      default:
        return score.toLocaleString();
    }
  };

  const currentUserEntry = currentLeaderboard?.entries.find(
    entry => entry.userId === currentUserId
  );

  const topEntries = currentLeaderboard?.entries.slice(0, 10) || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Leaderboards</h2>
          <p className="text-gray-600">Compete with other learners</p>
        </div>
        <Button onClick={onRefresh} variant="outline" size="sm">
          <TrendingUp className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Current User Position */}
      {currentUserEntry && (
        <Card className="border-primary bg-primary/5">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  {getRankIcon(currentUserEntry.rank)}
                  <span className="font-semibold">Your Position</span>
                </div>
                <Avatar className="h-8 w-8">
                  <AvatarImage src={currentUserEntry.avatar} />
                  <AvatarFallback>{currentUserEntry.displayName.charAt(0)}</AvatarFallback>
                </Avatar>
                <span className="font-medium">{currentUserEntry.displayName}</span>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="font-bold">
                    {formatScore(currentUserEntry.score, selectedType)}
                  </div>
                  <div className="text-sm text-gray-500">
                    Rank #{currentUserEntry.rank}
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  {getChangeIcon(currentUserEntry.change)}
                  <span className="text-sm">
                    {Math.abs(currentUserEntry.change)}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Type:</span>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="text-sm border rounded px-2 py-1"
              >
                <option value="experience">Experience</option>
                <option value="accuracy">Accuracy</option>
                <option value="streak">Streak</option>
                <option value="speed">Speed</option>
                <option value="subject">Subject Mastery</option>
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Period:</span>
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="text-sm border rounded px-2 py-1"
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="all_time">All Time</option>
              </select>
            </div>

            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={showFriendsOnly}
                onChange={(e) => setShowFriendsOnly(e.target.checked)}
              />
              <Users className="h-4 w-4" />
              <span>Friends Only</span>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Leaderboard */}
      {currentLeaderboard ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {getTypeIcon(currentLeaderboard.type)}
              <span>{currentLeaderboard.name}</span>
              <Badge variant="outline" className="capitalize">
                {currentLeaderboard.period.replace('_', ' ')}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {/* Top 3 Podium */}
            <div className="p-6 bg-gradient-to-r from-yellow-50 to-orange-50">
              <div className="flex items-end justify-center space-x-8">
                {topEntries.slice(0, 3).map((entry, index) => {
                  const positions = [1, 0, 2]; // Center 1st, left 2nd, right 3rd
                  const heights = ['h-24', 'h-32', 'h-20'];
                  const actualIndex = positions[index];
                  
                  return (
                    <div key={entry.userId} className="text-center">
                      <Avatar className="h-12 w-12 mx-auto mb-2">
                        <AvatarImage src={entry.avatar} />
                        <AvatarFallback>{entry.displayName.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className="text-sm font-medium mb-1">{entry.displayName}</div>
                      <div className="text-xs text-gray-600 mb-2">
                        {formatScore(entry.score, currentLeaderboard.type)}
                      </div>
                      <div className={cn(
                        "w-16 mx-auto rounded-t-lg flex items-end justify-center pb-2",
                        heights[actualIndex],
                        entry.rank === 1 && "bg-yellow-200",
                        entry.rank === 2 && "bg-gray-200",
                        entry.rank === 3 && "bg-amber-200"
                      )}>
                        {getRankIcon(entry.rank)}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Full Rankings */}
            <div className="divide-y">
              {topEntries.map((entry, index) => (
                <div
                  key={entry.userId}
                  className={cn(
                    "flex items-center justify-between p-4 hover:bg-gray-50",
                    entry.userId === currentUserId && "bg-primary/5 border-l-4 border-l-primary"
                  )}
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-8 text-center">
                      {getRankIcon(entry.rank)}
                    </div>
                    
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={entry.avatar} />
                      <AvatarFallback>{entry.displayName.charAt(0)}</AvatarFallback>
                    </Avatar>
                    
                    <div>
                      <div className="font-medium flex items-center space-x-2">
                        <span>{entry.displayName}</span>
                        {entry.userId === currentUserId && (
                          <Badge variant="secondary" className="text-xs">You</Badge>
                        )}
                      </div>
                      <div className="text-sm text-gray-500">@{entry.username}</div>
                    </div>
                    
                    {entry.badge && (
                      <Badge variant="outline" className="text-xs">
                        {entry.badge.name}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="font-bold">
                        {formatScore(entry.score, currentLeaderboard.type)}
                      </div>
                      {entry.change !== 0 && (
                        <div className="flex items-center justify-end space-x-1 text-sm">
                          {getChangeIcon(entry.change)}
                          <span>{Math.abs(entry.change)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {topEntries.length === 0 && (
              <div className="p-8 text-center">
                <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No rankings available for this period.</p>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <Filter className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No leaderboard data available for the selected filters.</p>
          </CardContent>
        </Card>
      )}

      {/* Last Updated */}
      {currentLeaderboard && (
        <div className="text-center text-sm text-gray-500">
          Last updated: {new Date(currentLeaderboard.updatedAt).toLocaleString()}
        </div>
      )}
    </div>
  );
}
