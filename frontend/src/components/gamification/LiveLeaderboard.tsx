import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Trophy, 
  Crown, 
  Medal, 
  TrendingUp, 
  TrendingDown,
  RefreshCw,
  Zap,
  Users
} from 'lucide-react';
import { useGamificationStore } from '@/stores/gamificationStore';

interface LeaderboardEntry {
  id: string;
  username: string;
  xp: number;
  level: number;
  rank: number;
  change: number; // Position change since last update
  isCurrentUser?: boolean;
  avatar?: string;
}

const MOCK_LEADERBOARD_DATA: LeaderboardEntry[] = [
  { id: '1', username: '<PERSON>', xp: 2450, level: 12, rank: 1, change: 0 },
  { id: '2', username: '<PERSON>', xp: 2380, level: 11, rank: 2, change: 1 },
  { id: '3', username: '<PERSON>', xp: 2320, level: 11, rank: 3, change: -1 },
  { id: '4', username: '<PERSON>', xp: 2180, level: 10, rank: 4, change: 0 },
  { id: '5', username: 'Emma <PERSON>', xp: 2050, level: 10, rank: 5, change: 2 },
  { id: '6', username: '<PERSON> <PERSON>', xp: 1980, level: 9, rank: 6, change: -1 },
  { id: '7', username: '<PERSON> Lee', xp: 1920, level: 9, rank: 7, change: 0 },
  { id: '8', username: 'Henry <PERSON>', xp: 1850, level: 9, rank: 8, change: 1 },
];

interface LiveLeaderboardProps {
  maxEntries?: number;
  showRankChanges?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number; // in seconds
}

export const LiveLeaderboard: React.FC<LiveLeaderboardProps> = ({
  maxEntries = 8,
  showRankChanges = true,
  autoRefresh = true,
  refreshInterval = 30,
}) => {
  const { userStats } = useGamificationStore();
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardEntry[]>(MOCK_LEADERBOARD_DATA);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Add current user to leaderboard
  const currentUserEntry: LeaderboardEntry = {
    id: 'current-user',
    username: 'You',
    xp: userStats.totalXP,
    level: userStats.level,
    rank: 0, // Will be calculated
    change: 0,
    isCurrentUser: true,
  };

  // Merge and sort leaderboard
  const mergedLeaderboard = React.useMemo(() => {
    const allEntries = [...leaderboardData, currentUserEntry]
      .sort((a, b) => b.xp - a.xp)
      .map((entry, index) => ({ ...entry, rank: index + 1 }));

    return allEntries.slice(0, maxEntries);
  }, [leaderboardData, currentUserEntry, maxEntries]);

  // Simulate real-time updates
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      simulateLeaderboardUpdate();
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  const simulateLeaderboardUpdate = () => {
    setIsRefreshing(true);
    
    // Simulate small XP changes for random users
    setTimeout(() => {
      setLeaderboardData(prev => {
        const updated = prev.map(entry => {
          // 30% chance of XP change
          if (Math.random() > 0.7) {
            const xpChange = Math.floor(Math.random() * 50) + 10; // 10-60 XP
            return {
              ...entry,
              xp: entry.xp + xpChange,
            };
          }
          return entry;
        });

        // Recalculate ranks and changes
        const sorted = updated.sort((a, b) => b.xp - a.xp);
        return sorted.map((entry, index) => {
          const oldRank = entry.rank;
          const newRank = index + 1;
          return {
            ...entry,
            rank: newRank,
            change: oldRank - newRank, // Positive = moved up, negative = moved down
          };
        });
      });

      setLastUpdate(new Date());
      setIsRefreshing(false);
    }, 1000);
  };

  const manualRefresh = () => {
    simulateLeaderboardUpdate();
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Medal className="h-5 w-5 text-amber-600" />;
      default:
        return (
          <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
            <span className="text-sm font-bold text-muted-foreground">#{rank}</span>
          </div>
        );
    }
  };

  const getRankChangeIcon = (change: number) => {
    if (change > 0) {
      return (
        <div className="flex items-center text-green-600">
          <TrendingUp className="h-3 w-3 mr-1" />
          <span className="text-xs">+{change}</span>
        </div>
      );
    }
    if (change < 0) {
      return (
        <div className="flex items-center text-red-600">
          <TrendingDown className="h-3 w-3 mr-1" />
          <span className="text-xs">{change}</span>
        </div>
      );
    }
    return null;
  };

  const getEntryBackground = (entry: LeaderboardEntry) => {
    if (entry.isCurrentUser) {
      return 'bg-primary/5 border-primary/20 ring-1 ring-primary/20';
    }
    if (entry.rank <= 3) {
      return 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-200';
    }
    return 'bg-background border-border hover:bg-muted/50';
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            Live Leaderboard
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {autoRefresh && (
              <Badge variant="outline" className="text-xs">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse" />
                Live
              </Badge>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={manualRefresh}
              disabled={isRefreshing}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
        
        <p className="text-sm text-muted-foreground">
          Updates every {refreshInterval} seconds • Last update: {lastUpdate.toLocaleTimeString()}
        </p>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-2">
          {mergedLeaderboard.map((entry, index) => (
            <div
              key={entry.id}
              className={`
                flex items-center gap-4 p-3 rounded-lg border transition-all duration-300
                ${getEntryBackground(entry)}
                ${isRefreshing && entry.isCurrentUser ? 'animate-pulse' : ''}
              `}
            >
              {/* Rank */}
              <div className="flex items-center justify-center w-12">
                {entry.rank <= 3 ? (
                  <div className="flex items-center gap-1">
                    {getRankIcon(entry.rank)}
                  </div>
                ) : (
                  getRankIcon(entry.rank)
                )}
              </div>

              {/* Avatar */}
              <Avatar className="h-10 w-10">
                <AvatarFallback className={`
                  ${entry.isCurrentUser 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-muted text-muted-foreground'
                  }
                `}>
                  {entry.username.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>

              {/* User Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <h4 className={`
                    font-semibold truncate
                    ${entry.isCurrentUser ? 'text-primary' : 'text-foreground'}
                  `}>
                    {entry.username}
                  </h4>
                  
                  {entry.isCurrentUser && (
                    <Badge className="bg-primary text-primary-foreground text-xs">
                      You
                    </Badge>
                  )}
                  
                  {entry.rank <= 3 && !entry.isCurrentUser && (
                    <Badge variant="outline" className="text-xs">
                      Top {entry.rank}
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center gap-3 mt-1">
                  <span className="text-sm text-muted-foreground">
                    Level {entry.level}
                  </span>
                  <span className="text-sm font-mono text-muted-foreground flex items-center gap-1">
                    <Zap className="h-3 w-3" />
                    {entry.xp.toLocaleString()} XP
                  </span>
                </div>
              </div>

              {/* Rank Change */}
              <div className="flex items-center gap-2">
                {showRankChanges && getRankChangeIcon(entry.change)}
              </div>
            </div>
          ))}
        </div>

        {/* Footer Stats */}
        <div className="mt-6 pt-4 border-t">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-primary">
                {mergedLeaderboard.length}
              </p>
              <p className="text-sm text-muted-foreground">Active Players</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-green-600">
                {mergedLeaderboard.find(e => e.isCurrentUser)?.rank || '-'}
              </p>
              <p className="text-sm text-muted-foreground">Your Rank</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-yellow-600">
                {Math.max(...mergedLeaderboard.map(e => e.xp)).toLocaleString()}
              </p>
              <p className="text-sm text-muted-foreground">Top Score</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
