import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Star, Zap, Trophy, TrendingUp, RotateCcw } from 'lucide-react';
import { useGamificationStore, getXPProgress } from '@/stores/gamificationStore';

interface XPProgressProps {
  showDetails?: boolean;
  compact?: boolean;
}

export const XPProgress: React.FC<XPProgressProps> = ({
  showDetails = true,
  compact = false,
}) => {
  const { userStats, resetProgress } = useGamificationStore();
  const xpProgress = getXPProgress(userStats.totalXP, userStats.level);

  if (compact) {
    return (
      <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
        <div className="flex items-center gap-2">
          <Star className="h-5 w-5 text-yellow-500" />
          <span className="font-bold text-lg">Level {userStats.level}</span>
        </div>
        
        <div className="flex-1">
          <Progress value={xpProgress.percentage} className="h-2" />
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>{xpProgress.current} XP</span>
            <span>{xpProgress.needed} XP</span>
          </div>
        </div>
        
        <Badge className="bg-primary text-primary-foreground">
          {userStats.totalXP.toLocaleString()} XP
        </Badge>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            Your Progress
          </div>
          {userStats.totalXP > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={resetProgress}
              className="text-muted-foreground hover:text-foreground"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Level and XP */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center gap-2">
            <Trophy className="h-6 w-6 text-yellow-500" />
            <span className="text-3xl font-bold">Level {userStats.level}</span>
          </div>
          
          <Badge className="bg-primary text-primary-foreground text-lg px-4 py-1">
            {userStats.totalXP.toLocaleString()} XP
          </Badge>
        </div>

        {/* Progress to Next Level */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress to Level {userStats.level + 1}</span>
            <span>{xpProgress.percentage}%</span>
          </div>
          
          <Progress value={xpProgress.percentage} className="h-3" />
          
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{xpProgress.current} / {xpProgress.needed} XP</span>
            <span>{xpProgress.needed - xpProgress.current} XP to go</span>
          </div>
        </div>

        {showDetails && (
          <>
            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Zap className="h-4 w-4 text-blue-500" />
                  <span className="text-2xl font-bold">{userStats.demosCompleted}</span>
                </div>
                <p className="text-sm text-muted-foreground">Demos Completed</p>
              </div>
              
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Trophy className="h-4 w-4 text-yellow-500" />
                  <span className="text-2xl font-bold">{userStats.perfectScores}</span>
                </div>
                <p className="text-sm text-muted-foreground">Perfect Scores</p>
              </div>
              
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span className="text-2xl font-bold">{userStats.averageScore}%</span>
                </div>
                <p className="text-sm text-muted-foreground">Average Score</p>
              </div>
              
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Star className="h-4 w-4 text-purple-500" />
                  <span className="text-2xl font-bold">{userStats.currentStreak}</span>
                </div>
                <p className="text-sm text-muted-foreground">Current Streak</p>
              </div>
            </div>

            {/* Performance Insights */}
            <div className="space-y-3">
              <h4 className="font-medium">Performance Insights</h4>
              
              <div className="space-y-2">
                {userStats.averageScore >= 80 && (
                  <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-lg">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-green-800">
                      Excellent performance! You're scoring above 80% on average.
                    </span>
                  </div>
                )}
                
                {userStats.perfectScores >= 3 && (
                  <div className="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <Trophy className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm text-yellow-800">
                      Perfectionist! You've achieved {userStats.perfectScores} perfect scores.
                    </span>
                  </div>
                )}
                
                {userStats.demosCompleted >= 5 && (
                  <div className="flex items-center gap-2 p-2 bg-blue-50 border border-blue-200 rounded-lg">
                    <Zap className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-blue-800">
                      Active learner! You've completed {userStats.demosCompleted} demos.
                    </span>
                  </div>
                )}
                
                {userStats.demosCompleted === 0 && (
                  <div className="flex items-center gap-2 p-2 bg-muted/50 border border-border rounded-lg">
                    <Star className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      Complete your first demo to start earning XP and achievements!
                    </span>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

interface XPNotificationProps {
  amount: number;
  reason: string;
  onClose: () => void;
}

export const XPNotification: React.FC<XPNotificationProps> = ({
  amount,
  reason,
  onClose,
}) => {
  return (
    <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-right duration-300">
      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1 text-green-600">
              <Zap className="h-5 w-5" />
              <span className="font-bold">+{amount} XP</span>
            </div>
            
            <div className="flex-1">
              <p className="text-sm text-green-800">{reason}</p>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0 text-green-600 hover:text-green-700"
            >
              ×
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

interface LevelUpNotificationProps {
  newLevel: number;
  onClose: () => void;
}

export const LevelUpNotification: React.FC<LevelUpNotificationProps> = ({
  newLevel,
  onClose,
}) => {
  return (
    <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-right duration-500">
      <Card className="border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50">
        <CardContent className="p-6">
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center gap-2 text-yellow-600">
              <Trophy className="h-8 w-8" />
              <span className="text-2xl font-bold">Level Up!</span>
            </div>
            
            <p className="text-lg font-semibold text-yellow-800">
              You're now Level {newLevel}!
            </p>
            
            <p className="text-sm text-yellow-700">
              Keep up the great work!
            </p>
            
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
              className="mt-3"
            >
              Awesome!
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
