import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Trophy, 
  Star, 
  Lock, 
  CheckCircle, 
  Target,
  Zap,
  Calendar,
  Users,
  BookOpen,
  Award,
  Crown,
  Gem
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Achievement {
  id: string;
  name: string;
  description: string;
  type: 'progress' | 'streak' | 'mastery' | 'social' | 'challenge' | 'exploration' | 'time' | 'special';
  category: string;
  progress: number;
  maxProgress: number;
  isCompleted: boolean;
  completedAt?: string;
  isSecret: boolean;
  difficulty: number; // 1-5 scale
  rewards: {
    experience: number;
    points: number;
    badges: string[];
    titles: string[];
  };
}

interface Badge {
  id: string;
  name: string;
  description: string;
  iconUrl: string;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  category: string;
  earnedAt: string;
}

interface AchievementsPanelProps {
  achievements: Achievement[];
  badges: Badge[];
  totalExperience: number;
  totalPoints: number;
}

export function AchievementsPanel({
  achievements,
  badges,
  totalExperience,
  totalPoints
}: AchievementsPanelProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showCompleted, setShowCompleted] = useState(true);
  const [showInProgress, setShowInProgress] = useState(true);

  const getAchievementIcon = (type: string) => {
    switch (type) {
      case 'progress': return <Target className="h-5 w-5" />;
      case 'streak': return <Zap className="h-5 w-5" />;
      case 'mastery': return <Trophy className="h-5 w-5" />;
      case 'social': return <Users className="h-5 w-5" />;
      case 'challenge': return <Award className="h-5 w-5" />;
      case 'exploration': return <BookOpen className="h-5 w-5" />;
      case 'time': return <Calendar className="h-5 w-5" />;
      case 'special': return <Crown className="h-5 w-5" />;
      default: return <Star className="h-5 w-5" />;
    }
  };

  const getBadgeRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-500';
      case 'uncommon': return 'bg-green-500';
      case 'rare': return 'bg-blue-500';
      case 'epic': return 'bg-purple-500';
      case 'legendary': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getDifficultyStars = (difficulty: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          "h-3 w-3",
          i < difficulty ? "text-yellow-400 fill-current" : "text-gray-300"
        )}
      />
    ));
  };

  const filteredAchievements = achievements.filter(achievement => {
    if (selectedCategory !== 'all' && achievement.category !== selectedCategory) {
      return false;
    }
    if (!showCompleted && achievement.isCompleted) {
      return false;
    }
    if (!showInProgress && !achievement.isCompleted) {
      return false;
    }
    return true;
  });

  const completedAchievements = achievements.filter(a => a.isCompleted).length;
  const totalAchievements = achievements.length;
  const completionPercentage = (completedAchievements / totalAchievements) * 100;

  const categories = ['all', ...new Set(achievements.map(a => a.category))];

  const recentBadges = badges
    .sort((a, b) => new Date(b.earnedAt).getTime() - new Date(a.earnedAt).getTime())
    .slice(0, 6);

  return (
    <div className="space-y-6">
      {/* Header Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Trophy className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">{completedAchievements}</div>
            <div className="text-sm text-gray-500">Achievements</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Award className="h-8 w-8 text-blue-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">{badges.length}</div>
            <div className="text-sm text-gray-500">Badges Earned</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Zap className="h-8 w-8 text-purple-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">{totalExperience.toLocaleString()}</div>
            <div className="text-sm text-gray-500">Total XP</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Gem className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">{totalPoints.toLocaleString()}</div>
            <div className="text-sm text-gray-500">Total Points</div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Overall Progress</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Achievement Completion</span>
              <span>{completedAchievements}/{totalAchievements} ({Math.round(completionPercentage)}%)</span>
            </div>
            <Progress value={completionPercentage} className="h-3" />
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="achievements" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="achievements">Achievements</TabsTrigger>
          <TabsTrigger value="badges">Badge Collection</TabsTrigger>
        </TabsList>

        <TabsContent value="achievements" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-wrap items-center gap-4">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">Category:</span>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="text-sm border rounded px-2 py-1"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>
                        {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2 text-sm">
                    <input
                      type="checkbox"
                      checked={showCompleted}
                      onChange={(e) => setShowCompleted(e.target.checked)}
                    />
                    <span>Completed</span>
                  </label>
                  <label className="flex items-center space-x-2 text-sm">
                    <input
                      type="checkbox"
                      checked={showInProgress}
                      onChange={(e) => setShowInProgress(e.target.checked)}
                    />
                    <span>In Progress</span>
                  </label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Achievements Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredAchievements.map(achievement => (
              <Card key={achievement.id} className={cn(
                "transition-all duration-200 hover:shadow-lg",
                achievement.isCompleted ? "border-green-200 bg-green-50" : "border-gray-200"
              )}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {achievement.isCompleted ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : achievement.isSecret && achievement.progress === 0 ? (
                        <Lock className="h-5 w-5 text-gray-400" />
                      ) : (
                        getAchievementIcon(achievement.type)
                      )}
                      <Badge variant="outline" className="text-xs capitalize">
                        {achievement.type}
                      </Badge>
                    </div>
                    <div className="flex">
                      {getDifficultyStars(achievement.difficulty)}
                    </div>
                  </div>

                  <h3 className="font-semibold mb-1">
                    {achievement.isSecret && achievement.progress === 0 ? '???' : achievement.name}
                  </h3>
                  
                  <p className="text-sm text-gray-600 mb-3">
                    {achievement.isSecret && achievement.progress === 0 
                      ? 'This is a secret achievement. Keep exploring to unlock it!'
                      : achievement.description
                    }
                  </p>

                  {!achievement.isCompleted && achievement.maxProgress > 1 && (
                    <div className="space-y-1 mb-3">
                      <div className="flex justify-between text-xs">
                        <span>Progress</span>
                        <span>{achievement.progress}/{achievement.maxProgress}</span>
                      </div>
                      <Progress 
                        value={(achievement.progress / achievement.maxProgress) * 100} 
                        className="h-2"
                      />
                    </div>
                  )}

                  {achievement.isCompleted && achievement.completedAt && (
                    <div className="text-xs text-green-600 mb-2">
                      Completed on {new Date(achievement.completedAt).toLocaleDateString()}
                    </div>
                  )}

                  {/* Rewards */}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center space-x-3">
                      {achievement.rewards.experience > 0 && (
                        <span>+{achievement.rewards.experience} XP</span>
                      )}
                      {achievement.rewards.points > 0 && (
                        <span>+{achievement.rewards.points} pts</span>
                      )}
                    </div>
                    {achievement.rewards.badges.length > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        +{achievement.rewards.badges.length} badge{achievement.rewards.badges.length > 1 ? 's' : ''}
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredAchievements.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center">
                <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No achievements match your current filters.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="badges" className="space-y-4">
          {/* Recent Badges */}
          {recentBadges.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Recently Earned</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-3">
                  {recentBadges.map(badge => (
                    <div key={badge.id} className="flex items-center space-x-2 bg-gray-50 rounded-lg p-2">
                      <div className={cn(
                        "w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold",
                        getBadgeRarityColor(badge.rarity)
                      )}>
                        {badge.name.charAt(0)}
                      </div>
                      <div>
                        <div className="text-sm font-medium">{badge.name}</div>
                        <div className="text-xs text-gray-500">
                          {new Date(badge.earnedAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* All Badges */}
          <Card>
            <CardHeader>
              <CardTitle>Badge Collection</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {badges.map(badge => (
                  <div key={badge.id} className="text-center group">
                    <div className={cn(
                      "w-16 h-16 rounded-full mx-auto mb-2 flex items-center justify-center text-white font-bold",
                      "transition-transform duration-200 group-hover:scale-110",
                      getBadgeRarityColor(badge.rarity)
                    )}>
                      {badge.iconUrl ? (
                        <img src={badge.iconUrl} alt={badge.name} className="w-8 h-8" />
                      ) : (
                        badge.name.charAt(0)
                      )}
                    </div>
                    <div className="text-sm font-medium">{badge.name}</div>
                    <Badge variant="outline" className="text-xs capitalize mt-1">
                      {badge.rarity}
                    </Badge>
                  </div>
                ))}
              </div>

              {badges.length === 0 && (
                <div className="text-center py-8">
                  <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No badges earned yet. Complete achievements to earn your first badge!</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
