import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  Trophy, 
  Zap, 
  Star, 
  TrendingUp, 
  Clock,
  RefreshCw,
  Users
} from 'lucide-react';
import { useGamificationStore } from '@/stores/gamificationStore';
import { formatDistanceToNow } from 'date-fns';

interface ActivityItem {
  id: string;
  type: 'xp_gained' | 'achievement_unlocked' | 'level_up' | 'demo_completed' | 'user_joined';
  message: string;
  timestamp: Date;
  user?: string;
  xp?: number;
  achievement?: any;
  isCurrentUser?: boolean;
}

// Mock global activity data
const MOCK_GLOBAL_ACTIVITY: ActivityItem[] = [
  {
    id: '1',
    type: 'achievement_unlocked',
    message: '<PERSON> unlocked "Math Master" achievement',
    timestamp: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
    user: '<PERSON>',
  },
  {
    id: '2',
    type: 'level_up',
    message: '<PERSON> reached Level 8',
    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    user: 'Bob',
  },
  {
    id: '3',
    type: 'demo_completed',
    message: 'Carol completed Drag & Drop demo with 95% score',
    timestamp: new Date(Date.now() - 8 * 60 * 1000), // 8 minutes ago
    user: 'Carol',
  },
  {
    id: '4',
    type: 'xp_gained',
    message: 'David earned 150 XP from Code Challenge',
    timestamp: new Date(Date.now() - 12 * 60 * 1000), // 12 minutes ago
    user: 'David',
    xp: 150,
  },
  {
    id: '5',
    type: 'achievement_unlocked',
    message: 'Emma unlocked "Speed Demon" achievement',
    timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
    user: 'Emma',
  },
];

interface LiveActivityFeedProps {
  showGlobalActivity?: boolean;
  maxItems?: number;
  compact?: boolean;
}

export const LiveActivityFeed: React.FC<LiveActivityFeedProps> = ({
  showGlobalActivity = false,
  maxItems = 10,
  compact = false,
}) => {
  const { recentActivity } = useGamificationStore();
  const [globalActivity, setGlobalActivity] = useState<ActivityItem[]>(MOCK_GLOBAL_ACTIVITY);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  // Simulate real-time updates
  useEffect(() => {
    if (!showGlobalActivity) return;

    const interval = setInterval(() => {
      // Simulate new activity every 30-60 seconds
      if (Math.random() > 0.7) {
        const newActivity: ActivityItem = {
          id: Date.now().toString(),
          type: ['xp_gained', 'demo_completed', 'achievement_unlocked'][Math.floor(Math.random() * 3)] as any,
          message: [
            'Someone completed a Math demo with 88% score',
            'A user earned 75 XP from Drag & Drop',
            'New user unlocked "First Steps" achievement',
          ][Math.floor(Math.random() * 3)],
          timestamp: new Date(),
          user: ['Alex', 'Sam', 'Jordan', 'Taylor'][Math.floor(Math.random() * 4)],
        };

        setGlobalActivity(prev => [newActivity, ...prev.slice(0, maxItems - 1)]);
        setLastUpdate(new Date());
      }
    }, 45000); // Every 45 seconds

    return () => clearInterval(interval);
  }, [showGlobalActivity, maxItems]);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'xp_gained':
        return <Zap className="h-4 w-4 text-blue-500" />;
      case 'achievement_unlocked':
        return <Trophy className="h-4 w-4 text-yellow-500" />;
      case 'level_up':
        return <Star className="h-4 w-4 text-purple-500" />;
      case 'demo_completed':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      default:
        return <Activity className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'xp_gained':
        return 'border-l-blue-500';
      case 'achievement_unlocked':
        return 'border-l-yellow-500';
      case 'level_up':
        return 'border-l-purple-500';
      case 'demo_completed':
        return 'border-l-green-500';
      default:
        return 'border-l-muted-foreground';
    }
  };

  const activities = showGlobalActivity 
    ? globalActivity 
    : recentActivity.map(activity => ({
        ...activity,
        timestamp: new Date(activity.timestamp),
        isCurrentUser: true,
      }));

  const displayActivities = activities.slice(0, maxItems);

  const refreshActivity = () => {
    setLastUpdate(new Date());
    // In a real app, this would fetch fresh data
  };

  if (compact) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-sm">Recent Activity</h4>
          <Button
            variant="ghost"
            size="sm"
            onClick={refreshActivity}
            className="h-6 w-6 p-0"
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
        
        <div className="space-y-1">
          {displayActivities.slice(0, 3).map((activity) => (
            <div
              key={activity.id}
              className={`flex items-center gap-2 p-2 rounded border-l-2 ${getActivityColor(activity.type)} bg-muted/30`}
            >
              {getActivityIcon(activity.type)}
              <span className="text-xs text-muted-foreground truncate">
                {activity.message}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            {showGlobalActivity ? 'Live Activity Feed' : 'Your Recent Activity'}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {showGlobalActivity && (
              <Badge variant="outline" className="text-xs">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse" />
                Live
              </Badge>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshActivity}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {showGlobalActivity && (
          <p className="text-sm text-muted-foreground">
            Last updated {formatDistanceToNow(lastUpdate, { addSuffix: true })}
          </p>
        )}
      </CardHeader>
      
      <CardContent>
        <ScrollArea className="h-64">
          <div className="space-y-3">
            {displayActivities.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No recent activity</p>
                <p className="text-sm">Complete demos to see activity here!</p>
              </div>
            ) : (
              displayActivities.map((activity) => (
                <div
                  key={activity.id}
                  className={`
                    flex items-start gap-3 p-3 rounded-lg border-l-2 transition-all duration-200
                    ${getActivityColor(activity.type)}
                    ${activity.isCurrentUser ? 'bg-primary/5' : 'bg-muted/30'}
                    hover:bg-muted/50
                  `}
                >
                  <div className="flex-shrink-0 mt-0.5">
                    {getActivityIcon(activity.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">
                      {activity.message}
                      {activity.isCurrentUser && (
                        <Badge variant="outline" className="ml-2 text-xs">
                          You
                        </Badge>
                      )}
                    </p>
                    
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs text-muted-foreground flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatDistanceToNow(activity.timestamp, { addSuffix: true })}
                      </span>
                      
                      {activity.xp && (
                        <Badge className="bg-blue-100 text-blue-800 text-xs">
                          +{activity.xp} XP
                        </Badge>
                      )}
                      
                      {activity.user && !activity.isCurrentUser && (
                        <Badge variant="outline" className="text-xs">
                          <Users className="h-3 w-3 mr-1" />
                          {activity.user}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};
