import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Trophy, 
  Target, 
  Clock, 
  Zap, 
  TrendingUp,
  Calendar,
  CheckCircle,
  Star,
  Award,
  BookOpen,
  Brain,
  Sparkles
} from 'lucide-react';
import { useGamificationStore } from '@/stores/gamificationStore';

interface MilestoneProgress {
  id: string;
  title: string;
  description: string;
  progress: number;
  target: number;
  unit: string;
  icon: React.ComponentType<any>;
  color: string;
  completed: boolean;
  reward?: string;
}

interface WeeklyGoal {
  id: string;
  title: string;
  current: number;
  target: number;
  unit: string;
  daysLeft: number;
  onTrack: boolean;
}

export const ProgressSummary: React.FC = () => {
  const { userStats, achievements } = useGamificationStore();
  const [currentStreak, setCurrentStreak] = useState(7);
  const [weeklyGoals, setWeeklyGoals] = useState<WeeklyGoal[]>([
    {
      id: 'study_time',
      title: 'Study Time',
      current: 8.5,
      target: 12,
      unit: 'hours',
      daysLeft: 3,
      onTrack: true
    },
    {
      id: 'demos_completed',
      title: 'Demos Completed',
      current: 15,
      target: 20,
      unit: 'demos',
      daysLeft: 3,
      onTrack: true
    },
    {
      id: 'accuracy_target',
      title: 'Accuracy Goal',
      current: userStats.averageScore || 0,
      target: 85,
      unit: '%',
      daysLeft: 3,
      onTrack: (userStats.averageScore || 0) >= 80
    }
  ]);

  const [milestones, setMilestones] = useState<MilestoneProgress[]>([
    {
      id: 'xp_milestone',
      title: 'XP Master',
      description: 'Reach 5,000 total XP',
      progress: userStats.totalXP,
      target: 5000,
      unit: 'XP',
      icon: Zap,
      color: 'text-purple-500',
      completed: userStats.totalXP >= 5000,
      reward: '500 Bonus XP + Special Badge'
    },
    {
      id: 'level_milestone',
      title: 'Level Champion',
      description: 'Reach Level 15',
      progress: userStats.level,
      target: 15,
      unit: 'Level',
      icon: Star,
      color: 'text-yellow-500',
      completed: userStats.level >= 15,
      reward: 'Exclusive Avatar Frame'
    },
    {
      id: 'achievement_milestone',
      title: 'Achievement Hunter',
      description: 'Unlock 15 achievements',
      progress: achievements.filter(a => a.unlocked).length,
      target: 15,
      unit: 'achievements',
      icon: Trophy,
      color: 'text-orange-500',
      completed: achievements.filter(a => a.unlocked).length >= 15,
      reward: 'Master Learner Title'
    },
    {
      id: 'streak_milestone',
      title: 'Consistency King',
      description: 'Maintain 30-day streak',
      progress: currentStreak,
      target: 30,
      unit: 'days',
      icon: Calendar,
      color: 'text-green-500',
      completed: currentStreak >= 30,
      reward: 'Streak Master Badge'
    }
  ]);

  // Calculate overall progress
  const overallProgress = React.useMemo(() => {
    const totalMilestones = milestones.length;
    const completedMilestones = milestones.filter(m => m.completed).length;
    const progressSum = milestones.reduce((sum, m) => sum + (m.progress / m.target) * 100, 0);
    
    return {
      completed: completedMilestones,
      total: totalMilestones,
      percentage: Math.min(100, progressSum / totalMilestones)
    };
  }, [milestones]);

  // Simulate streak updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Small chance to increment streak (simulating daily activity)
      if (Math.random() > 0.95) {
        setCurrentStreak(prev => prev + 1);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const getProgressColor = (progress: number, target: number) => {
    const percentage = (progress / target) * 100;
    if (percentage >= 100) return 'bg-green-500';
    if (percentage >= 75) return 'bg-blue-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-gray-400';
  };

  const getGoalStatus = (goal: WeeklyGoal) => {
    const percentage = (goal.current / goal.target) * 100;
    if (percentage >= 100) return { status: 'completed', color: 'text-green-600', bg: 'bg-green-50' };
    if (goal.onTrack) return { status: 'on-track', color: 'text-blue-600', bg: 'bg-blue-50' };
    return { status: 'behind', color: 'text-orange-600', bg: 'bg-orange-50' };
  };

  return (
    <div className="space-y-6">
      {/* Overall Progress Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-purple-800">Learning Journey</h2>
              <p className="text-purple-600">Track your progress towards mastery</p>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-purple-800">
                {Math.round(overallProgress.percentage)}%
              </div>
              <div className="text-sm text-purple-600">Overall Progress</div>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-purple-700">Milestones Completed</span>
              <span className="font-medium text-purple-800">
                {overallProgress.completed}/{overallProgress.total}
              </span>
            </div>
            <Progress 
              value={overallProgress.percentage} 
              className="h-3 bg-purple-100"
            />
          </div>
        </CardContent>
      </Card>

      {/* Weekly Goals */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            This Week's Goals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {weeklyGoals.map((goal) => {
              const status = getGoalStatus(goal);
              const percentage = Math.min(100, (goal.current / goal.target) * 100);
              
              return (
                <div key={goal.id} className={`p-4 rounded-lg border ${status.bg}`}>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{goal.title}</h4>
                    <Badge className={`${status.color} bg-transparent border-current`}>
                      {status.status === 'completed' ? 'Done!' : 
                       status.status === 'on-track' ? 'On Track' : 'Behind'}
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>{goal.current} / {goal.target} {goal.unit}</span>
                      <span className="font-medium">{Math.round(percentage)}%</span>
                    </div>
                    <Progress value={percentage} className="h-2" />
                    <div className="text-xs text-muted-foreground">
                      {goal.daysLeft} days remaining
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Milestones */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Learning Milestones
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {milestones.map((milestone) => {
              const percentage = Math.min(100, (milestone.progress / milestone.target) * 100);
              
              return (
                <div 
                  key={milestone.id} 
                  className={`
                    p-4 rounded-lg border transition-all duration-200
                    ${milestone.completed 
                      ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200' 
                      : 'bg-muted/30 hover:bg-muted/50'
                    }
                  `}
                >
                  <div className="flex items-start gap-3">
                    <div className={`
                      p-2 rounded-lg 
                      ${milestone.completed ? 'bg-green-100' : 'bg-background'}
                    `}>
                      <milestone.icon className={`h-5 w-5 ${
                        milestone.completed ? 'text-green-600' : milestone.color
                      }`} />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{milestone.title}</h4>
                        {milestone.completed && (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        )}
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-3">
                        {milestone.description}
                      </p>
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>
                            {milestone.progress} / {milestone.target} {milestone.unit}
                          </span>
                          <span className="font-medium">{Math.round(percentage)}%</span>
                        </div>
                        
                        <Progress 
                          value={percentage} 
                          className={`h-2 ${milestone.completed ? 'bg-green-100' : ''}`}
                        />
                        
                        {milestone.reward && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Sparkles className="h-3 w-3" />
                            <span>Reward: {milestone.reward}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Calendar className="h-8 w-8 mx-auto mb-2 text-green-500" />
            <div className="text-2xl font-bold text-green-600">{currentStreak}</div>
            <div className="text-sm text-muted-foreground">Day Streak</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <BookOpen className="h-8 w-8 mx-auto mb-2 text-blue-500" />
            <div className="text-2xl font-bold text-blue-600">
              {achievements.filter(a => a.unlocked).length}
            </div>
            <div className="text-sm text-muted-foreground">Achievements</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Brain className="h-8 w-8 mx-auto mb-2 text-purple-500" />
            <div className="text-2xl font-bold text-purple-600">{userStats.level}</div>
            <div className="text-sm text-muted-foreground">Current Level</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-8 w-8 mx-auto mb-2 text-orange-500" />
            <div className="text-2xl font-bold text-orange-600">
              {userStats.averageScore || 0}%
            </div>
            <div className="text-sm text-muted-foreground">Avg Score</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
