import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  FileText, 
  Download, 
  Calendar, 
  FileIcon, 
  AlertCircle, 
  History,
  ExternalLink
} from 'lucide-react';
import { useReports, reportHelpers, ReportHistoryItem } from '@/lib/api/reports';

interface ReportHistoryProps {
  onReportDownload?: (reportId: string) => void;
}

export const ReportHistory: React.FC<ReportHistoryProps> = ({
  onReportDownload,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [reports, setReports] = useState<ReportHistoryItem[]>([]);
  const [error, setError] = useState<string | null>(null);

  const { getHistory, downloadReport } = useReports();

  // Load report history when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadReportHistory();
    }
  }, [isOpen]);

  const loadReportHistory = async () => {
    setIsLoading(true);
    setError(null);

    const result = await getHistory();

    if (result.success && result.data) {
      setReports(result.data.reports);
    } else {
      setError(result.error || 'Failed to load report history');
    }

    setIsLoading(false);
  };

  const handleDownload = async (report: ReportHistoryItem) => {
    // Extract report ID from download URL
    const reportId = report.download_url.split('/').pop() || '';
    
    const result = await downloadReport(reportId, report.filename);
    
    if (result.success) {
      onReportDownload?.(reportId);
    } else {
      setError(result.error || 'Failed to download report');
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format.toLowerCase()) {
      case 'pdf':
        return <FileIcon className="h-4 w-4 text-red-500" />;
      case 'html':
        return <FileText className="h-4 w-4 text-blue-500" />;
      default:
        return <FileIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getFormatBadgeVariant = (format: string) => {
    switch (format.toLowerCase()) {
      case 'pdf':
        return 'destructive' as const;
      case 'html':
        return 'default' as const;
      default:
        return 'secondary' as const;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <History className="h-4 w-4 mr-2" />
          Report History
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <History className="h-5 w-5 mr-2" />
            Report History
          </DialogTitle>
          <DialogDescription>
            View and download your previously generated reports.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Error Message */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Loading State */}
          {isLoading && (
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Skeleton className="h-8 w-8" />
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-48" />
                          <Skeleton className="h-3 w-32" />
                        </div>
                      </div>
                      <Skeleton className="h-8 w-24" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Reports List */}
          {!isLoading && reports.length > 0 && (
            <div className="space-y-3">
              {reports.map((report, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getFormatIcon(report.format)}
                        <div>
                          <div className="font-medium">{report.filename}</div>
                          <div className="text-sm text-muted-foreground flex items-center space-x-4">
                            <span className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {reportHelpers.formatDate(report.generated_at)}
                            </span>
                            <span>{reportHelpers.formatFileSize(report.size)}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={getFormatBadgeVariant(report.format)}>
                          {report.format.toUpperCase()}
                        </Badge>
                        <Button
                          size="sm"
                          onClick={() => handleDownload(report)}
                          className="btn-gradient"
                        >
                          <Download className="h-3 w-3 mr-1" />
                          Download
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Empty State */}
          {!isLoading && reports.length === 0 && !error && (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="flex flex-col items-center space-y-4">
                  <FileText className="h-12 w-12 text-muted-foreground" />
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold">No Reports Yet</h3>
                    <p className="text-muted-foreground max-w-md">
                      You haven't generated any reports yet. Create your first report to see it here.
                    </p>
                  </div>
                  <Button
                    onClick={() => setIsOpen(false)}
                    variant="outline"
                  >
                    Generate First Report
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Refresh Button */}
          {!isLoading && reports.length > 0 && (
            <div className="flex justify-center pt-4">
              <Button
                variant="outline"
                onClick={loadReportHistory}
                size="sm"
              >
                Refresh List
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
