import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  FileText, 
  Download, 
  Settings, 
  Calendar, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Eye,
  FileDown,
  Printer
} from 'lucide-react';
import { useReports, reportHelpers, ReportRequest, ReportTemplate } from '@/lib/api/reports';
import { TimeFilter, DateRange } from '@/lib/api-types';

interface ReportGeneratorProps {
  defaultTimeFilter?: TimeFilter;
  onReportGenerated?: (reportId: string) => void;
}

export const ReportGenerator: React.FC<ReportGeneratorProps> = ({
  defaultTimeFilter = 'month',
  onReportGenerated,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isPreviewing, setIsPreviewing] = useState(false);
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [sections, setSections] = useState<Record<string, string>>({});
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [reportType, setReportType] = useState<'performance' | 'progress' | 'detailed' | 'comparative'>('performance');
  const [format, setFormat] = useState<'pdf' | 'html'>('html');
  const [timeFilter, setTimeFilter] = useState<TimeFilter>(defaultTimeFilter);
  const [customTitle, setCustomTitle] = useState('');
  const [selectedSections, setSelectedSections] = useState<string[]>([
    'summary', 'performance', 'strengths', 'weaknesses', 'charts'
  ]);
  const [dateRange, setDateRange] = useState<DateRange | null>(null);

  const { generateReport, previewReport, getTemplates, downloadReport } = useReports();

  // Load templates on component mount
  useEffect(() => {
    const loadTemplates = async () => {
      const result = await getTemplates();
      if (result.success && result.data) {
        setTemplates(result.data.templates);
        setSections(result.data.sections);
      }
    };
    loadTemplates();
  }, []); // Empty dependency array - only run once on mount

  // Update selected sections when report type changes
  useEffect(() => {
    const template = templates.find(t => t.type === reportType && t.is_default);
    if (template) {
      setSelectedSections(template.sections);
    }
  }, [reportType, templates]);

  const handleGenerateReport = async () => {
    setIsGenerating(true);
    setError(null);
    setSuccess(null);

    const request: ReportRequest = {
      report_type: reportType,
      format,
      time_filter: timeFilter,
      include_sections: selectedSections,
      custom_title: customTitle || undefined,
      date_range: timeFilter === 'custom' ? dateRange || undefined : undefined,
    };

    const result = await generateReport(request);

    if (result.success && result.data) {
      setSuccess(`Report generated successfully! Download will start automatically.`);
      onReportGenerated?.(result.data.report_id);

      // Auto-download the report using proper authentication
      setTimeout(async () => {
        try {
          const downloadResult = await downloadReport(result.data.report_id);
          if (!downloadResult.success) {
            setError(downloadResult.error || 'Failed to download report');
          }
        } catch (error) {
          setError('Failed to download report');
        }
      }, 1000);

      // Close dialog after success
      setTimeout(() => {
        setIsOpen(false);
        setSuccess(null);
      }, 3000);
    } else {
      let errorMessage = result.error || 'Failed to generate report';

      // Provide more helpful error messages
      if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
        errorMessage = 'Authentication required. Please refresh the page and try again.';
      } else if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
        errorMessage = 'Access denied. Please check your permissions.';
      } else if (errorMessage.includes('500') || errorMessage.includes('Internal Server Error')) {
        errorMessage = 'Server error. Please try again later or contact support.';
      }

      setError(errorMessage);
    }

    setIsGenerating(false);
  };

  const handlePreviewReport = async () => {
    setIsPreviewing(true);
    setError(null);

    const request: ReportRequest = {
      report_type: reportType,
      format: 'html', // Always use HTML for preview
      time_filter: timeFilter,
      include_sections: selectedSections,
      custom_title: customTitle || undefined,
      date_range: timeFilter === 'custom' ? dateRange || undefined : undefined,
    };

    const result = await previewReport(request);

    if (result.success && result.data) {
      // Open preview in new window with HTML content
      const previewWindow = window.open('', '_blank');
      if (previewWindow) {
        // The result.data should now be HTML content from the backend
        previewWindow.document.write(result.data);
        previewWindow.document.close();
      }
    } else {
      let errorMessage = result.error || 'Failed to preview report';

      // Provide more helpful error messages
      if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
        errorMessage = 'Authentication required. Please refresh the page and try again.';
      } else if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
        errorMessage = 'Access denied. Please check your permissions.';
      } else if (errorMessage.includes('500') || errorMessage.includes('Internal Server Error')) {
        errorMessage = 'Server error. Please try again later or contact support.';
      }

      setError(errorMessage);
    }

    setIsPreviewing(false);
  };

  const handleSectionToggle = (sectionId: string, checked: boolean) => {
    if (checked) {
      setSelectedSections(prev => [...prev, sectionId]);
    } else {
      setSelectedSections(prev => prev.filter(id => id !== sectionId));
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setReportType(template.type as any);
      setSelectedSections(template.sections);
      setCustomTitle(template.name);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="btn-gradient">
          <FileText className="h-4 w-4 mr-2" />
          Generate Report
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Generate Analytics Report
          </DialogTitle>
          <DialogDescription>
            Create a comprehensive report of your learning analytics and performance data.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Error/Success Messages */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          {/* Report Templates */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Templates</CardTitle>
              <CardDescription>Choose from predefined report templates</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {templates.map((template) => (
                  <Button
                    key={template.id}
                    variant="outline"
                    className="h-auto p-4 text-left"
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <div>
                      <div className="font-semibold">{template.name}</div>
                      <div className="text-sm text-muted-foreground mt-1">
                        {template.description}
                      </div>
                      <Badge variant="secondary" className="mt-2">
                        {template.sections.length} sections
                      </Badge>
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Report Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Report Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="report-type">Report Type</Label>
                  <Select value={reportType} onValueChange={(value: any) => setReportType(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="performance">Performance Analysis</SelectItem>
                      <SelectItem value="progress">Learning Progress</SelectItem>
                      <SelectItem value="detailed">Detailed Analysis</SelectItem>
                      <SelectItem value="comparative">Comparative Analysis</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="format">Output Format</Label>
                  <Select value={format} onValueChange={(value: any) => setFormat(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="html">HTML (Web Page)</SelectItem>
                      <SelectItem value="pdf">PDF Document</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="time-filter">Time Period</Label>
                  <Select value={timeFilter} onValueChange={(value: any) => setTimeFilter(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="week">Last Week</SelectItem>
                      <SelectItem value="month">Last Month</SelectItem>
                      <SelectItem value="quarter">Last Quarter</SelectItem>
                      <SelectItem value="year">Last Year</SelectItem>
                      <SelectItem value="custom">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="custom-title">Custom Title (Optional)</Label>
                  <Input
                    id="custom-title"
                    value={customTitle}
                    onChange={(e) => setCustomTitle(e.target.value)}
                    placeholder="Enter custom report title"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Section Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Report Sections</CardTitle>
                <CardDescription>Choose which sections to include</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(sections).map(([sectionId, sectionName]) => (
                    <div key={sectionId} className="flex items-center space-x-2">
                      <Checkbox
                        id={sectionId}
                        checked={selectedSections.includes(sectionId)}
                        onCheckedChange={(checked) => handleSectionToggle(sectionId, !!checked)}
                      />
                      <Label htmlFor={sectionId} className="text-sm">
                        {sectionName}
                      </Label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between">
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={handlePreviewReport}
                disabled={isPreviewing || selectedSections.length === 0}
              >
                {isPreviewing ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Eye className="h-4 w-4 mr-2" />
                )}
                Preview
              </Button>
            </div>

            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleGenerateReport}
                disabled={isGenerating || selectedSections.length === 0}
                className="btn-gradient"
              >
                {isGenerating ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <FileDown className="h-4 w-4 mr-2" />
                )}
                Generate Report
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
