import React, { useState, useC<PERSON>back, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, XCircle, Lightbulb, Calculator, Trophy, Bot } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useGamificationNotifications } from '@/components/gamification/GamificationNotifications';
import { AIChatInterface } from '@/components/ai-tutor/AIChatInterface';

interface MathProblem {
  id: string;
  equation: string;
  answer: number;
  steps: string[];
  hint: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

const MATH_PROBLEMS: MathProblem[] = [
  {
    id: 'linear-1',
    equation: '2x + 5 = 13',
    answer: 4,
    steps: [
      '2x + 5 = 13',
      '2x = 13 - 5',
      '2x = 8',
      'x = 8 ÷ 2',
      'x = 4'
    ],
    hint: 'Subtract 5 from both sides first',
    difficulty: 'easy'
  },
  {
    id: 'quadratic-1',
    equation: 'x² - 5x + 6 = 0',
    answer: 2, // We'll accept either 2 or 3
    steps: [
      'x² - 5x + 6 = 0',
      'Factor: (x - 2)(x - 3) = 0',
      'x - 2 = 0  or  x - 3 = 0',
      'x = 2  or  x = 3'
    ],
    hint: 'Try factoring the quadratic expression',
    difficulty: 'medium'
  },
  {
    id: 'exponential-1',
    equation: '2^x = 16',
    answer: 4,
    steps: [
      '2^x = 16',
      '2^x = 2^4',
      'x = 4'
    ],
    hint: 'Express 16 as a power of 2',
    difficulty: 'medium'
  },
  {
    id: 'fraction-1',
    equation: '(3x + 1)/2 = 7',
    answer: 4.33, // We'll round to nearest decimal
    steps: [
      '(3x + 1)/2 = 7',
      '3x + 1 = 14',
      '3x = 13',
      'x = 13/3',
      'x ≈ 4.33'
    ],
    hint: 'Multiply both sides by 2 first',
    difficulty: 'easy'
  }
];

export const InteractiveMathDemo: React.FC = () => {
  const [currentProblem, setCurrentProblem] = useState(0);
  const [userAnswer, setUserAnswer] = useState('');
  const [showSteps, setShowSteps] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [attempts, setAttempts] = useState(0);
  const [score, setScore] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [showingSolution, setShowingSolution] = useState(false);
  const [showAIHelp, setShowAIHelp] = useState(false);
  const { toast } = useToast();
  const { completeDemoWithNotification } = useGamificationNotifications();

  const problem = MATH_PROBLEMS[currentProblem];

  const checkAnswer = useCallback(() => {
    const numericAnswer = parseFloat(userAnswer);
    setAttempts(prev => prev + 1);

    // Handle special cases for quadratic equations (multiple solutions)
    let correct = false;
    if (problem.id === 'quadratic-1') {
      correct = numericAnswer === 2 || numericAnswer === 3;
    } else if (problem.id === 'fraction-1') {
      // Allow some tolerance for decimal answers
      correct = Math.abs(numericAnswer - problem.answer) < 0.1;
    } else {
      correct = Math.abs(numericAnswer - problem.answer) < 0.01;
    }

    setIsCorrect(correct);

    if (correct) {
      const points = Math.max(0, 100 - (attempts * 15) - (showHint ? 10 : 0) - (showSteps ? 20 : 0));
      setScore(points);

      // Award gamification points
      completeDemoWithNotification('Math Problem', points, attempts);

      toast({
        title: "🎉 Correct!",
        description: `Excellent work! You earned ${points} points.`,
        duration: 3000,
      });
    } else {
      toast({
        title: "Not quite right",
        description: "Try again! Use the hint if you need help.",
        variant: "destructive",
        duration: 2000,
      });
    }
  }, [userAnswer, problem, attempts, showHint, showSteps, toast]);

  const showNextStep = useCallback(() => {
    if (currentStep < problem.steps.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  }, [currentStep, problem.steps.length]);

  const nextProblem = useCallback(() => {
    if (currentProblem < MATH_PROBLEMS.length - 1) {
      setCurrentProblem(prev => prev + 1);
      setUserAnswer('');
      setShowSteps(false);
      setShowHint(false);
      setIsCorrect(null);
      setAttempts(0);
      setScore(0);
      setCurrentStep(0);
      setShowingSolution(false);
    }
  }, [currentProblem]);

  const resetProblem = useCallback(() => {
    setUserAnswer('');
    setShowSteps(false);
    setShowHint(false);
    setIsCorrect(null);
    setAttempts(0);
    setScore(0);
    setCurrentStep(0);
    setShowingSolution(false);
  }, []);

  const showSolution = useCallback(() => {
    setShowingSolution(true);
    setShowSteps(true);
    setCurrentStep(problem.steps.length - 1);
  }, [problem.steps.length]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && userAnswer && isCorrect === null) {
      checkAnswer();
    }
  }, [userAnswer, isCorrect, checkAnswer]);

  const progress = ((currentProblem + (isCorrect ? 1 : 0)) / MATH_PROBLEMS.length) * 100;

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Interactive Math Solver
            {isCorrect && <Trophy className="h-5 w-5 text-yellow-500" />}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge className={getDifficultyColor(problem.difficulty)}>
              {problem.difficulty}
            </Badge>
            <Badge variant="outline">
              Problem {currentProblem + 1} of {MATH_PROBLEMS.length}
            </Badge>
          </div>
        </div>
        <Progress value={progress} className="w-full" />
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Problem Display */}
        <div className="text-center p-6 bg-muted/50 rounded-lg">
          <h3 className="text-2xl font-mono font-bold mb-4">{problem.equation}</h3>
          <p className="text-muted-foreground">Solve for x</p>
        </div>

        {/* Answer Input */}
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <label className="text-lg font-medium">x = </label>
            <Input
              type="number"
              step="any"
              value={userAnswer}
              onChange={(e) => setUserAnswer(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter your answer"
              className="text-lg font-mono"
              disabled={isCorrect === true}
            />
            <Button
              onClick={checkAnswer}
              disabled={!userAnswer || isCorrect === true}
              className="min-w-[100px]"
            >
              {isCorrect === true ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Correct!
                </>
              ) : (
                'Check'
              )}
            </Button>
          </div>

          {/* Feedback */}
          {isCorrect !== null && (
            <div className={`p-4 rounded-lg border ${
              isCorrect 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center gap-2 mb-2">
                {isCorrect ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600" />
                )}
                <span className={`font-semibold ${
                  isCorrect ? 'text-green-800' : 'text-red-800'
                }`}>
                  {isCorrect ? 'Correct!' : 'Incorrect'}
                </span>
              </div>
              {isCorrect ? (
                <p className="text-green-700">
                  Great job! You scored {score} points.
                  {score === 100 && ' Perfect score!'}
                </p>
              ) : (
                <p className="text-red-700">
                  Try again! {attempts >= 2 && 'Consider using the hint or viewing the solution.'}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Help Options */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowHint(!showHint)}
            className="flex items-center gap-2"
          >
            <Lightbulb className="h-4 w-4" />
            {showHint ? 'Hide Hint' : 'Show Hint'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSteps(!showSteps)}
            className="flex items-center gap-2"
          >
            Show {showSteps ? 'Less' : 'Steps'}
          </Button>

          {attempts >= 2 && !showingSolution && (
            <Button
              variant="outline"
              size="sm"
              onClick={showSolution}
              className="text-orange-600 hover:text-orange-700"
            >
              Show Solution
            </Button>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={resetProblem}
          >
            Reset
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAIHelp(!showAIHelp)}
            className="flex items-center gap-2"
          >
            <Bot className="h-4 w-4" />
            {showAIHelp ? 'Hide' : 'Get'} AI Help
          </Button>
        </div>

        {/* Hint */}
        {showHint && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Lightbulb className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-800">Hint:</span>
            </div>
            <p className="text-blue-700">{problem.hint}</p>
          </div>
        )}

        {/* Step-by-step solution */}
        {showSteps && (
          <div className="space-y-3">
            <h4 className="font-medium">Step-by-step solution:</h4>
            <div className="space-y-2">
              {problem.steps.slice(0, currentStep + 1).map((step, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg font-mono ${
                    index === currentStep 
                      ? 'bg-primary/10 border border-primary/20' 
                      : 'bg-muted/50'
                  }`}
                >
                  <span className="text-sm text-muted-foreground mr-2">
                    Step {index + 1}:
                  </span>
                  {step}
                </div>
              ))}
            </div>
            
            {!showingSolution && currentStep < problem.steps.length - 1 && (
              <Button
                variant="outline"
                size="sm"
                onClick={showNextStep}
              >
                Next Step
              </Button>
            )}
          </div>
        )}

        {/* Navigation */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            {attempts > 0 && (
              <Badge variant="secondary">
                Attempts: {attempts}
              </Badge>
            )}
            {score > 0 && (
              <Badge className="bg-yellow-100 text-yellow-800">
                Score: {score}
              </Badge>
            )}
          </div>

          {isCorrect && currentProblem < MATH_PROBLEMS.length - 1 && (
            <Button onClick={nextProblem}>
              Next Problem
            </Button>
          )}
        </div>

        {/* AI Help Interface */}
        {showAIHelp && (
          <div className="mt-6 pt-6 border-t">
            <AIChatInterface
              context="math"
              maxHeight="300px"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};
