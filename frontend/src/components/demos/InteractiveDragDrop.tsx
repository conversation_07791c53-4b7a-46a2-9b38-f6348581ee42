import React, { useState, useCallback, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, XCircle, RotateCcw, Trophy, Bot } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useGamificationNotifications } from '@/components/gamification/GamificationNotifications';
import { AIChatInterface } from '@/components/ai-tutor/AIChatInterface';
import { useScreenReader, useKeyboardNavigation, useAriaLiveRegion } from '@/hooks/useAccessibility';
import { FadeTransition, AccessibleHover } from '@/components/ui/accessible-animations';

interface DragItem {
  id: string;
  content: string;
  correctPosition: number;
}

interface DropZone {
  id: number;
  label: string;
  acceptedItem?: DragItem;
}

const DEMO_QUESTIONS = [
  {
    id: 'scientific-method',
    title: 'Scientific Method Steps',
    description: 'Arrange the steps of the scientific method in the correct order:',
    items: [
      { id: 'observation', content: '1. Observation', correctPosition: 0 },
      { id: 'hypothesis', content: '2. Hypothesis', correctPosition: 1 },
      { id: 'experiment', content: '3. Experiment', correctPosition: 2 },
      { id: 'conclusion', content: '4. Conclusion', correctPosition: 3 },
    ],
    dropZones: [
      { id: 0, label: 'First Step' },
      { id: 1, label: 'Second Step' },
      { id: 2, label: 'Third Step' },
      { id: 3, label: 'Fourth Step' },
    ]
  },
  {
    id: 'math-operations',
    title: 'Order of Operations',
    description: 'Arrange these mathematical operations in order of precedence:',
    items: [
      { id: 'parentheses', content: 'Parentheses ( )', correctPosition: 0 },
      { id: 'exponents', content: 'Exponents ^', correctPosition: 1 },
      { id: 'multiply', content: 'Multiply ×', correctPosition: 2 },
      { id: 'addition', content: 'Addition +', correctPosition: 3 },
    ],
    dropZones: [
      { id: 0, label: 'Highest Priority' },
      { id: 1, label: 'Second Priority' },
      { id: 2, label: 'Third Priority' },
      { id: 3, label: 'Lowest Priority' },
    ]
  }
];

export const InteractiveDragDrop: React.FC = () => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [draggedItem, setDraggedItem] = useState<DragItem | null>(null);
  const [dropZones, setDropZones] = useState<DropZone[]>(DEMO_QUESTIONS[0].dropZones);
  const [availableItems, setAvailableItems] = useState<DragItem[]>(DEMO_QUESTIONS[0].items);
  const [isCompleted, setIsCompleted] = useState(false);
  const [score, setScore] = useState(0);
  const [attempts, setAttempts] = useState(0);
  const [showFeedback, setShowFeedback] = useState(false);
  const [showAIHelp, setShowAIHelp] = useState(false);
  const dragCounter = useRef(0);
  const { toast } = useToast();
  const { completeDemoWithNotification } = useGamificationNotifications();

  // Accessibility hooks
  const { announce } = useScreenReader();
  const { message: liveMessage, updateMessage, ariaLiveProps } = useAriaLiveRegion();

  const question = DEMO_QUESTIONS[currentQuestion];

  const handleDragStart = useCallback((e: React.DragEvent, item: DragItem) => {
    setDraggedItem(item);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', item.id);
    announce(`Started dragging ${item.content}`, 'polite');
  }, [announce]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }, []);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    dragCounter.current++;
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    dragCounter.current--;
  }, []);

  const handleDrop = useCallback((e: React.DragEvent, zoneId: number) => {
    e.preventDefault();
    dragCounter.current = 0;
    
    if (!draggedItem) return;

    // Remove item from available items
    setAvailableItems(prev => prev.filter(item => item.id !== draggedItem.id));
    
    // Add item to drop zone (replace if already occupied)
    setDropZones(prev => prev.map(zone => {
      if (zone.id === zoneId) {
        // If zone was occupied, return that item to available items
        if (zone.acceptedItem) {
          setAvailableItems(current => [...current, zone.acceptedItem!]);
        }
        return { ...zone, acceptedItem: draggedItem };
      }
      return zone;
    }));

    setDraggedItem(null);
    announce(`Dropped ${draggedItem.content} in position ${zoneId + 1}`, 'polite');
    updateMessage(`${draggedItem.content} placed in position ${zoneId + 1}`);
  }, [draggedItem, announce, updateMessage]);

  const handleRemoveFromZone = useCallback((zoneId: number) => {
    setDropZones(prev => prev.map(zone => {
      if (zone.id === zoneId && zone.acceptedItem) {
        setAvailableItems(current => [...current, zone.acceptedItem!]);
        return { ...zone, acceptedItem: undefined };
      }
      return zone;
    }));
  }, []);

  const validateAnswer = useCallback(() => {
    setAttempts(prev => prev + 1);
    
    const isCorrect = dropZones.every(zone => 
      zone.acceptedItem && zone.acceptedItem.correctPosition === zone.id
    );

    if (isCorrect) {
      const finalScore = Math.max(0, 100 - (attempts * 10)); // Reduce score for multiple attempts
      setIsCompleted(true);
      setScore(finalScore);
      setShowFeedback(true);

      // Award gamification points
      completeDemoWithNotification('Drag & Drop', finalScore, attempts + 1);

      const successMessage = `Excellent! Perfect sequence completed in ${attempts + 1} attempt${attempts === 0 ? '' : 's'}!`;
      announce(successMessage, 'assertive');
      updateMessage(successMessage);

      toast({
        title: "🎉 Excellent!",
        description: `Perfect! You completed it in ${attempts + 1} attempt${attempts === 0 ? '' : 's'}.`,
        duration: 3000,
      });
    } else {
      const errorMessage = "Not quite right. Some items are in the wrong position. Try again!";
      announce(errorMessage, 'assertive');
      updateMessage(errorMessage);

      toast({
        title: "Not quite right",
        description: "Some items are in the wrong position. Try again!",
        variant: "destructive",
        duration: 2000,
      });
    }
  }, [dropZones, attempts, toast, announce, updateMessage]);

  const resetDemo = useCallback(() => {
    setDropZones(question.dropZones);
    setAvailableItems(question.items);
    setIsCompleted(false);
    setScore(0);
    setAttempts(0);
    setShowFeedback(false);
    setDraggedItem(null);
  }, [question]);

  const nextQuestion = useCallback(() => {
    if (currentQuestion < DEMO_QUESTIONS.length - 1) {
      setCurrentQuestion(prev => prev + 1);
      const nextQ = DEMO_QUESTIONS[currentQuestion + 1];
      setDropZones(nextQ.dropZones);
      setAvailableItems(nextQ.items);
      setIsCompleted(false);
      setScore(0);
      setAttempts(0);
      setShowFeedback(false);
    }
  }, [currentQuestion]);

  const progress = ((currentQuestion + (isCompleted ? 1 : 0)) / DEMO_QUESTIONS.length) * 100;

  return (
    <Card className="w-full max-w-4xl mx-auto">
      {/* ARIA Live Region for screen readers */}
      <div {...ariaLiveProps}>
        {liveMessage}
      </div>

      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            🎯 Interactive Drag & Drop Demo
            {isCompleted && <Trophy className="h-5 w-5 text-yellow-500" />}
          </CardTitle>
          <Badge variant="outline">
            Question {currentQuestion + 1} of {DEMO_QUESTIONS.length}
          </Badge>
        </div>
        <Progress value={progress} className="w-full" />
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Question */}
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">{question.title}</h3>
          <p className="text-muted-foreground">{question.description}</p>
        </div>

        {/* Available Items */}
        <div className="space-y-2">
          <h4 className="font-medium">Available Items:</h4>
          <div className="flex flex-wrap gap-2 min-h-[60px] p-3 border-2 border-dashed border-border rounded-lg">
            {availableItems.map((item) => (
              <div
                key={item.id}
                draggable
                onDragStart={(e) => handleDragStart(e, item)}
                className="px-3 py-2 bg-primary/10 border border-primary/20 rounded-lg cursor-move hover:bg-primary/20 transition-colors select-none"
              >
                {item.content}
              </div>
            ))}
            {availableItems.length === 0 && (
              <p className="text-muted-foreground italic">All items have been placed</p>
            )}
          </div>
        </div>

        {/* Drop Zones */}
        <div className="space-y-2">
          <h4 className="font-medium">Drop Zones:</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {dropZones.map((zone) => (
              <div
                key={zone.id}
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, zone.id)}
                className={`
                  min-h-[80px] p-4 border-2 border-dashed rounded-lg transition-all
                  ${zone.acceptedItem 
                    ? 'border-primary bg-primary/5' 
                    : 'border-border hover:border-primary/50'
                  }
                  ${draggedItem ? 'hover:bg-primary/10' : ''}
                `}
              >
                <div className="text-sm text-muted-foreground mb-2">{zone.label}</div>
                {zone.acceptedItem ? (
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{zone.acceptedItem.content}</span>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleRemoveFromZone(zone.id)}
                      className="h-6 w-6 p-0"
                    >
                      ×
                    </Button>
                  </div>
                ) : (
                  <div className="text-muted-foreground italic text-center">
                    Drop item here
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4">
          <div className="flex items-center gap-2">
            <Button
              onClick={resetDemo}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset
            </Button>
            {attempts > 0 && (
              <Badge variant="secondary">
                Attempts: {attempts}
              </Badge>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAIHelp(!showAIHelp)}
              className="flex items-center gap-2"
            >
              <Bot className="h-4 w-4" />
              {showAIHelp ? 'Hide' : 'Get'} AI Help
            </Button>
          </div>

          <div className="flex items-center gap-2">
            {isCompleted && currentQuestion < DEMO_QUESTIONS.length - 1 && (
              <Button onClick={nextQuestion} className="flex items-center gap-2">
                Next Question
              </Button>
            )}
            
            <Button
              onClick={validateAnswer}
              disabled={dropZones.some(zone => !zone.acceptedItem) || isCompleted}
              className="flex items-center gap-2"
            >
              {isCompleted ? (
                <>
                  <CheckCircle className="h-4 w-4" />
                  Completed!
                </>
              ) : (
                'Check Answer'
              )}
            </Button>
          </div>
        </div>

        {/* Feedback */}
        {showFeedback && isCompleted && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="font-semibold text-green-800">Perfect!</span>
            </div>
            <p className="text-green-700">
              You scored {score} points! {score === 100 ? 'Amazing first try!' : 'Great job!'}
            </p>
          </div>
        )}

        {/* AI Help Interface */}
        {showAIHelp && (
          <div className="mt-6 pt-6 border-t">
            <AIChatInterface
              context="drag-drop"
              maxHeight="300px"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};
