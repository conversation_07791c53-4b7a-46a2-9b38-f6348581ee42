import React, { useState, use<PERSON><PERSON>back } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { CheckCircle, XCircle, Play, RotateCcw, Code, Trophy } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useGamificationNotifications } from '@/components/gamification/GamificationNotifications';

interface CodeChallenge {
  id: string;
  title: string;
  description: string;
  language: 'python' | 'javascript';
  starterCode: string;
  solution: string;
  testCases: Array<{
    input: any[];
    expected: any;
    description: string;
  }>;
  hints: string[];
}

const CODE_CHALLENGES: CodeChallenge[] = [
  {
    id: 'add-numbers',
    title: 'Add Two Numbers',
    description: 'Write a function that takes two numbers and returns their sum.',
    language: 'python',
    starterCode: `def add_numbers(a, b):
    # Your code here
    pass`,
    solution: `def add_numbers(a, b):
    return a + b`,
    testCases: [
      { input: [2, 3], expected: 5, description: 'add_numbers(2, 3) should return 5' },
      { input: [-1, 1], expected: 0, description: 'add_numbers(-1, 1) should return 0' },
      { input: [0, 0], expected: 0, description: 'add_numbers(0, 0) should return 0' },
    ],
    hints: [
      'Use the + operator to add two numbers',
      'Return the result using the return statement'
    ]
  },
  {
    id: 'find-max',
    title: 'Find Maximum',
    description: 'Write a function that finds the maximum number in a list.',
    language: 'python',
    starterCode: `def find_max(numbers):
    # Your code here
    pass`,
    solution: `def find_max(numbers):
    if not numbers:
        return None
    return max(numbers)`,
    testCases: [
      { input: [[1, 3, 2]], expected: 3, description: 'find_max([1, 3, 2]) should return 3' },
      { input: [[-1, -5, -2]], expected: -1, description: 'find_max([-1, -5, -2]) should return -1' },
      { input: [[42]], expected: 42, description: 'find_max([42]) should return 42' },
    ],
    hints: [
      'You can use the built-in max() function',
      'Consider what to return for an empty list'
    ]
  },
  {
    id: 'reverse-string',
    title: 'Reverse String',
    description: 'Write a function that reverses a string.',
    language: 'javascript',
    starterCode: `function reverseString(str) {
    // Your code here
}`,
    solution: `function reverseString(str) {
    return str.split('').reverse().join('');
}`,
    testCases: [
      { input: ['hello'], expected: 'olleh', description: 'reverseString("hello") should return "olleh"' },
      { input: [''], expected: '', description: 'reverseString("") should return ""' },
      { input: ['a'], expected: 'a', description: 'reverseString("a") should return "a"' },
    ],
    hints: [
      'You can split the string into an array',
      'Use the reverse() method on arrays',
      'Join the array back into a string'
    ]
  }
];

export const InteractiveCodeDemo: React.FC = () => {
  const [currentChallenge, setCurrentChallenge] = useState(0);
  const [userCode, setUserCode] = useState(CODE_CHALLENGES[0].starterCode);
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<Array<{passed: boolean, error?: string}>>([]);
  const [showHints, setShowHints] = useState(false);
  const [showSolution, setShowSolution] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [score, setScore] = useState(0);
  const { toast } = useToast();
  const { completeDemoWithNotification } = useGamificationNotifications();

  const challenge = CODE_CHALLENGES[currentChallenge];

  // Simple code execution simulator
  const executeCode = useCallback(async () => {
    setIsRunning(true);
    setAttempts(prev => prev + 1);
    
    // Simulate execution delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const results: Array<{passed: boolean, error?: string}> = [];

    try {
      // Simple validation for Python functions
      if (challenge.language === 'python') {
        // Check if the function is properly defined
        if (!userCode.includes('def ' + challenge.testCases[0].description.split('(')[0].split(' ')[0])) {
          throw new Error('Function not properly defined');
        }

        // Check for basic patterns
        for (const testCase of challenge.testCases) {
          let passed = false;
          let error = '';

          // Simple pattern matching for solutions
          if (challenge.id === 'add-numbers') {
            passed = userCode.includes('return a + b') || userCode.includes('return b + a');
            if (!passed) error = 'Function should return a + b';
          } else if (challenge.id === 'find-max') {
            passed = userCode.includes('max(') || userCode.includes('return max');
            if (!passed) error = 'Consider using the max() function';
          }

          results.push({ passed, error });
        }
      } else if (challenge.language === 'javascript') {
        // Check for JavaScript patterns
        for (const testCase of challenge.testCases) {
          let passed = false;
          let error = '';

          if (challenge.id === 'reverse-string') {
            passed = userCode.includes('.reverse()') && userCode.includes('.split(') && userCode.includes('.join(');
            if (!passed) error = 'Try using split(), reverse(), and join() methods';
          }

          results.push({ passed, error });
        }
      }

      setTestResults(results);

      const allPassed = results.every(r => r.passed);
      if (allPassed) {
        const points = Math.max(0, 100 - (attempts * 10) - (showHints ? 20 : 0) - (showSolution ? 50 : 0));
        setScore(points);

        // Award gamification points
        completeDemoWithNotification('Code Challenge', points, attempts);

        toast({
          title: "🎉 All tests passed!",
          description: `Excellent! You earned ${points} points.`,
          duration: 3000,
        });
      } else {
        toast({
          title: "Some tests failed",
          description: "Check the test results and try again!",
          variant: "destructive",
          duration: 2000,
        });
      }

    } catch (error) {
      toast({
        title: "Code Error",
        description: error instanceof Error ? error.message : "There's an error in your code",
        variant: "destructive",
        duration: 3000,
      });
    }

    setIsRunning(false);
  }, [userCode, challenge, attempts, showHints, showSolution, toast]);

  const resetChallenge = useCallback(() => {
    setUserCode(challenge.starterCode);
    setTestResults([]);
    setShowHints(false);
    setShowSolution(false);
    setAttempts(0);
    setScore(0);
  }, [challenge.starterCode]);

  const nextChallenge = useCallback(() => {
    if (currentChallenge < CODE_CHALLENGES.length - 1) {
      setCurrentChallenge(prev => prev + 1);
      const nextCh = CODE_CHALLENGES[currentChallenge + 1];
      setUserCode(nextCh.starterCode);
      setTestResults([]);
      setShowHints(false);
      setShowSolution(false);
      setAttempts(0);
      setScore(0);
    }
  }, [currentChallenge]);

  const insertSolution = useCallback(() => {
    setUserCode(challenge.solution);
    setShowSolution(true);
  }, [challenge.solution]);

  const progress = ((currentChallenge + (testResults.length > 0 && testResults.every(r => r.passed) ? 1 : 0)) / CODE_CHALLENGES.length) * 100;

  const getLanguageColor = (language: string) => {
    switch (language) {
      case 'python': return 'bg-blue-100 text-blue-800';
      case 'javascript': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            Interactive Code Challenge
            {testResults.length > 0 && testResults.every(r => r.passed) && (
              <Trophy className="h-5 w-5 text-yellow-500" />
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge className={getLanguageColor(challenge.language)}>
              {challenge.language}
            </Badge>
            <Badge variant="outline">
              Challenge {currentChallenge + 1} of {CODE_CHALLENGES.length}
            </Badge>
          </div>
        </div>
        <Progress value={progress} className="w-full" />
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Challenge Description */}
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">{challenge.title}</h3>
          <p className="text-muted-foreground">{challenge.description}</p>
        </div>

        {/* Code Editor */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Your Code:</h4>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={resetChallenge}
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Reset
              </Button>
              {attempts >= 2 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={insertSolution}
                  className="text-orange-600 hover:text-orange-700"
                >
                  Show Solution
                </Button>
              )}
            </div>
          </div>
          
          <div className="relative">
            <Textarea
              value={userCode}
              onChange={(e) => setUserCode(e.target.value)}
              className="font-mono text-sm min-h-[200px] bg-slate-900 text-green-400 border-slate-700"
              placeholder="Write your code here..."
            />
          </div>
        </div>

        {/* Test Cases */}
        <div className="space-y-2">
          <h4 className="font-medium">Test Cases:</h4>
          <div className="space-y-2">
            {challenge.testCases.map((testCase, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border ${
                  testResults[index]?.passed 
                    ? 'bg-green-50 border-green-200' 
                    : testResults[index]?.passed === false
                    ? 'bg-red-50 border-red-200'
                    : 'bg-muted/50 border-border'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="font-mono text-sm">{testCase.description}</span>
                  {testResults[index] && (
                    testResults[index].passed ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )
                  )}
                </div>
                {testResults[index]?.error && (
                  <p className="text-sm text-red-600 mt-1">{testResults[index].error}</p>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Hints */}
        {showHints && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2">Hints:</h4>
            <ul className="space-y-1">
              {challenge.hints.map((hint, index) => (
                <li key={index} className="text-blue-700 text-sm">
                  • {hint}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHints(!showHints)}
            >
              {showHints ? 'Hide Hints' : 'Show Hints'}
            </Button>
            
            {attempts > 0 && (
              <Badge variant="secondary">
                Attempts: {attempts}
              </Badge>
            )}
            
            {score > 0 && (
              <Badge className="bg-yellow-100 text-yellow-800">
                Score: {score}
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            {testResults.length > 0 && testResults.every(r => r.passed) && 
             currentChallenge < CODE_CHALLENGES.length - 1 && (
              <Button onClick={nextChallenge}>
                Next Challenge
              </Button>
            )}
            
            <Button
              onClick={executeCode}
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              {isRunning ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                  Running...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  Run Code
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
