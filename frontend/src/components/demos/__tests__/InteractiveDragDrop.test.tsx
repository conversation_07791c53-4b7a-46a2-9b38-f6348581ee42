import React from 'react';
import { render, screen, fireEvent, waitFor } from '@/test/test-utils';
import { InteractiveDragDrop } from '../InteractiveDragDrop';
import { createMockDragEvent } from '@/test/test-utils';

// Mock the gamification notifications hook
jest.mock('@/components/gamification/GamificationNotifications', () => ({
  useGamificationNotifications: () => ({
    completeDemoWithNotification: jest.fn(),
  }),
}));

describe('InteractiveDragDrop', () => {
  beforeEach(() => {
    // Reset any mocks before each test
    jest.clearAllMocks();
  });

  it('renders the drag and drop interface', () => {
    render(<InteractiveDragDrop />);
    
    expect(screen.getByText('Interactive Drag & Drop')).toBeInTheDocument();
    expect(screen.getByText('Scientific Method Steps')).toBeInTheDocument();
    expect(screen.getByText('Drag the steps into the correct order:')).toBeInTheDocument();
  });

  it('displays all draggable items', () => {
    render(<InteractiveDragDrop />);
    
    expect(screen.getByText('Observation')).toBeInTheDocument();
    expect(screen.getByText('Hypothesis')).toBeInTheDocument();
    expect(screen.getByText('Experiment')).toBeInTheDocument();
    expect(screen.getByText('Analysis')).toBeInTheDocument();
    expect(screen.getByText('Conclusion')).toBeInTheDocument();
  });

  it('shows drop zones', () => {
    render(<InteractiveDragDrop />);
    
    const dropZones = screen.getAllByText(/Drop here/);
    expect(dropZones).toHaveLength(5);
  });

  it('handles drag start event', () => {
    render(<InteractiveDragDrop />);
    
    const draggableItem = screen.getByText('Observation');
    const dragEvent = createMockDragEvent('dragstart');
    
    fireEvent(draggableItem, dragEvent);
    
    expect(dragEvent.dataTransfer.setData).toHaveBeenCalledWith('text/plain', 'observation');
  });

  it('handles drop event', async () => {
    render(<InteractiveDragDrop />);
    
    const dropZone = screen.getAllByText(/Drop here/)[0];
    const dropEvent = createMockDragEvent('drop', {
      getData: jest.fn().mockReturnValue('observation'),
    });
    
    fireEvent(dropZone, dropEvent);
    
    await waitFor(() => {
      expect(screen.getByText('Observation')).toBeInTheDocument();
    });
  });

  it('validates correct order', async () => {
    render(<InteractiveDragDrop />);
    
    // Simulate dropping items in correct order
    const dropZones = screen.getAllByText(/Drop here/);
    const correctOrder = ['observation', 'hypothesis', 'experiment', 'analysis', 'conclusion'];
    
    correctOrder.forEach((item, index) => {
      const dropEvent = createMockDragEvent('drop', {
        getData: jest.fn().mockReturnValue(item),
      });
      fireEvent(dropZones[index], dropEvent);
    });
    
    // Click check answer button
    const checkButton = screen.getByText('Check Answer');
    fireEvent.click(checkButton);
    
    await waitFor(() => {
      expect(screen.getByText(/Excellent!/)).toBeInTheDocument();
    });
  });

  it('shows incorrect feedback for wrong order', async () => {
    render(<InteractiveDragDrop />);
    
    // Simulate dropping items in wrong order
    const dropZones = screen.getAllByText(/Drop here/);
    const wrongOrder = ['conclusion', 'observation', 'hypothesis', 'experiment', 'analysis'];
    
    wrongOrder.forEach((item, index) => {
      const dropEvent = createMockDragEvent('drop', {
        getData: jest.fn().mockReturnValue(item),
      });
      fireEvent(dropZones[index], dropEvent);
    });
    
    // Click check answer button
    const checkButton = screen.getByText('Check Answer');
    fireEvent.click(checkButton);
    
    await waitFor(() => {
      expect(screen.getByText(/Not quite right/)).toBeInTheDocument();
    });
  });

  it('allows reset functionality', () => {
    render(<InteractiveDragDrop />);
    
    const resetButton = screen.getByText('Reset');
    fireEvent.click(resetButton);
    
    // All items should be back in the original container
    expect(screen.getAllByText(/Drop here/)).toHaveLength(5);
  });

  it('tracks attempts', async () => {
    render(<InteractiveDragDrop />);
    
    // Make an incorrect attempt
    const checkButton = screen.getByText('Check Answer');
    fireEvent.click(checkButton);
    
    await waitFor(() => {
      expect(screen.getByText('Attempts: 1')).toBeInTheDocument();
    });
  });

  it('shows AI help button', () => {
    render(<InteractiveDragDrop />);
    
    expect(screen.getByText('Get AI Help')).toBeInTheDocument();
  });

  it('toggles AI help interface', () => {
    render(<InteractiveDragDrop />);
    
    const aiHelpButton = screen.getByText('Get AI Help');
    fireEvent.click(aiHelpButton);
    
    expect(screen.getByText('Hide AI Help')).toBeInTheDocument();
  });

  it('prevents drag over default behavior', () => {
    render(<InteractiveDragDrop />);
    
    const dropZone = screen.getAllByText(/Drop here/)[0];
    const dragOverEvent = createMockDragEvent('dragover');
    
    fireEvent(dropZone, dragOverEvent);
    
    expect(dragOverEvent.preventDefault).toHaveBeenCalled();
  });

  it('handles multiple questions', async () => {
    render(<InteractiveDragDrop />);
    
    // Complete first question correctly
    const dropZones = screen.getAllByText(/Drop here/);
    const correctOrder = ['observation', 'hypothesis', 'experiment', 'analysis', 'conclusion'];
    
    correctOrder.forEach((item, index) => {
      const dropEvent = createMockDragEvent('drop', {
        getData: jest.fn().mockReturnValue(item),
      });
      fireEvent(dropZones[index], dropEvent);
    });
    
    const checkButton = screen.getByText('Check Answer');
    fireEvent.click(checkButton);
    
    await waitFor(() => {
      expect(screen.getByText('Next Question')).toBeInTheDocument();
    });
    
    // Click next question
    const nextButton = screen.getByText('Next Question');
    fireEvent.click(nextButton);
    
    // Should show new question
    await waitFor(() => {
      expect(screen.getByText(/Ecosystem Components/)).toBeInTheDocument();
    });
  });
});
