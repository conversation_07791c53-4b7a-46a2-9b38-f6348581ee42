import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onRetry?: () => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ChartErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Chart Error Boundary caught an error:', error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Card className="h-[300px] w-full">
          <CardContent className="h-full flex items-center justify-center">
            <div className="text-center space-y-4">
              <AlertTriangle className="h-12 w-12 text-destructive mx-auto" />
              <div>
                <h3 className="font-semibold text-lg mb-2">Chart Error</h3>
                <p className="text-muted-foreground text-sm mb-4">
                  Unable to render chart. Please try again.
                </p>
                {this.state.error && (
                  <p className="text-xs text-muted-foreground mb-4 font-mono">
                    {this.state.error.message}
                  </p>
                )}
              </div>
              <Button 
                onClick={this.handleRetry}
                variant="outline"
                size="sm"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

// Hook version for functional components
export const useChartErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  return { error, resetError, handleError };
};

// Higher-order component for wrapping charts
export const withChartErrorBoundary = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  const WithErrorBoundary = (props: P) => (
    <ChartErrorBoundary fallback={fallback}>
      <WrappedComponent {...props} />
    </ChartErrorBoundary>
  );

  WithErrorBoundary.displayName = `withChartErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;
  
  return WithErrorBoundary;
};

// Loading skeleton for charts
export const ChartSkeleton = ({ height = 300 }: { height?: number }) => (
  <div 
    className="w-full animate-pulse bg-muted rounded-lg flex items-center justify-center"
    style={{ height: `${height}px` }}
  >
    <div className="text-center">
      <div className="h-8 w-8 bg-muted-foreground/20 rounded-full mx-auto mb-2" />
      <div className="h-4 w-24 bg-muted-foreground/20 rounded mx-auto" />
    </div>
  </div>
);

// Empty state for charts
export const ChartEmptyState = ({ 
  title = "No Data Available", 
  description = "Complete more tests to see your analytics",
  icon: Icon = AlertTriangle,
  height = 300 
}: { 
  title?: string; 
  description?: string; 
  icon?: React.ComponentType<{ className?: string }>;
  height?: number;
}) => (
  <div 
    className="w-full flex items-center justify-center"
    style={{ height: `${height}px` }}
  >
    <div className="text-center">
      <Icon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
      <h3 className="font-semibold text-lg mb-2">{title}</h3>
      <p className="text-muted-foreground text-sm">{description}</p>
    </div>
  </div>
);
