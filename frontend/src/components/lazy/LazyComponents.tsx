import React, { Suspense, lazy } from 'react';
import { LoadingSpinner, CardSkeleton, DashboardSkeleton, ChatSkeleton } from '@/components/ui/loading-states';
import ErrorBoundary from '@/components/error/ErrorBoundary';

// Lazy load heavy components
const LazyLiveAnalyticsDashboard = lazy(() => 
  import('@/components/analytics/LiveAnalyticsDashboard').then(module => ({
    default: module.LiveAnalyticsDashboard
  }))
);

const LazyAIChatInterface = lazy(() => 
  import('@/components/ai-tutor/AIChatInterface').then(module => ({
    default: module.AIChatInterface
  }))
);

const LazyProgressSummary = lazy(() => 
  import('@/components/progress/ProgressSummary').then(module => ({
    default: module.ProgressSummary
  }))
);

const LazyLiveLeaderboard = lazy(() => 
  import('@/components/gamification/LiveLeaderboard').then(module => ({
    default: module.LiveLeaderboard
  }))
);

const LazyLiveActivityFeed = lazy(() => 
  import('@/components/gamification/LiveActivityFeed').then(module => ({
    default: module.LiveActivityFeed
  }))
);

const LazyInteractiveDragDrop = lazy(() => 
  import('@/components/demos/InteractiveDragDrop').then(module => ({
    default: module.InteractiveDragDrop
  }))
);

const LazyInteractiveMathDemo = lazy(() => 
  import('@/components/demos/InteractiveMathDemo').then(module => ({
    default: module.InteractiveMathDemo
  }))
);

const LazyInteractiveCodeDemo = lazy(() => 
  import('@/components/demos/InteractiveCodeDemo').then(module => ({
    default: module.InteractiveCodeDemo
  }))
);

// Wrapper components with error boundaries and loading states
export const LiveAnalyticsDashboard: React.FC = () => (
  <ErrorBoundary fallback={<div className="text-center p-8">Failed to load analytics dashboard</div>}>
    <Suspense fallback={<DashboardSkeleton />}>
      <LazyLiveAnalyticsDashboard />
    </Suspense>
  </ErrorBoundary>
);

export const AIChatInterface: React.FC<{
  context?: string;
  maxHeight?: string;
}> = (props) => (
  <ErrorBoundary fallback={<div className="text-center p-4">Failed to load AI chat</div>}>
    <Suspense fallback={<ChatSkeleton />}>
      <LazyAIChatInterface {...props} />
    </Suspense>
  </ErrorBoundary>
);

export const ProgressSummary: React.FC = () => (
  <ErrorBoundary fallback={<div className="text-center p-8">Failed to load progress summary</div>}>
    <Suspense fallback={<DashboardSkeleton />}>
      <LazyProgressSummary />
    </Suspense>
  </ErrorBoundary>
);

export const LiveLeaderboard: React.FC<{
  maxEntries?: number;
  showRankChanges?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}> = (props) => (
  <ErrorBoundary fallback={<div className="text-center p-4">Failed to load leaderboard</div>}>
    <Suspense fallback={<CardSkeleton lines={8} />}>
      <LazyLiveLeaderboard {...props} />
    </Suspense>
  </ErrorBoundary>
);

export const LiveActivityFeed: React.FC<{
  showGlobalActivity?: boolean;
  maxItems?: number;
  compact?: boolean;
}> = (props) => (
  <ErrorBoundary fallback={<div className="text-center p-4">Failed to load activity feed</div>}>
    <Suspense fallback={<CardSkeleton lines={5} />}>
      <LazyLiveActivityFeed {...props} />
    </Suspense>
  </ErrorBoundary>
);

export const InteractiveDragDrop: React.FC = () => (
  <ErrorBoundary fallback={<div className="text-center p-4">Failed to load drag & drop demo</div>}>
    <Suspense fallback={<CardSkeleton lines={6} />}>
      <LazyInteractiveDragDrop />
    </Suspense>
  </ErrorBoundary>
);

export const InteractiveMathDemo: React.FC = () => (
  <ErrorBoundary fallback={<div className="text-center p-4">Failed to load math demo</div>}>
    <Suspense fallback={<CardSkeleton lines={6} />}>
      <LazyInteractiveMathDemo />
    </Suspense>
  </ErrorBoundary>
);

export const InteractiveCodeDemo: React.FC = () => (
  <ErrorBoundary fallback={<div className="text-center p-4">Failed to load code demo</div>}>
    <Suspense fallback={<CardSkeleton lines={6} />}>
      <LazyInteractiveCodeDemo />
    </Suspense>
  </ErrorBoundary>
);

// Preload components for better UX
export const preloadComponents = {
  analytics: () => import('@/components/analytics/LiveAnalyticsDashboard'),
  aiChat: () => import('@/components/ai-tutor/AIChatInterface'),
  progress: () => import('@/components/progress/ProgressSummary'),
  leaderboard: () => import('@/components/gamification/LiveLeaderboard'),
  activityFeed: () => import('@/components/gamification/LiveActivityFeed'),
  dragDrop: () => import('@/components/demos/InteractiveDragDrop'),
  mathDemo: () => import('@/components/demos/InteractiveMathDemo'),
  codeDemo: () => import('@/components/demos/InteractiveCodeDemo'),
};

// Preload on hover or focus
export const usePreloadOnHover = (componentKey: keyof typeof preloadComponents) => {
  const preload = React.useCallback(() => {
    preloadComponents[componentKey]();
  }, [componentKey]);

  return {
    onMouseEnter: preload,
    onFocus: preload,
  };
};

// Performance monitoring hook
export const usePerformanceMonitor = (componentName: string) => {
  React.useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Log performance in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`);
      }
      
      // In production, you might send this to analytics
      if (renderTime > 1000) { // Log slow renders
        console.warn(`Slow render detected: ${componentName} took ${renderTime.toFixed(2)}ms`);
      }
    };
  }, [componentName]);
};

// Memoized wrapper for expensive components
export const withMemoization = <P extends object>(
  Component: React.ComponentType<P>,
  areEqual?: (prevProps: P, nextProps: P) => boolean
) => {
  const MemoizedComponent = React.memo(Component, areEqual);
  MemoizedComponent.displayName = `Memoized(${Component.displayName || Component.name})`;
  return MemoizedComponent;
};

// Virtual scrolling for large lists
export const VirtualizedList: React.FC<{
  items: any[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: any, index: number) => React.ReactNode;
  overscan?: number;
}> = ({ items, itemHeight, containerHeight, renderItem, overscan = 5 }) => {
  const [scrollTop, setScrollTop] = React.useState(0);
  
  const visibleStart = Math.floor(scrollTop / itemHeight);
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight),
    items.length - 1
  );
  
  const startIndex = Math.max(0, visibleStart - overscan);
  const endIndex = Math.min(items.length - 1, visibleEnd + overscan);
  
  const visibleItems = items.slice(startIndex, endIndex + 1);
  
  return (
    <div
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={(e) => setScrollTop(e.currentTarget.scrollTop)}
    >
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${startIndex * itemHeight}px)` }}>
          {visibleItems.map((item, index) => (
            <div key={startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Image lazy loading with intersection observer
export const LazyImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
  placeholder?: string;
}> = ({ src, alt, className, placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+' }) => {
  const [imageSrc, setImageSrc] = React.useState(placeholder);
  const [imageRef, setImageRef] = React.useState<HTMLImageElement | null>(null);

  React.useEffect(() => {
    let observer: IntersectionObserver;
    
    if (imageRef && imageSrc === placeholder) {
      observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              setImageSrc(src);
              observer.unobserve(imageRef);
            }
          });
        },
        { threshold: 0.1 }
      );
      
      observer.observe(imageRef);
    }
    
    return () => {
      if (observer && imageRef) {
        observer.unobserve(imageRef);
      }
    };
  }, [imageRef, imageSrc, src, placeholder]);

  return (
    <img
      ref={setImageRef}
      src={imageSrc}
      alt={alt}
      className={className}
      loading="lazy"
    />
  );
};
