import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Bot, 
  User, 
  Send, 
  Loader2, 
  Lightbulb,
  BookOpen,
  Calculator,
  Code,
  Trash2,
  Sparkles
} from 'lucide-react';
import { useGamificationStore } from '@/stores/gamificationStore';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  context?: string; // What demo/topic this relates to
}

interface AIChatInterfaceProps {
  context?: string; // Current demo context
  maxHeight?: string;
}

export const AIChatInterface: React.FC<AIChatInterfaceProps> = ({
  context,
  maxHeight = "400px",
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'assistant',
      content: "Hi! I'm <PERSON>, your AI learning assistant. I'm here to help you understand concepts better and guide you through the interactive demos. What would you like to work on today?",
      timestamp: new Date(),
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const { userStats, awardXP } = useGamificationStore();

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages, isTyping]);

  // Simulate AI response with context awareness
  const generateAIResponse = async (userMessage: string): Promise<string> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));

    const lowerMessage = userMessage.toLowerCase();
    
    // Context-aware responses
    if (context === 'drag-drop' || lowerMessage.includes('drag') || lowerMessage.includes('drop')) {
      const responses = [
        "Great question about drag and drop! The key is to understand the logical sequence. For the scientific method, think about what comes first - observation or hypothesis?",
        "Drag and drop questions test your understanding of order and relationships. Try to think about cause and effect when arranging items.",
        "I see you're working on sequencing! Remember, each step should logically lead to the next. What do you think is the foundation that everything else builds upon?"
      ];
      return responses[Math.floor(Math.random() * responses.length)];
    }

    if (context === 'math' || lowerMessage.includes('math') || lowerMessage.includes('equation')) {
      const responses = [
        "Math can be tricky, but let's break it down step by step! What specific part are you struggling with?",
        "For equations like this, I always recommend working backwards from what you know. What information do you have?",
        "Remember the order of operations: Parentheses, Exponents, Multiplication/Division, Addition/Subtraction. Which step are you on?",
        "Don't worry if it seems complex! Every math problem is just a series of smaller, simpler steps. Let's tackle them one at a time."
      ];
      return responses[Math.floor(Math.random() * responses.length)];
    }

    if (context === 'code' || lowerMessage.includes('code') || lowerMessage.includes('programming')) {
      const responses = [
        "Coding is all about breaking problems into smaller pieces! What's the main goal of your function?",
        "I love helping with code! Remember to think about inputs, processing, and outputs. What should your function receive and return?",
        "Programming tip: Start with the simplest case first, then handle edge cases. What's the most basic version of this problem?",
        "Debugging is part of learning! Read the error message carefully - it usually tells you exactly what's wrong."
      ];
      return responses[Math.floor(Math.random() * responses.length)];
    }

    // General responses
    if (lowerMessage.includes('help') || lowerMessage.includes('stuck')) {
      return "I'm here to help! Can you tell me more about what you're working on? The more specific you are, the better I can assist you.";
    }

    if (lowerMessage.includes('hint')) {
      return "Here's a hint: Try to think about the fundamental concepts first. What are the key principles that apply to this problem?";
    }

    if (lowerMessage.includes('thank')) {
      return "You're very welcome! I'm glad I could help. Keep up the great work - you're doing fantastic! 🌟";
    }

    // Default responses
    const defaultResponses = [
      "That's an interesting question! Can you provide a bit more context about what you're working on?",
      "I'd love to help you with that! What specific aspect would you like me to explain?",
      "Great thinking! Let's explore this together. What's your current understanding of the topic?",
      "I can see you're really engaged with learning! What would you like to dive deeper into?",
      "Excellent question! Learning is all about curiosity. What made you think about this?"
    ];

    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
      context,
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setIsTyping(true);

    try {
      const aiResponse = await generateAIResponse(userMessage.content);
      
      setIsTyping(false);
      
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: aiResponse,
        timestamp: new Date(),
        context,
      };

      setMessages(prev => [...prev, assistantMessage]);
      
      // Award XP for engaging with AI tutor
      awardXP(5, 'AI Tutor interaction');
      
    } catch (error) {
      setIsTyping(false);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: "I'm sorry, I'm having trouble responding right now. Please try again in a moment!",
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearChat = () => {
    setMessages([{
      id: '1',
      type: 'assistant',
      content: "Chat cleared! I'm ready to help you with anything you'd like to learn. What can I assist you with?",
      timestamp: new Date(),
    }]);
  };

  const getQuickActions = () => {
    const actions = [
      { label: "Explain this step", icon: Lightbulb },
      { label: "Give me a hint", icon: BookOpen },
      { label: "Show example", icon: Calculator },
      { label: "Help with code", icon: Code },
    ];

    return actions.map((action) => (
      <Button
        key={action.label}
        variant="outline"
        size="sm"
        onClick={() => setInputValue(action.label)}
        className="flex items-center gap-1 text-xs"
      >
        <action.icon className="h-3 w-3" />
        {action.label}
      </Button>
    ));
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-purple-500" />
            AI Learning Assistant
            <Badge className="bg-purple-100 text-purple-800">
              <Sparkles className="h-3 w-3 mr-1" />
              Smart
            </Badge>
          </CardTitle>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={clearChat}
            className="text-muted-foreground hover:text-foreground"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
        
        {context && (
          <Badge variant="outline" className="w-fit">
            Context: {context}
          </Badge>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Chat Messages */}
        <ScrollArea 
          className="border rounded-lg p-4" 
          style={{ height: maxHeight }}
          ref={scrollAreaRef}
        >
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${
                  message.type === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.type === 'assistant' && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-purple-100 text-purple-600">
                      <Bot className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    message.type === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <span className="text-xs opacity-70 mt-1 block">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                
                {message.type === 'user' && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
            
            {/* Typing Indicator */}
            {isTyping && (
              <div className="flex gap-3 justify-start">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="bg-purple-100 text-purple-600">
                    <Bot className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-muted rounded-lg p-3">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2">
          {getQuickActions()}
        </div>

        {/* Input Area */}
        <div className="flex gap-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me anything about the demos or concepts..."
            disabled={isLoading}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="px-3"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Stats */}
        <div className="text-center">
          <p className="text-xs text-muted-foreground">
            💡 Ask questions to earn +5 XP per interaction • {messages.filter(m => m.type === 'user').length} questions asked
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
