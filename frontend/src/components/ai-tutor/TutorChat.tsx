import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Send, 
  Bot, 
  User, 
  Lightbulb, 
  BookOpen, 
  Target,
  MessageCircle,
  Sparkles,
  ThumbsUp,
  ThumbsDown,
  MoreHorizontal,
  Mic,
  MicOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TutorMessage {
  id: string;
  sender: 'user' | 'tutor' | 'system';
  content: string;
  type: 'text' | 'question' | 'explanation' | 'hint' | 'encouragement' | 'correction' | 'resource' | 'suggestion';
  timestamp: string;
  metadata?: {
    intent?: string;
    confidence?: number;
    resources?: Array<{
      type: string;
      title: string;
      url: string;
      description: string;
    }>;
    actions?: Array<{
      type: string;
      description: string;
      priority: number;
    }>;
  };
}

interface TutorPersonality {
  type: 'mentor' | 'friend' | 'coach' | 'professor' | 'cheerleader';
  encouragement: number;
  patience: number;
  formality: number;
  humor: number;
}

interface TutorChatProps {
  tutorName: string;
  tutorPersonality: TutorPersonality;
  sessionType: 'help' | 'explanation' | 'review' | 'practice' | 'motivation' | 'strategy';
  onSendMessage: (message: string) => Promise<TutorMessage>;
  onEndSession: () => void;
  onFeedback: (messageId: string, rating: 'positive' | 'negative') => void;
}

export function TutorChat({
  tutorName,
  tutorPersonality,
  sessionType,
  onSendMessage,
  onEndSession,
  onFeedback
}: TutorChatProps) {
  const [messages, setMessages] = useState<TutorMessage[]>([
    {
      id: '1',
      sender: 'tutor',
      content: `Hi! I'm ${tutorName}, your AI learning assistant. I'm here to help you understand concepts better. What would you like to work on today?`,
      type: 'text',
      timestamp: new Date().toISOString()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: TutorMessage = {
      id: Date.now().toString(),
      sender: 'user',
      content: inputMessage,
      type: 'text',
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const tutorResponse = await onSendMessage(inputMessage);
      setMessages(prev => [...prev, tutorResponse]);
    } catch (error) {
      const errorMessage: TutorMessage = {
        id: Date.now().toString(),
        sender: 'system',
        content: 'Sorry, I encountered an error. Please try again.',
        type: 'text',
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'question': return <MessageCircle className="h-4 w-4" />;
      case 'explanation': return <BookOpen className="h-4 w-4" />;
      case 'hint': return <Lightbulb className="h-4 w-4" />;
      case 'encouragement': return <Sparkles className="h-4 w-4" />;
      case 'suggestion': return <Target className="h-4 w-4" />;
      default: return null;
    }
  };

  const getPersonalityColor = (type: string) => {
    switch (type) {
      case 'mentor': return 'bg-blue-500';
      case 'friend': return 'bg-green-500';
      case 'coach': return 'bg-orange-500';
      case 'professor': return 'bg-purple-500';
      case 'cheerleader': return 'bg-pink-500';
      default: return 'bg-gray-500';
    }
  };

  const renderMessage = (message: TutorMessage) => {
    const isUser = message.sender === 'user';
    const isSystem = message.sender === 'system';

    return (
      <div
        key={message.id}
        className={cn(
          "flex gap-3 mb-4",
          isUser ? "justify-end" : "justify-start"
        )}
      >
        {!isUser && (
          <Avatar className="h-8 w-8 mt-1">
            <AvatarFallback className={cn(
              "text-white text-sm",
              isSystem ? "bg-gray-500" : getPersonalityColor(tutorPersonality.type)
            )}>
              {isSystem ? 'S' : <Bot className="h-4 w-4" />}
            </AvatarFallback>
          </Avatar>
        )}

        <div className={cn(
          "max-w-[80%] space-y-2",
          isUser && "flex flex-col items-end"
        )}>
          <div className={cn(
            "rounded-lg px-4 py-2 text-sm",
            isUser 
              ? "bg-primary text-primary-foreground" 
              : isSystem
                ? "bg-gray-100 text-gray-800"
                : "bg-gray-100 text-gray-800"
          )}>
            {/* Message type indicator */}
            {!isUser && message.type !== 'text' && (
              <div className="flex items-center gap-2 mb-2 text-xs text-gray-600">
                {getMessageIcon(message.type)}
                <span className="capitalize">{message.type}</span>
              </div>
            )}

            <div className="whitespace-pre-wrap">{message.content}</div>

            {/* Resources */}
            {message.metadata?.resources && message.metadata.resources.length > 0 && (
              <div className="mt-3 space-y-2">
                <div className="text-xs font-medium text-gray-600">Helpful Resources:</div>
                {message.metadata.resources.map((resource, index) => (
                  <div key={index} className="bg-white rounded p-2 border">
                    <div className="font-medium text-xs">{resource.title}</div>
                    <div className="text-xs text-gray-600">{resource.description}</div>
                  </div>
                ))}
              </div>
            )}

            {/* Suggested Actions */}
            {message.metadata?.actions && message.metadata.actions.length > 0 && (
              <div className="mt-3 space-y-1">
                <div className="text-xs font-medium text-gray-600">Suggestions:</div>
                {message.metadata.actions.map((action, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    className="text-xs h-6"
                    onClick={() => setInputMessage(action.description)}
                  >
                    {action.description}
                  </Button>
                ))}
              </div>
            )}
          </div>

          {/* Message metadata */}
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
            
            {/* Feedback buttons for tutor messages */}
            {!isUser && !isSystem && (
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={() => onFeedback(message.id, 'positive')}
                >
                  <ThumbsUp className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={() => onFeedback(message.id, 'negative')}
                >
                  <ThumbsDown className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        </div>

        {isUser && (
          <Avatar className="h-8 w-8 mt-1">
            <AvatarFallback className="bg-primary text-primary-foreground">
              <User className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
        )}
      </div>
    );
  };

  return (
    <Card className="h-[600px] flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback className={cn(
                "text-white",
                getPersonalityColor(tutorPersonality.type)
              )}>
                <Bot className="h-5 w-5" />
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-semibold">{tutorName}</div>
              <div className="text-sm text-gray-500 capitalize">
                {tutorPersonality.type} • {sessionType} session
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              Active
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={onEndSession}
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages */}
        <ScrollArea className="flex-1 px-4">
          <div className="space-y-4 py-4">
            {messages.map(renderMessage)}
            {isLoading && (
              <div className="flex justify-start">
                <div className="flex items-center space-x-2 bg-gray-100 rounded-lg px-4 py-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                  <span className="text-sm text-gray-600">{tutorName} is thinking...</span>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Input */}
        <div className="border-t p-4">
          <div className="flex items-center space-x-2">
            <div className="flex-1 relative">
              <Input
                ref={inputRef}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask a question or describe what you need help with..."
                disabled={isLoading}
                className="pr-12"
              />
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                onClick={() => setIsListening(!isListening)}
                disabled={isLoading}
              >
                {isListening ? (
                  <MicOff className="h-4 w-4 text-red-500" />
                ) : (
                  <Mic className="h-4 w-4" />
                )}
              </Button>
            </div>
            
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isLoading}
              size="sm"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
            <span>Press Enter to send, Shift+Enter for new line</span>
            <span>{inputMessage.length}/500</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
