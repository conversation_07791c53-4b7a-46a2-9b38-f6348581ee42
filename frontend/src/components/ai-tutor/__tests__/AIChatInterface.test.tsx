import React from 'react';
import { render, screen, fireEvent, waitFor } from '@/test/test-utils';
import { AIChatInterface } from '../AIChatInterface';

describe('AIChatInterface', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the chat interface', () => {
    render(<AIChatInterface />);
    
    expect(screen.getByText('AI Learning Assistant')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Ask me anything about the demos or concepts...')).toBeInTheDocument();
  });

  it('displays initial AI message', () => {
    render(<AIChatInterface />);
    
    expect(screen.getByText(/Hi! I'm <PERSON>, your AI learning assistant/)).toBeInTheDocument();
  });

  it('shows context badge when context is provided', () => {
    render(<AIChatInterface context="math" />);
    
    expect(screen.getByText('Context: math')).toBeInTheDocument();
  });

  it('handles user message input', () => {
    render(<AIChatInterface />);
    
    const input = screen.getByPlaceholderText('Ask me anything about the demos or concepts...');
    fireEvent.change(input, { target: { value: 'Hello AI' } });
    
    expect(input).toHaveValue('Hello AI');
  });

  it('sends message on button click', async () => {
    render(<AIChatInterface />);
    
    const input = screen.getByPlaceholderText('Ask me anything about the demos or concepts...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(screen.getByText('Test message')).toBeInTheDocument();
    });
  });

  it('sends message on Enter key press', async () => {
    render(<AIChatInterface />);
    
    const input = screen.getByPlaceholderText('Ask me anything about the demos or concepts...');
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.keyPress(input, { key: 'Enter', code: 'Enter' });
    
    await waitFor(() => {
      expect(screen.getByText('Test message')).toBeInTheDocument();
    });
  });

  it('prevents sending empty messages', () => {
    render(<AIChatInterface />);
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    expect(sendButton).toBeDisabled();
  });

  it('shows loading state while AI responds', async () => {
    render(<AIChatInterface />);
    
    const input = screen.getByPlaceholderText('Ask me anything about the demos or concepts...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);
    
    // Should show loading spinner
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });
  });

  it('displays AI response after user message', async () => {
    render(<AIChatInterface />);
    
    const input = screen.getByPlaceholderText('Ask me anything about the demos or concepts...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Help with math' } });
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(screen.getByText('Help with math')).toBeInTheDocument();
    });
    
    // Wait for AI response
    await waitFor(() => {
      expect(screen.getByText(/Math can be tricky/)).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('shows typing indicator during AI response', async () => {
    render(<AIChatInterface />);
    
    const input = screen.getByPlaceholderText('Ask me anything about the demos or concepts...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);
    
    // Should show typing indicator
    await waitFor(() => {
      expect(screen.getByTestId('typing-indicator')).toBeInTheDocument();
    });
  });

  it('provides context-aware responses', async () => {
    render(<AIChatInterface context="drag-drop" />);
    
    const input = screen.getByPlaceholderText('Ask me anything about the demos or concepts...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Help with this demo' } });
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(screen.getByText(/drag and drop/i)).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('displays quick action buttons', () => {
    render(<AIChatInterface />);
    
    expect(screen.getByText('Explain this step')).toBeInTheDocument();
    expect(screen.getByText('Give me a hint')).toBeInTheDocument();
    expect(screen.getByText('Show example')).toBeInTheDocument();
    expect(screen.getByText('Help with code')).toBeInTheDocument();
  });

  it('handles quick action button clicks', () => {
    render(<AIChatInterface />);
    
    const hintButton = screen.getByText('Give me a hint');
    fireEvent.click(hintButton);
    
    const input = screen.getByPlaceholderText('Ask me anything about the demos or concepts...');
    expect(input).toHaveValue('Give me a hint');
  });

  it('clears chat when clear button is clicked', () => {
    render(<AIChatInterface />);
    
    const clearButton = screen.getByRole('button', { name: /clear/i });
    fireEvent.click(clearButton);
    
    // Should only show the initial AI message
    const messages = screen.getAllByText(/Hi! I'm Alex/);
    expect(messages).toHaveLength(1);
  });

  it('shows message timestamps', async () => {
    render(<AIChatInterface />);
    
    const input = screen.getByPlaceholderText('Ask me anything about the demos or concepts...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      // Should show timestamp
      expect(screen.getByText(/\d{1,2}:\d{2}:\d{2}/)).toBeInTheDocument();
    });
  });

  it('scrolls to bottom when new messages arrive', async () => {
    const scrollIntoViewMock = jest.fn();
    Element.prototype.scrollIntoView = scrollIntoViewMock;
    
    render(<AIChatInterface />);
    
    const input = screen.getByPlaceholderText('Ask me anything about the demos or concepts...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(scrollIntoViewMock).toHaveBeenCalled();
    });
  });

  it('awards XP for AI interactions', async () => {
    const { mockGamificationStore } = require('@/test/test-utils');
    
    render(<AIChatInterface />);
    
    const input = screen.getByPlaceholderText('Ask me anything about the demos or concepts...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(mockGamificationStore.awardXP).toHaveBeenCalledWith(5, 'AI Tutor interaction');
    });
  });

  it('handles API errors gracefully', async () => {
    // Mock console.error to avoid error logs in test output
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    
    render(<AIChatInterface />);
    
    const input = screen.getByPlaceholderText('Ask me anything about the demos or concepts...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(screen.getByText(/I'm sorry, I'm having trouble responding/)).toBeInTheDocument();
    }, { timeout: 3000 });
    
    consoleSpy.mockRestore();
  });
});
