import React, { useState, useCallback, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, RotateCcw, Lightbulb } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DragItem {
  id: string;
  content: string;
  category?: string;
  media?: MediaElement[];
}

interface DropZone {
  id: string;
  label: string;
  acceptedItems: string[];
  maxItems: number;
  position: { x: number; y: number };
  feedback?: string;
}

interface MediaElement {
  id: string;
  type: 'image' | 'audio' | 'video';
  url: string;
  caption?: string;
  altText?: string;
}

interface DragDropQuestionProps {
  question: string;
  instructions: string;
  dragItems: DragItem[];
  dropZones: DropZone[];
  explanation: string;
  onAnswer: (answer: Record<string, string[]>) => void;
  showFeedback?: boolean;
  isCorrect?: boolean;
  disabled?: boolean;
}

export function DragDropQuestion({
  question,
  instructions,
  dragItems,
  dropZones,
  explanation,
  onAnswer,
  showFeedback = false,
  isCorrect = false,
  disabled = false
}: DragDropQuestionProps) {
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [dropZoneItems, setDropZoneItems] = useState<Record<string, string[]>>({});
  const [availableItems, setAvailableItems] = useState<string[]>(
    dragItems.map(item => item.id)
  );
  const [showHint, setShowHint] = useState(false);
  const dragRef = useRef<HTMLDivElement>(null);

  const handleDragStart = useCallback((e: React.DragEvent, itemId: string) => {
    if (disabled) return;
    setDraggedItem(itemId);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', itemId);
  }, [disabled]);

  const handleDragEnd = useCallback(() => {
    setDraggedItem(null);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }, []);

  const handleDrop = useCallback((e: React.DragEvent, zoneId: string) => {
    e.preventDefault();
    if (disabled) return;

    const itemId = e.dataTransfer.getData('text/plain');
    const zone = dropZones.find(z => z.id === zoneId);
    
    if (!zone || !itemId) return;

    // Check if zone accepts this item
    if (zone.acceptedItems.length > 0 && !zone.acceptedItems.includes(itemId)) {
      return; // Item not accepted in this zone
    }

    // Check if zone has space
    const currentItems = dropZoneItems[zoneId] || [];
    if (currentItems.length >= zone.maxItems) {
      return; // Zone is full
    }

    // Move item to drop zone
    setDropZoneItems(prev => ({
      ...prev,
      [zoneId]: [...(prev[zoneId] || []), itemId]
    }));

    // Remove from available items or other zones
    setAvailableItems(prev => prev.filter(id => id !== itemId));
    setDropZoneItems(prev => {
      const updated = { ...prev };
      Object.keys(updated).forEach(key => {
        if (key !== zoneId) {
          updated[key] = updated[key]?.filter(id => id !== itemId) || [];
        }
      });
      return updated;
    });

    setDraggedItem(null);
  }, [disabled, dropZones, dropZoneItems]);

  const handleRemoveFromZone = useCallback((zoneId: string, itemId: string) => {
    if (disabled) return;

    setDropZoneItems(prev => ({
      ...prev,
      [zoneId]: (prev[zoneId] || []).filter(id => id !== itemId)
    }));
    setAvailableItems(prev => [...prev, itemId]);
  }, [disabled]);

  const handleReset = useCallback(() => {
    setDropZoneItems({});
    setAvailableItems(dragItems.map(item => item.id));
    setShowHint(false);
  }, [dragItems]);

  const handleSubmit = useCallback(() => {
    onAnswer(dropZoneItems);
  }, [dropZoneItems, onAnswer]);

  const renderDragItem = (item: DragItem, isInZone = false, zoneId?: string) => (
    <div
      key={item.id}
      draggable={!disabled}
      onDragStart={(e) => handleDragStart(e, item.id)}
      onDragEnd={handleDragEnd}
      className={cn(
        "p-3 bg-white border-2 border-dashed border-gray-300 rounded-lg cursor-move",
        "hover:border-primary hover:bg-primary/5 transition-colors",
        "select-none shadow-sm",
        draggedItem === item.id && "opacity-50",
        disabled && "cursor-not-allowed opacity-60",
        isInZone && "border-solid border-primary bg-primary/10"
      )}
    >
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium">{item.content}</span>
        {isInZone && zoneId && (
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleRemoveFromZone(zoneId, item.id)}
            disabled={disabled}
            className="h-6 w-6 p-0"
          >
            <XCircle className="h-4 w-4" />
          </Button>
        )}
      </div>
      {item.category && (
        <Badge variant="secondary" className="mt-1 text-xs">
          {item.category}
        </Badge>
      )}
    </div>
  );

  const renderDropZone = (zone: DropZone) => {
    const items = dropZoneItems[zone.id] || [];
    const isEmpty = items.length === 0;
    const isFull = items.length >= zone.maxItems;

    return (
      <div
        key={zone.id}
        onDragOver={handleDragOver}
        onDrop={(e) => handleDrop(e, zone.id)}
        className={cn(
          "min-h-[120px] p-4 border-2 border-dashed rounded-lg",
          "transition-colors duration-200",
          isEmpty ? "border-gray-300 bg-gray-50" : "border-primary bg-primary/5",
          draggedItem && !isFull && "border-primary bg-primary/10",
          isFull && "border-green-500 bg-green-50"
        )}
      >
        <div className="text-sm font-medium text-gray-700 mb-2">
          {zone.label}
          {zone.maxItems > 1 && (
            <span className="text-xs text-gray-500 ml-2">
              ({items.length}/{zone.maxItems})
            </span>
          )}
        </div>
        
        <div className="space-y-2">
          {items.map(itemId => {
            const item = dragItems.find(i => i.id === itemId);
            return item ? renderDragItem(item, true, zone.id) : null;
          })}
          
          {isEmpty && (
            <div className="text-center text-gray-400 py-8">
              Drop items here
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        {/* Question Header */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">{question}</h3>
          <p className="text-gray-600 text-sm">{instructions}</p>
        </div>

        {/* Drag Items Pool */}
        <div className="mb-6">
          <h4 className="text-md font-medium mb-3">Available Items</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {availableItems.map(itemId => {
              const item = dragItems.find(i => i.id === itemId);
              return item ? renderDragItem(item) : null;
            })}
          </div>
          {availableItems.length === 0 && (
            <div className="text-center text-gray-400 py-4">
              All items have been placed
            </div>
          )}
        </div>

        {/* Drop Zones */}
        <div className="mb-6">
          <h4 className="text-md font-medium mb-3">Drop Zones</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {dropZones.map(renderDropZone)}
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={disabled}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHint(!showHint)}
              disabled={disabled}
            >
              <Lightbulb className="h-4 w-4 mr-2" />
              Hint
            </Button>
          </div>
          
          <Button
            onClick={handleSubmit}
            disabled={disabled || Object.keys(dropZoneItems).length === 0}
            className="btn-gradient"
          >
            Submit Answer
          </Button>
        </div>

        {/* Hint */}
        {showHint && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              💡 Try grouping items by their categories or relationships
            </p>
          </div>
        )}

        {/* Feedback */}
        {showFeedback && (
          <div className={cn(
            "mt-4 p-4 rounded-lg border",
            isCorrect ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
          )}>
            <div className="flex items-center mb-2">
              {isCorrect ? (
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600 mr-2" />
              )}
              <span className={cn(
                "font-medium",
                isCorrect ? "text-green-800" : "text-red-800"
              )}>
                {isCorrect ? "Correct!" : "Incorrect"}
              </span>
            </div>
            <p className={cn(
              "text-sm",
              isCorrect ? "text-green-700" : "text-red-700"
            )}>
              {explanation}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
