import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Settings, 
  CheckCircle, 
  XCircle,
  Zap,
  Beaker,
  Atom,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SimulationParameter {
  name: string;
  value: number;
  min: number;
  max: number;
  step: number;
  unit?: string;
  description: string;
}

interface SimulationResult {
  parameter: string;
  value: number | string;
  tolerance: number;
}

interface SimulationQuestionProps {
  question: string;
  instructions: string;
  simulationType: 'physics' | 'chemistry' | 'biology' | 'math' | 'engineering';
  parameters: Record<string, any>;
  expectedResults: SimulationResult[];
  explanation: string;
  interactionMode: 'click' | 'drag' | 'touch';
  onSubmit: (results: Record<string, any>) => void;
  showFeedback?: boolean;
  isCorrect?: boolean;
  disabled?: boolean;
}

export function SimulationQuestion({
  question,
  instructions,
  simulationType,
  parameters,
  expectedResults,
  explanation,
  interactionMode,
  onSubmit,
  showFeedback = false,
  isCorrect = false,
  disabled = false
}: SimulationQuestionProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [currentParams, setCurrentParams] = useState<Record<string, number>>(parameters);
  const [simulationResults, setSimulationResults] = useState<Record<string, any>>({});
  const [animationFrame, setAnimationFrame] = useState(0);
  const [activeTab, setActiveTab] = useState('simulation');
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  // Simulation parameters based on type
  const getSimulationParams = useCallback((): SimulationParameter[] => {
    switch (simulationType) {
      case 'physics':
        return [
          { name: 'mass', value: currentParams.mass || 1, min: 0.1, max: 10, step: 0.1, unit: 'kg', description: 'Object mass' },
          { name: 'velocity', value: currentParams.velocity || 5, min: 0, max: 20, step: 0.5, unit: 'm/s', description: 'Initial velocity' },
          { name: 'angle', value: currentParams.angle || 45, min: 0, max: 90, step: 1, unit: '°', description: 'Launch angle' },
          { name: 'gravity', value: currentParams.gravity || 9.8, min: 1, max: 20, step: 0.1, unit: 'm/s²', description: 'Gravitational acceleration' }
        ];
      case 'chemistry':
        return [
          { name: 'concentration', value: currentParams.concentration || 0.1, min: 0.01, max: 1, step: 0.01, unit: 'M', description: 'Concentration' },
          { name: 'temperature', value: currentParams.temperature || 298, min: 273, max: 373, step: 1, unit: 'K', description: 'Temperature' },
          { name: 'pressure', value: currentParams.pressure || 1, min: 0.1, max: 5, step: 0.1, unit: 'atm', description: 'Pressure' },
          { name: 'volume', value: currentParams.volume || 1, min: 0.1, max: 10, step: 0.1, unit: 'L', description: 'Volume' }
        ];
      default:
        return [
          { name: 'parameter1', value: currentParams.parameter1 || 1, min: 0, max: 10, step: 0.1, unit: '', description: 'Parameter 1' },
          { name: 'parameter2', value: currentParams.parameter2 || 1, min: 0, max: 10, step: 0.1, unit: '', description: 'Parameter 2' }
        ];
    }
  }, [simulationType, currentParams]);

  const updateParameter = useCallback((name: string, value: number) => {
    setCurrentParams(prev => ({ ...prev, [name]: value }));
  }, []);

  const runSimulation = useCallback(() => {
    if (disabled) return;
    
    setIsRunning(true);
    setAnimationFrame(0);
    
    // Mock simulation logic based on type
    const simulate = () => {
      setAnimationFrame(prev => {
        const newFrame = prev + 1;
        
        // Update simulation results based on current parameters
        const results: Record<string, any> = {};
        
        switch (simulationType) {
          case 'physics':
            // Projectile motion simulation
            const { mass, velocity, angle, gravity } = currentParams;
            const angleRad = (angle * Math.PI) / 180;
            const time = newFrame * 0.1;
            
            results.x = velocity * Math.cos(angleRad) * time;
            results.y = velocity * Math.sin(angleRad) * time - 0.5 * gravity * time * time;
            results.maxHeight = (velocity * Math.sin(angleRad)) ** 2 / (2 * gravity);
            results.range = (velocity ** 2 * Math.sin(2 * angleRad)) / gravity;
            break;
            
          case 'chemistry':
            // Chemical reaction simulation
            const { concentration, temperature, pressure } = currentParams;
            results.reactionRate = concentration * Math.exp(-5000 / temperature) * pressure;
            results.equilibrium = concentration * (temperature / 298) * pressure;
            break;
            
          default:
            results.output = currentParams.parameter1 * currentParams.parameter2;
        }
        
        setSimulationResults(results);
        
        // Continue animation for a certain duration
        if (newFrame < 100) {
          animationRef.current = requestAnimationFrame(simulate);
        } else {
          setIsRunning(false);
        }
        
        return newFrame;
      });
    };
    
    simulate();
  }, [currentParams, simulationType, disabled]);

  const stopSimulation = useCallback(() => {
    setIsRunning(false);
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
  }, []);

  const resetSimulation = useCallback(() => {
    stopSimulation();
    setCurrentParams(parameters);
    setSimulationResults({});
    setAnimationFrame(0);
  }, [parameters, stopSimulation]);

  const handleSubmit = useCallback(() => {
    onSubmit(simulationResults);
  }, [simulationResults, onSubmit]);

  // Canvas drawing for visualization
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw simulation visualization based on type
    switch (simulationType) {
      case 'physics':
        // Draw projectile motion
        if (simulationResults.x !== undefined && simulationResults.y !== undefined) {
          const x = simulationResults.x * 10; // Scale for canvas
          const y = canvas.height - (simulationResults.y * 10 + 50); // Flip Y and offset
          
          // Draw trajectory
          ctx.strokeStyle = '#3b82f6';
          ctx.lineWidth = 2;
          ctx.beginPath();
          ctx.arc(Math.max(0, x), Math.max(0, y), 5, 0, 2 * Math.PI);
          ctx.fill();
          
          // Draw ground
          ctx.strokeStyle = '#22c55e';
          ctx.lineWidth = 3;
          ctx.beginPath();
          ctx.moveTo(0, canvas.height - 50);
          ctx.lineTo(canvas.width, canvas.height - 50);
          ctx.stroke();
        }
        break;
        
      case 'chemistry':
        // Draw molecular representation
        ctx.fillStyle = '#ef4444';
        ctx.beginPath();
        ctx.arc(100, 100, 20, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.fillStyle = '#3b82f6';
        ctx.beginPath();
        ctx.arc(200, 100, 20, 0, 2 * Math.PI);
        ctx.fill();
        
        // Draw reaction progress
        const progress = (animationFrame / 100) * 100;
        ctx.fillStyle = '#22c55e';
        ctx.fillRect(50, 150, progress * 2, 20);
        break;
    }
  }, [simulationType, simulationResults, animationFrame]);

  const getSimulationIcon = () => {
    switch (simulationType) {
      case 'physics': return <Zap className="h-5 w-5" />;
      case 'chemistry': return <Beaker className="h-5 w-5" />;
      case 'biology': return <Activity className="h-5 w-5" />;
      default: return <Atom className="h-5 w-5" />;
    }
  };

  const simParams = getSimulationParams();

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getSimulationIcon()}
            <span>Interactive Simulation</span>
            <Badge variant="secondary" className="capitalize">
              {simulationType}
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6">
        {/* Question */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">{question}</h3>
          <p className="text-gray-600 text-sm">{instructions}</p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="simulation">Simulation</TabsTrigger>
            <TabsTrigger value="parameters">Parameters</TabsTrigger>
            <TabsTrigger value="results">Results</TabsTrigger>
          </TabsList>

          <TabsContent value="simulation" className="space-y-4">
            {/* Simulation Canvas */}
            <div className="border rounded-lg p-4 bg-gray-50">
              <canvas
                ref={canvasRef}
                width={400}
                height={300}
                className="w-full border rounded bg-white"
              />
            </div>
            
            {/* Simulation Controls */}
            <div className="flex items-center justify-center space-x-4">
              <Button
                onClick={isRunning ? stopSimulation : runSimulation}
                disabled={disabled}
                variant={isRunning ? "destructive" : "default"}
                className="btn-gradient"
              >
                {isRunning ? (
                  <>
                    <Pause className="h-4 w-4 mr-2" />
                    Stop
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Run Simulation
                  </>
                )}
              </Button>
              
              <Button
                onClick={resetSimulation}
                disabled={disabled}
                variant="outline"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="parameters" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {simParams.map((param) => (
                <div key={param.name} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium capitalize">
                      {param.name.replace(/([A-Z])/g, ' $1')}
                    </label>
                    <span className="text-sm text-gray-500">
                      {param.value.toFixed(param.step < 1 ? 2 : 0)} {param.unit}
                    </span>
                  </div>
                  <Slider
                    value={[param.value]}
                    onValueChange={([value]) => updateParameter(param.name, value)}
                    min={param.min}
                    max={param.max}
                    step={param.step}
                    disabled={disabled}
                    className="w-full"
                  />
                  <p className="text-xs text-gray-500">{param.description}</p>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="results" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(simulationResults).map(([key, value]) => (
                <Card key={key} className="p-4">
                  <div className="text-sm font-medium text-gray-700 capitalize mb-1">
                    {key.replace(/([A-Z])/g, ' $1')}
                  </div>
                  <div className="text-lg font-semibold">
                    {typeof value === 'number' ? value.toFixed(3) : value}
                  </div>
                </Card>
              ))}
            </div>
            
            {Object.keys(simulationResults).length === 0 && (
              <div className="text-center text-gray-500 py-8">
                Run the simulation to see results
              </div>
            )}
            
            {Object.keys(simulationResults).length > 0 && (
              <div className="flex justify-end">
                <Button
                  onClick={handleSubmit}
                  disabled={disabled}
                  className="btn-gradient"
                >
                  Submit Results
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Feedback */}
        {showFeedback && (
          <div className={cn(
            "mt-6 p-4 rounded-lg border",
            isCorrect ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
          )}>
            <div className="flex items-center mb-2">
              {isCorrect ? (
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600 mr-2" />
              )}
              <span className={cn(
                "font-medium",
                isCorrect ? "text-green-800" : "text-red-800"
              )}>
                {isCorrect ? "Simulation Completed Successfully!" : "Results Need Review"}
              </span>
            </div>
            <p className={cn(
              "text-sm",
              isCorrect ? "text-green-700" : "text-red-700"
            )}>
              {explanation}
            </p>
            
            {!isCorrect && expectedResults.length > 0 && (
              <div className="mt-3">
                <p className="text-sm font-medium text-red-800 mb-2">Expected Results:</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {expectedResults.map((result, index) => (
                    <div key={index} className="text-xs bg-red-100 p-2 rounded">
                      <span className="font-medium">{result.parameter}:</span> {result.value}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
