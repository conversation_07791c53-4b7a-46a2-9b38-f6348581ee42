import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calculator, 
  CheckCircle, 
  XCircle, 
  RotateCcw, 
  Lightbulb,
  Eye,
  EyeOff,
  Function
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MathExpressionQuestionProps {
  question: string;
  instructions: string;
  expectedAnswer: string;
  alternateAnswers: string[];
  tolerance: number;
  units?: string;
  showSteps: boolean;
  explanation: string;
  mathRenderer: 'latex' | 'mathml' | 'ascii';
  onAnswer: (answer: string, steps?: string[]) => void;
  showFeedback?: boolean;
  isCorrect?: boolean;
  disabled?: boolean;
}

interface MathStep {
  step: string;
  explanation: string;
  latex?: string;
}

export function MathExpressionQuestion({
  question,
  instructions,
  expectedAnswer,
  alternateAnswers,
  tolerance,
  units,
  showSteps,
  explanation,
  mathRenderer,
  onAnswer,
  showFeedback = false,
  isCorrect = false,
  disabled = false
}: MathExpressionQuestionProps) {
  const [userAnswer, setUserAnswer] = useState('');
  const [userSteps, setUserSteps] = useState<string[]>(['']);
  const [showStepByStep, setShowStepByStep] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [previewMode, setPreviewMode] = useState<'input' | 'rendered'>('input');
  const [activeTab, setActiveTab] = useState('answer');
  const inputRef = useRef<HTMLInputElement>(null);

  // Math symbols palette
  const mathSymbols = [
    { symbol: '√', latex: '\\sqrt{}', description: 'Square root' },
    { symbol: 'x²', latex: '^2', description: 'Squared' },
    { symbol: 'x³', latex: '^3', description: 'Cubed' },
    { symbol: '∫', latex: '\\int', description: 'Integral' },
    { symbol: '∑', latex: '\\sum', description: 'Sum' },
    { symbol: '∞', latex: '\\infty', description: 'Infinity' },
    { symbol: 'π', latex: '\\pi', description: 'Pi' },
    { symbol: 'θ', latex: '\\theta', description: 'Theta' },
    { symbol: '≤', latex: '\\leq', description: 'Less than or equal' },
    { symbol: '≥', latex: '\\geq', description: 'Greater than or equal' },
    { symbol: '≠', latex: '\\neq', description: 'Not equal' },
    { symbol: '±', latex: '\\pm', description: 'Plus minus' },
    { symbol: '÷', latex: '\\div', description: 'Division' },
    { symbol: '×', latex: '\\times', description: 'Multiplication' },
    { symbol: '∂', latex: '\\partial', description: 'Partial derivative' },
    { symbol: 'α', latex: '\\alpha', description: 'Alpha' },
    { symbol: 'β', latex: '\\beta', description: 'Beta' },
    { symbol: 'γ', latex: '\\gamma', description: 'Gamma' },
  ];

  const insertSymbol = useCallback((latex: string) => {
    if (!inputRef.current || disabled) return;
    
    const input = inputRef.current;
    const start = input.selectionStart || 0;
    const end = input.selectionEnd || 0;
    const newValue = userAnswer.slice(0, start) + latex + userAnswer.slice(end);
    
    setUserAnswer(newValue);
    
    // Set cursor position after inserted symbol
    setTimeout(() => {
      const newPosition = start + latex.length;
      input.setSelectionRange(newPosition, newPosition);
      input.focus();
    }, 0);
  }, [userAnswer, disabled]);

  const addStep = useCallback(() => {
    setUserSteps(prev => [...prev, '']);
  }, []);

  const updateStep = useCallback((index: number, value: string) => {
    setUserSteps(prev => prev.map((step, i) => i === index ? value : step));
  }, []);

  const removeStep = useCallback((index: number) => {
    setUserSteps(prev => prev.filter((_, i) => i !== index));
  }, []);

  const handleSubmit = useCallback(() => {
    const steps = showSteps ? userSteps.filter(step => step.trim()) : undefined;
    onAnswer(userAnswer, steps);
  }, [userAnswer, userSteps, showSteps, onAnswer]);

  const handleReset = useCallback(() => {
    setUserAnswer('');
    setUserSteps(['']);
    setShowHint(false);
    setShowStepByStep(false);
  }, []);

  // Mock LaTeX rendering - in production, use a library like KaTeX or MathJax
  const renderMath = useCallback((expression: string) => {
    if (mathRenderer === 'latex') {
      // This would be replaced with actual LaTeX rendering
      return (
        <div className="math-expression bg-white p-2 border rounded font-mono text-center">
          {expression}
        </div>
      );
    }
    return <span className="font-mono">{expression}</span>;
  }, [mathRenderer]);

  const validateAnswer = useCallback((answer: string): boolean => {
    // Simple validation - in production, use a math parser
    const normalizedAnswer = answer.trim().toLowerCase();
    const normalizedExpected = expectedAnswer.trim().toLowerCase();
    
    // Check exact match
    if (normalizedAnswer === normalizedExpected) return true;
    
    // Check alternate answers
    if (alternateAnswers.some(alt => alt.trim().toLowerCase() === normalizedAnswer)) {
      return true;
    }
    
    // Check numerical tolerance if both are numbers
    const userNum = parseFloat(normalizedAnswer);
    const expectedNum = parseFloat(normalizedExpected);
    
    if (!isNaN(userNum) && !isNaN(expectedNum)) {
      return Math.abs(userNum - expectedNum) <= tolerance;
    }
    
    return false;
  }, [expectedAnswer, alternateAnswers, tolerance]);

  const isAnswerValid = userAnswer.trim() && validateAnswer(userAnswer);

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        {/* Question Header */}
        <div className="mb-6">
          <div className="flex items-center mb-2">
            <Function className="h-5 w-5 mr-2" />
            <h3 className="text-lg font-semibold">Mathematical Expression</h3>
            {units && (
              <Badge variant="secondary" className="ml-2">
                Units: {units}
              </Badge>
            )}
          </div>
          <div className="mb-4">
            {renderMath(question)}
          </div>
          <p className="text-gray-600 text-sm">{instructions}</p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="answer">Answer</TabsTrigger>
            {showSteps && <TabsTrigger value="steps">Show Work</TabsTrigger>}
          </TabsList>

          <TabsContent value="answer" className="space-y-4">
            {/* Math Symbols Palette */}
            <div className="border rounded-lg p-3">
              <h4 className="text-sm font-medium mb-2">Math Symbols</h4>
              <div className="grid grid-cols-6 md:grid-cols-9 gap-2">
                {mathSymbols.map((item, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0 text-sm"
                    onClick={() => insertSymbol(item.latex)}
                    disabled={disabled}
                    title={item.description}
                  >
                    {item.symbol}
                  </Button>
                ))}
              </div>
            </div>

            {/* Answer Input */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Your Answer:</label>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setPreviewMode(prev => prev === 'input' ? 'rendered' : 'input')}
                  >
                    {previewMode === 'input' ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                    {previewMode === 'input' ? 'Preview' : 'Edit'}
                  </Button>
                </div>
              </div>
              
              {previewMode === 'input' ? (
                <div className="relative">
                  <Input
                    ref={inputRef}
                    value={userAnswer}
                    onChange={(e) => setUserAnswer(e.target.value)}
                    disabled={disabled}
                    placeholder="Enter your mathematical expression..."
                    className="font-mono"
                  />
                  {units && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500">
                      {units}
                    </div>
                  )}
                </div>
              ) : (
                <div className="min-h-[40px] border rounded-md p-2 bg-gray-50">
                  {userAnswer ? renderMath(userAnswer) : (
                    <span className="text-gray-400">Preview will appear here</span>
                  )}
                </div>
              )}
            </div>

            {/* Answer Validation */}
            {userAnswer && (
              <div className={cn(
                "p-2 rounded-md text-sm",
                isAnswerValid ? "bg-green-50 text-green-700" : "bg-yellow-50 text-yellow-700"
              )}>
                {isAnswerValid ? (
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Answer format looks correct
                  </div>
                ) : (
                  <div className="flex items-center">
                    <Calculator className="h-4 w-4 mr-2" />
                    Check your expression format
                  </div>
                )}
              </div>
            )}
          </TabsContent>

          {showSteps && (
            <TabsContent value="steps" className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Show Your Work:</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addStep}
                  disabled={disabled}
                >
                  Add Step
                </Button>
              </div>
              
              <div className="space-y-3">
                {userSteps.map((step, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-500 w-8">
                      {index + 1}.
                    </span>
                    <Input
                      value={step}
                      onChange={(e) => updateStep(index, e.target.value)}
                      disabled={disabled}
                      placeholder={`Step ${index + 1}...`}
                      className="flex-1 font-mono"
                    />
                    {userSteps.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeStep(index)}
                        disabled={disabled}
                        className="h-8 w-8 p-0"
                      >
                        <XCircle className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </TabsContent>
          )}
        </Tabs>

        {/* Controls */}
        <div className="flex items-center justify-between mt-6">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={disabled}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHint(!showHint)}
              disabled={disabled}
            >
              <Lightbulb className="h-4 w-4 mr-2" />
              Hint
            </Button>
          </div>
          
          <Button
            onClick={handleSubmit}
            disabled={disabled || !userAnswer.trim()}
            className="btn-gradient"
          >
            Submit Answer
          </Button>
        </div>

        {/* Hint */}
        {showHint && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              💡 Remember to check your units and consider if your answer makes sense in context
            </p>
          </div>
        )}

        {/* Feedback */}
        {showFeedback && (
          <div className={cn(
            "mt-4 p-4 rounded-lg border",
            isCorrect ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
          )}>
            <div className="flex items-center mb-2">
              {isCorrect ? (
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600 mr-2" />
              )}
              <span className={cn(
                "font-medium",
                isCorrect ? "text-green-800" : "text-red-800"
              )}>
                {isCorrect ? "Correct!" : "Incorrect"}
              </span>
            </div>
            <div className={cn(
              "text-sm space-y-2",
              isCorrect ? "text-green-700" : "text-red-700"
            )}>
              <p>{explanation}</p>
              {!isCorrect && (
                <div>
                  <p className="font-medium">Expected answer:</p>
                  {renderMath(expectedAnswer)}
                  {alternateAnswers.length > 0 && (
                    <div className="mt-2">
                      <p className="font-medium">Alternate forms:</p>
                      {alternateAnswers.map((alt, index) => (
                        <div key={index}>{renderMath(alt)}</div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
