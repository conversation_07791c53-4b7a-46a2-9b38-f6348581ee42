import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Play, 
  Square, 
  RotateCcw, 
  CheckCircle, 
  XCircle, 
  Clock,
  Memory,
  Terminal,
  Code,
  TestTube
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TestCase {
  input: string;
  expectedOutput: string;
  isHidden: boolean;
  points: number;
}

interface CodeExecutionQuestionProps {
  question: string;
  instructions: string;
  language: string;
  starterCode: string;
  testCases: TestCase[];
  expectedOutput: string;
  explanation: string;
  allowedLibraries: string[];
  timeLimit: number; // seconds
  memoryLimit: number; // MB
  onSubmit: (code: string, results: TestResult[]) => void;
  showFeedback?: boolean;
  isCorrect?: boolean;
  disabled?: boolean;
}

interface TestResult {
  testCase: TestCase;
  passed: boolean;
  actualOutput: string;
  executionTime: number;
  memoryUsed: number;
  error?: string;
}

interface ExecutionResult {
  output: string;
  error?: string;
  executionTime: number;
  memoryUsed: number;
}

export function CodeExecutionQuestion({
  question,
  instructions,
  language,
  starterCode,
  testCases,
  expectedOutput,
  explanation,
  allowedLibraries,
  timeLimit,
  memoryLimit,
  onSubmit,
  showFeedback = false,
  isCorrect = false,
  disabled = false
}: CodeExecutionQuestionProps) {
  const [code, setCode] = useState(starterCode);
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [currentOutput, setCurrentOutput] = useState('');
  const [executionError, setExecutionError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('code');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Mock code execution - in a real implementation, this would call a backend service
  const executeCode = useCallback(async (codeToRun: string, input?: string): Promise<ExecutionResult> => {
    // Simulate execution delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    // Mock execution logic
    try {
      // Simple JavaScript evaluation for demo purposes
      // In production, this would be handled by a secure code execution service
      const startTime = performance.now();
      
      // Create a safe execution environment
      const func = new Function('input', `
        const console = {
          log: (...args) => args.join(' ') + '\\n'
        };
        let output = '';
        const originalLog = console.log;
        console.log = (...args) => {
          output += args.join(' ') + '\\n';
        };
        
        try {
          ${codeToRun}
          return output.trim();
        } catch (error) {
          throw error;
        }
      `);
      
      const result = func(input || '');
      const endTime = performance.now();
      
      return {
        output: result || '',
        executionTime: endTime - startTime,
        memoryUsed: Math.random() * 10 // Mock memory usage
      };
    } catch (error) {
      return {
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: 0,
        memoryUsed: 0
      };
    }
  }, []);

  const runCode = useCallback(async () => {
    if (isRunning || disabled) return;
    
    setIsRunning(true);
    setExecutionError(null);
    setCurrentOutput('');
    
    try {
      const result = await executeCode(code);
      
      if (result.error) {
        setExecutionError(result.error);
      } else {
        setCurrentOutput(result.output);
      }
    } catch (error) {
      setExecutionError(error instanceof Error ? error.message : 'Execution failed');
    } finally {
      setIsRunning(false);
    }
  }, [code, executeCode, isRunning, disabled]);

  const runTests = useCallback(async () => {
    if (isRunning || disabled) return;
    
    setIsRunning(true);
    setTestResults([]);
    
    const results: TestResult[] = [];
    
    for (const testCase of testCases) {
      try {
        const result = await executeCode(code, testCase.input);
        
        const passed = result.output.trim() === testCase.expectedOutput.trim() && !result.error;
        
        results.push({
          testCase,
          passed,
          actualOutput: result.output,
          executionTime: result.executionTime,
          memoryUsed: result.memoryUsed,
          error: result.error
        });
      } catch (error) {
        results.push({
          testCase,
          passed: false,
          actualOutput: '',
          executionTime: 0,
          memoryUsed: 0,
          error: error instanceof Error ? error.message : 'Test execution failed'
        });
      }
    }
    
    setTestResults(results);
    setIsRunning(false);
    setActiveTab('tests');
  }, [code, testCases, executeCode, isRunning, disabled]);

  const handleSubmit = useCallback(() => {
    onSubmit(code, testResults);
  }, [code, testResults, onSubmit]);

  const handleReset = useCallback(() => {
    setCode(starterCode);
    setTestResults([]);
    setCurrentOutput('');
    setExecutionError(null);
  }, [starterCode]);

  const getLanguageIcon = (lang: string) => {
    switch (lang.toLowerCase()) {
      case 'javascript':
      case 'js':
        return '🟨';
      case 'python':
        return '🐍';
      case 'java':
        return '☕';
      case 'cpp':
      case 'c++':
        return '⚡';
      default:
        return '💻';
    }
  };

  const passedTests = testResults.filter(r => r.passed).length;
  const totalTests = testResults.length;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Code className="h-5 w-5" />
            <span>Code Challenge</span>
            <Badge variant="secondary" className="ml-2">
              {getLanguageIcon(language)} {language}
            </Badge>
          </div>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              {timeLimit}s
            </div>
            <div className="flex items-center">
              <Memory className="h-4 w-4 mr-1" />
              {memoryLimit}MB
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6">
        {/* Question */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">{question}</h3>
          <p className="text-gray-600 text-sm whitespace-pre-wrap">{instructions}</p>
        </div>

        {/* Allowed Libraries */}
        {allowedLibraries.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium mb-2">Allowed Libraries:</h4>
            <div className="flex flex-wrap gap-2">
              {allowedLibraries.map((lib, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {lib}
                </Badge>
              ))}
            </div>
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="code">Code Editor</TabsTrigger>
            <TabsTrigger value="output">Output</TabsTrigger>
            <TabsTrigger value="tests">
              Tests {testResults.length > 0 && `(${passedTests}/${totalTests})`}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="code" className="space-y-4">
            <div className="relative">
              <textarea
                ref={textareaRef}
                value={code}
                onChange={(e) => setCode(e.target.value)}
                disabled={disabled}
                className={cn(
                  "w-full h-64 p-4 font-mono text-sm border rounded-lg",
                  "focus:ring-2 focus:ring-primary focus:border-transparent",
                  "resize-none bg-gray-50",
                  disabled && "opacity-60 cursor-not-allowed"
                )}
                placeholder="Write your code here..."
                spellCheck={false}
              />
              <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                Lines: {code.split('\n').length}
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                onClick={runCode}
                disabled={isRunning || disabled}
                variant="outline"
                size="sm"
              >
                {isRunning ? (
                  <Square className="h-4 w-4 mr-2" />
                ) : (
                  <Play className="h-4 w-4 mr-2" />
                )}
                {isRunning ? 'Running...' : 'Run Code'}
              </Button>
              
              <Button
                onClick={runTests}
                disabled={isRunning || disabled}
                variant="outline"
                size="sm"
              >
                <TestTube className="h-4 w-4 mr-2" />
                Run Tests
              </Button>
              
              <Button
                onClick={handleReset}
                disabled={disabled}
                variant="outline"
                size="sm"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="output" className="space-y-4">
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm min-h-[200px]">
              <div className="flex items-center mb-2">
                <Terminal className="h-4 w-4 mr-2" />
                <span>Output</span>
              </div>
              <ScrollArea className="h-40">
                {isRunning ? (
                  <div className="animate-pulse">Executing code...</div>
                ) : executionError ? (
                  <div className="text-red-400">Error: {executionError}</div>
                ) : currentOutput ? (
                  <pre className="whitespace-pre-wrap">{currentOutput}</pre>
                ) : (
                  <div className="text-gray-500">No output yet. Run your code to see results.</div>
                )}
              </ScrollArea>
            </div>
          </TabsContent>

          <TabsContent value="tests" className="space-y-4">
            <div className="space-y-3">
              {testResults.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  No test results yet. Click "Run Tests" to execute all test cases.
                </div>
              ) : (
                testResults.map((result, index) => (
                  <Card key={index} className={cn(
                    "border-l-4",
                    result.passed ? "border-l-green-500" : "border-l-red-500"
                  )}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {result.passed ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-600" />
                          )}
                          <span className="font-medium">
                            Test Case {index + 1}
                            {result.testCase.isHidden && (
                              <Badge variant="secondary" className="ml-2 text-xs">Hidden</Badge>
                            )}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {result.executionTime.toFixed(2)}ms
                        </div>
                      </div>
                      
                      {!result.testCase.isHidden && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <div className="font-medium text-gray-700">Input:</div>
                            <code className="bg-gray-100 p-2 rounded text-xs block">
                              {result.testCase.input || '(no input)'}
                            </code>
                          </div>
                          <div>
                            <div className="font-medium text-gray-700">Expected:</div>
                            <code className="bg-gray-100 p-2 rounded text-xs block">
                              {result.testCase.expectedOutput}
                            </code>
                          </div>
                          <div>
                            <div className="font-medium text-gray-700">Your Output:</div>
                            <code className={cn(
                              "p-2 rounded text-xs block",
                              result.passed ? "bg-green-100" : "bg-red-100"
                            )}>
                              {result.actualOutput || '(no output)'}
                            </code>
                          </div>
                          {result.error && (
                            <div>
                              <div className="font-medium text-red-700">Error:</div>
                              <code className="bg-red-100 p-2 rounded text-xs block text-red-800">
                                {result.error}
                              </code>
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
            
            {testResults.length > 0 && (
              <div className="flex items-center justify-between pt-4 border-t">
                <div className="text-sm">
                  <span className="font-medium">Score: </span>
                  <span className={cn(
                    passedTests === totalTests ? "text-green-600" : "text-orange-600"
                  )}>
                    {passedTests}/{totalTests} tests passed
                  </span>
                </div>
                
                <Button
                  onClick={handleSubmit}
                  disabled={disabled || testResults.length === 0}
                  className="btn-gradient"
                >
                  Submit Solution
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Feedback */}
        {showFeedback && (
          <div className={cn(
            "mt-6 p-4 rounded-lg border",
            isCorrect ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
          )}>
            <div className="flex items-center mb-2">
              {isCorrect ? (
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600 mr-2" />
              )}
              <span className={cn(
                "font-medium",
                isCorrect ? "text-green-800" : "text-red-800"
              )}>
                {isCorrect ? "Solution Accepted!" : "Solution Needs Improvement"}
              </span>
            </div>
            <p className={cn(
              "text-sm whitespace-pre-wrap",
              isCorrect ? "text-green-700" : "text-red-700"
            )}>
              {explanation}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
