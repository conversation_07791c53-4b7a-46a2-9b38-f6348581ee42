import React, { useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  Eye, 
  EyeOff, 
  BarChart3,
  PieChart,
  LineChart,
  Download,
  Info
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

interface AccessibleAnalyticsCardProps {
  title: string;
  description?: string;
  value: string | number;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
  className?: string;
  children?: React.ReactNode;
  ariaLabel?: string;
  onExport?: () => void;
  isLoading?: boolean;
}

export function AccessibleAnalyticsCard({
  title,
  description,
  value,
  trend,
  trendValue,
  className,
  children,
  ariaLabel,
  onExport,
  isLoading = false,
}: AccessibleAnalyticsCardProps) {
  const cardRef = useRef<HTMLDivElement>(null);

  // Announce changes to screen readers
  useEffect(() => {
    if (cardRef.current && !isLoading) {
      const announcement = `${title} updated: ${value}${trendValue ? `, trend: ${trendValue}` : ''}`;
      
      // Create a live region for announcements
      const liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only';
      liveRegion.textContent = announcement;
      
      document.body.appendChild(liveRegion);
      
      // Clean up after announcement
      setTimeout(() => {
        document.body.removeChild(liveRegion);
      }, 1000);
    }
  }, [title, value, trendValue, isLoading]);

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" aria-label="Trending up" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" aria-label="Trending down" />;
      case 'stable':
        return <Minus className="h-4 w-4 text-gray-600" aria-label="Stable trend" />;
      default:
        return null;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-green-600 bg-green-50';
      case 'down':
        return 'text-red-600 bg-red-50';
      case 'stable':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <Card 
      ref={cardRef}
      className={cn(
        'transition-all duration-200 hover:shadow-md focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2',
        className
      )}
      role="region"
      aria-label={ariaLabel || `${title} analytics card`}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="space-y-1">
          <CardTitle className="text-sm font-medium" id={`${title.replace(/\s+/g, '-').toLowerCase()}-title`}>
            {title}
          </CardTitle>
          {description && (
            <CardDescription className="text-xs">
              {description}
            </CardDescription>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {trend && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge 
                    variant="secondary" 
                    className={cn('text-xs', getTrendColor())}
                    aria-label={`Trend: ${trend} ${trendValue || ''}`}
                  >
                    {getTrendIcon()}
                    {trendValue && <span className="ml-1">{trendValue}</span>}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Performance trend: {trend} {trendValue}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {onExport && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onExport}
              aria-label={`Export ${title} data`}
              className="h-8 w-8 p-0"
            >
              <Download className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div 
            className="text-2xl font-bold"
            aria-describedby={`${title.replace(/\s+/g, '-').toLowerCase()}-title`}
            role="status"
            aria-live="polite"
          >
            {isLoading ? (
              <div className="animate-pulse bg-gray-200 h-8 w-24 rounded" aria-label="Loading..." />
            ) : (
              value
            )}
          </div>
          {children}
        </div>
      </CardContent>
    </Card>
  );
}

interface AccessibleProgressBarProps {
  label: string;
  value: number;
  max?: number;
  showPercentage?: boolean;
  color?: 'default' | 'success' | 'warning' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function AccessibleProgressBar({
  label,
  value,
  max = 100,
  showPercentage = true,
  color = 'default',
  size = 'md',
  className,
}: AccessibleProgressBarProps) {
  const percentage = Math.round((value / max) * 100);
  
  const getColorClass = () => {
    switch (color) {
      case 'success':
        return 'bg-green-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'danger':
        return 'bg-red-500';
      default:
        return 'bg-primary';
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'h-2';
      case 'lg':
        return 'h-4';
      default:
        return 'h-3';
    }
  };

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex justify-between items-center">
        <label className="text-sm font-medium" htmlFor={`progress-${label.replace(/\s+/g, '-')}`}>
          {label}
        </label>
        {showPercentage && (
          <span className="text-sm text-muted-foreground" aria-label={`${percentage} percent`}>
            {percentage}%
          </span>
        )}
      </div>
      <Progress
        id={`progress-${label.replace(/\s+/g, '-')}`}
        value={percentage}
        className={cn('w-full', getSizeClass())}
        aria-label={`${label}: ${percentage}% of ${max}`}
        aria-valuenow={percentage}
        aria-valuemin={0}
        aria-valuemax={100}
        role="progressbar"
      />
    </div>
  );
}

interface AccessibleChartContainerProps {
  title: string;
  description?: string;
  chartType: 'line' | 'bar' | 'pie';
  data: any[];
  children: React.ReactNode;
  onToggleView?: () => void;
  showDataTable?: boolean;
  className?: string;
}

export function AccessibleChartContainer({
  title,
  description,
  chartType,
  data,
  children,
  onToggleView,
  showDataTable = false,
  className,
}: AccessibleChartContainerProps) {
  const getChartIcon = () => {
    switch (chartType) {
      case 'line':
        return <LineChart className="h-4 w-4" />;
      case 'bar':
        return <BarChart3 className="h-4 w-4" />;
      case 'pie':
        return <PieChart className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="flex items-center gap-2">
              {getChartIcon()}
              {title}
            </CardTitle>
            {description && (
              <CardDescription>{description}</CardDescription>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onToggleView}
                    aria-label={showDataTable ? 'Show chart view' : 'Show data table view'}
                  >
                    {showDataTable ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{showDataTable ? 'Switch to chart view' : 'Switch to table view'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" aria-label="Chart information">
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Chart shows {data.length} data points</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div 
          role="img" 
          aria-label={`${chartType} chart showing ${title}${description ? `: ${description}` : ''}`}
          tabIndex={0}
          className="focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded"
        >
          {showDataTable ? (
            <AccessibleDataTable data={data} title={title} />
          ) : (
            children
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface AccessibleDataTableProps {
  data: any[];
  title: string;
}

function AccessibleDataTable({ data, title }: AccessibleDataTableProps) {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No data available for {title}
      </div>
    );
  }

  const columns = Object.keys(data[0]);

  return (
    <div className="overflow-x-auto">
      <table 
        className="w-full border-collapse border border-gray-300"
        role="table"
        aria-label={`Data table for ${title}`}
      >
        <thead>
          <tr className="bg-gray-50">
            {columns.map((column) => (
              <th
                key={column}
                className="border border-gray-300 px-4 py-2 text-left font-medium"
                scope="col"
              >
                {column.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
              {columns.map((column) => (
                <td
                  key={column}
                  className="border border-gray-300 px-4 py-2"
                  role="cell"
                >
                  {row[column]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
