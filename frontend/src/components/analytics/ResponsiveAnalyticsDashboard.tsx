import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bar<PERSON><PERSON>3, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>hart, 
  TrendingUp, 
  Users, 
  BookOpen, 
  Target,
  Menu,
  X,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  useScreenSize, 
  useResponsiveGrid, 
  useResponsiveSpacing,
  useAnalyticsLayout,
  useResponsiveTextSize,
  useResponsiveChart
} from '@/lib/responsive';
import { AccessibleAnalyticsCard, AccessibleProgressBar, AccessibleChartContainer } from './AccessibleAnalytics';
import { TimeFilterSelector } from './TimeFilterSelector';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { usePerformanceMonitor } from '@/lib/memoization';
import { TimeFilter, DateRange } from '@/lib/api-types';

interface ResponsiveAnalyticsDashboardProps {
  className?: string;
}

export function ResponsiveAnalyticsDashboard({ className }: ResponsiveAnalyticsDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showDataTable, setShowDataTable] = useState(false);

  // Responsive hooks
  const { breakpoint, width } = useScreenSize();
  const dashboardGrid = useResponsiveGrid('dashboard');
  const chartsGrid = useResponsiveGrid('charts');
  const containerSpacing = useResponsiveSpacing('container');
  const sectionSpacing = useResponsiveSpacing('section');
  const { showSidebar, contentPadding } = useAnalyticsLayout();
  const titleSize = useResponsiveTextSize('title');
  const { height: chartHeight } = useResponsiveChart();

  // Performance monitoring
  const { measureOperation } = usePerformanceMonitor('ResponsiveAnalyticsDashboard');

  // Analytics data
  const {
    dashboard,
    performanceTrends,
    insights,
    studyPatterns,
    recommendations,
    heatmapData,
    timeFilter,
    dateRange,
    isLoading,
    hasError,
    updateTimeFilter,
    refreshData,
  } = useAnalyticsData('month');

  // Handle time filter changes
  const handleTimeFilterChange = useCallback(async (filter: TimeFilter, customDateRange?: DateRange) => {
    await measureOperation('timeFilterChange', () => 
      updateTimeFilter(filter, customDateRange)
    );
  }, [updateTimeFilter, measureOperation]);

  // Handle data refresh
  const handleRefresh = useCallback(async () => {
    await measureOperation('dataRefresh', () => refreshData());
  }, [refreshData, measureOperation]);

  // Export functionality
  const handleExport = useCallback((dataType: string) => {
    measureOperation('dataExport', () => {
      // Implementation for data export
      console.log(`Exporting ${dataType} data`);
    });
  }, [measureOperation]);

  // Mobile sidebar toggle
  const toggleSidebar = useCallback(() => {
    setSidebarOpen(prev => !prev);
  }, []);

  // Responsive navigation
  const NavigationTabs = () => (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className={cn(
        "grid w-full",
        breakpoint === 'xs' || breakpoint === 'sm' ? "grid-cols-2" : "grid-cols-4"
      )}>
        <TabsTrigger value="overview" className="text-xs sm:text-sm">
          <BarChart3 className="h-4 w-4 mr-1" />
          {width > 640 && "Overview"}
        </TabsTrigger>
        <TabsTrigger value="trends" className="text-xs sm:text-sm">
          <LineChart className="h-4 w-4 mr-1" />
          {width > 640 && "Trends"}
        </TabsTrigger>
        {width > 768 && (
          <>
            <TabsTrigger value="insights" className="text-xs sm:text-sm">
              <Target className="h-4 w-4 mr-1" />
              Insights
            </TabsTrigger>
            <TabsTrigger value="recommendations" className="text-xs sm:text-sm">
              <BookOpen className="h-4 w-4 mr-1" />
              Recommendations
            </TabsTrigger>
          </>
        )}
      </TabsList>

      <div className="mt-6">
        <TabsContent value="overview" className="space-y-6">
          <OverviewTab />
        </TabsContent>
        <TabsContent value="trends" className="space-y-6">
          <TrendsTab />
        </TabsContent>
        <TabsContent value="insights" className="space-y-6">
          <InsightsTab />
        </TabsContent>
        <TabsContent value="recommendations" className="space-y-6">
          <RecommendationsTab />
        </TabsContent>
      </div>
    </Tabs>
  );

  // Overview tab content
  const OverviewTab = () => (
    <div className={cn("grid gap-4", dashboardGrid)}>
      <AccessibleAnalyticsCard
        title="Total Tests"
        value={dashboard?.total_tests_taken || 0}
        trend="up"
        trendValue="+12%"
        ariaLabel="Total tests taken with 12% increase"
        onExport={() => handleExport('total-tests')}
        isLoading={isLoading}
      />
      <AccessibleAnalyticsCard
        title="Average Score"
        value={dashboard?.average_score ? `${dashboard.average_score.toFixed(1)}%` : '0%'}
        trend="up"
        trendValue="+5.2%"
        ariaLabel="Average test score with 5.2% improvement"
        onExport={() => handleExport('average-score')}
        isLoading={isLoading}
      />
      <AccessibleAnalyticsCard
        title="Overall Proficiency"
        value={dashboard?.overall_proficiency ? `${dashboard.overall_proficiency.toFixed(1)}%` : '0%'}
        trend="stable"
        ariaLabel="Overall proficiency level"
        onExport={() => handleExport('proficiency')}
        isLoading={isLoading}
      >
        <AccessibleProgressBar
          label="Progress to next level"
          value={dashboard?.overall_proficiency || 0}
          color={dashboard?.overall_proficiency && dashboard.overall_proficiency > 80 ? 'success' : 'default'}
          size="sm"
        />
      </AccessibleAnalyticsCard>
      {width > 1024 && (
        <AccessibleAnalyticsCard
          title="Study Streak"
          value={studyPatterns?.current_streak ? `${studyPatterns.current_streak} days` : '0 days'}
          trend={studyPatterns?.current_streak && studyPatterns.current_streak > 0 ? 'up' : 'stable'}
          ariaLabel="Current study streak"
          onExport={() => handleExport('streak')}
          isLoading={isLoading}
        />
      )}
    </div>
  );

  // Trends tab content
  const TrendsTab = () => (
    <div className={cn("grid gap-6", chartsGrid)}>
      <AccessibleChartContainer
        title="Performance Trends"
        description="Your score progression over time"
        chartType="line"
        data={performanceTrends || []}
        onToggleView={() => setShowDataTable(!showDataTable)}
        showDataTable={showDataTable}
        className="col-span-full"
      >
        <div 
          className="w-full bg-gray-100 rounded-lg flex items-center justify-center"
          style={{ height: chartHeight }}
        >
          {isLoading ? (
            <div className="animate-pulse">Loading chart...</div>
          ) : (
            <div className="text-center text-gray-500">
              Performance Trends Chart
              <br />
              <small>Chart component would be rendered here</small>
            </div>
          )}
        </div>
      </AccessibleChartContainer>

      <AccessibleChartContainer
        title="Subject Performance"
        description="Performance breakdown by subject"
        chartType="bar"
        data={dashboard?.topic_performance || []}
        onToggleView={() => setShowDataTable(!showDataTable)}
        showDataTable={showDataTable}
      >
        <div 
          className="w-full bg-gray-100 rounded-lg flex items-center justify-center"
          style={{ height: chartHeight * 0.8 }}
        >
          {isLoading ? (
            <div className="animate-pulse">Loading chart...</div>
          ) : (
            <div className="text-center text-gray-500">
              Subject Performance Chart
              <br />
              <small>Chart component would be rendered here</small>
            </div>
          )}
        </div>
      </AccessibleChartContainer>
    </div>
  );

  // Insights tab content
  const InsightsTab = () => (
    <div className="space-y-4">
      {insights && insights.length > 0 ? (
        insights.map((insight, index) => (
          <Card key={index} className="p-4">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant={insight.type === 'concern' ? 'destructive' : 'default'}>
                    {insight.type}
                  </Badge>
                  <h3 className="font-medium">{insight.title}</h3>
                </div>
                <p className="text-sm text-muted-foreground">{insight.description}</p>
                {insight.action && (
                  <Button size="sm" variant="outline">
                    {insight.action}
                  </Button>
                )}
              </div>
            </div>
          </Card>
        ))
      ) : (
        <Card className="p-8 text-center">
          <p className="text-muted-foreground">No insights available yet. Complete more tests to get personalized insights.</p>
        </Card>
      )}
    </div>
  );

  // Recommendations tab content
  const RecommendationsTab = () => (
    <div className="space-y-4">
      {recommendations && recommendations.length > 0 ? (
        recommendations.map((rec, index) => (
          <Card key={index} className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">{rec.title}</h3>
                <Badge variant="outline">Priority {rec.priority}</Badge>
              </div>
              <p className="text-sm text-muted-foreground">{rec.description}</p>
              <div className="flex gap-2 pt-2">
                <Button size="sm">Start Practice</Button>
                <Button size="sm" variant="outline">Learn More</Button>
              </div>
            </div>
          </Card>
        ))
      ) : (
        <Card className="p-8 text-center">
          <p className="text-muted-foreground">No recommendations available. Complete your profile setup to get personalized recommendations.</p>
        </Card>
      )}
    </div>
  );

  return (
    <div className={cn("min-h-screen bg-background", className)}>
      {/* Mobile Header */}
      {!showSidebar && (
        <div className="sticky top-0 z-40 bg-background border-b lg:hidden">
          <div className="flex items-center justify-between p-4">
            <h1 className={cn("font-bold", titleSize)}>Analytics</h1>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
                aria-label="Refresh data"
              >
                <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleSidebar}
                aria-label="Open menu"
              >
                <Menu className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && !showSidebar && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div className="fixed inset-0 bg-black/50" onClick={toggleSidebar} />
          <div className="fixed right-0 top-0 h-full w-64 bg-background border-l p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="font-semibold">Menu</h2>
              <Button variant="ghost" size="sm" onClick={toggleSidebar}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="space-y-4">
              <TimeFilterSelector
                value={timeFilter}
                dateRange={dateRange}
                onChange={handleTimeFilterChange}
                showPresets={true}
              />
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className={cn("flex-1", contentPadding)}>
        {/* Desktop Header */}
        <div className="hidden lg:flex items-center justify-between mb-8">
          <h1 className={cn("font-bold", titleSize)}>Analytics Dashboard</h1>
          <div className="flex items-center gap-4">
            <TimeFilterSelector
              value={timeFilter}
              dateRange={dateRange}
              onChange={handleTimeFilterChange}
              showPresets={true}
            />
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isLoading}
              aria-label="Refresh all data"
            >
              <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Error State */}
        {hasError && (
          <Card className="mb-6 p-4 border-red-200 bg-red-50">
            <p className="text-red-800">Failed to load analytics data. Please try refreshing.</p>
          </Card>
        )}

        {/* Navigation and Content */}
        <NavigationTabs />
      </div>
    </div>
  );
}
