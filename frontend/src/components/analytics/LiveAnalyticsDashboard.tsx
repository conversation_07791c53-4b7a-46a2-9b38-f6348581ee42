import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Target, 
  Zap, 
  Brain,
  BarChart3,
  Pie<PERSON>hart,
  Activity,
  RefreshCw,
  Calendar,
  Award
} from 'lucide-react';
import { useGamificationStore } from '@/stores/gamificationStore';

interface AnalyticsMetric {
  id: string;
  label: string;
  value: number;
  previousValue: number;
  unit: string;
  icon: React.ComponentType<any>;
  color: string;
  trend: 'up' | 'down' | 'stable';
}

interface PerformanceData {
  subject: string;
  accuracy: number;
  improvement: number;
  timeSpent: number;
  questionsAnswered: number;
}

interface StudyPattern {
  hour: number;
  activity: number;
  focus: number;
}

export const LiveAnalyticsDashboard: React.FC = () => {
  const { userStats } = useGamificationStore();
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Generate dynamic metrics based on user stats
  const [metrics, setMetrics] = useState<AnalyticsMetric[]>([
    {
      id: 'study_time',
      label: 'Study Time Today',
      value: 125, // minutes
      previousValue: 98,
      unit: 'min',
      icon: Clock,
      color: 'text-blue-500',
      trend: 'up'
    },
    {
      id: 'accuracy',
      label: 'Overall Accuracy',
      value: userStats.averageScore || 0,
      previousValue: Math.max(0, (userStats.averageScore || 0) - 5),
      unit: '%',
      icon: Target,
      color: 'text-green-500',
      trend: userStats.averageScore > 75 ? 'up' : 'stable'
    },
    {
      id: 'learning_velocity',
      label: 'Learning Velocity',
      value: Math.min(100, userStats.totalXP / 10),
      previousValue: Math.min(100, Math.max(0, userStats.totalXP / 10 - 8)),
      unit: '%',
      icon: Zap,
      color: 'text-purple-500',
      trend: 'up'
    },
    {
      id: 'focus_score',
      label: 'Focus Score',
      value: 87,
      previousValue: 82,
      unit: '/100',
      icon: Brain,
      color: 'text-orange-500',
      trend: 'up'
    }
  ]);

  const [performanceData] = useState<PerformanceData[]>([
    { subject: 'Mathematics', accuracy: 85, improvement: 12, timeSpent: 45, questionsAnswered: 23 },
    { subject: 'Drag & Drop', accuracy: 92, improvement: 8, timeSpent: 32, questionsAnswered: 15 },
    { subject: 'Coding', accuracy: 78, improvement: 15, timeSpent: 67, questionsAnswered: 12 },
    { subject: 'Logic', accuracy: 88, improvement: 5, timeSpent: 28, questionsAnswered: 18 },
  ]);

  const [studyPatterns] = useState<StudyPattern[]>([
    { hour: 9, activity: 20, focus: 65 },
    { hour: 10, activity: 45, focus: 78 },
    { hour: 11, activity: 78, focus: 85 },
    { hour: 12, activity: 65, focus: 72 },
    { hour: 13, activity: 30, focus: 45 },
    { hour: 14, activity: 85, focus: 92 },
    { hour: 15, activity: 95, focus: 88 },
    { hour: 16, activity: 70, focus: 75 },
  ]);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(prev => prev.map(metric => {
        // Small random fluctuations
        const change = (Math.random() - 0.5) * 2;
        const newValue = Math.max(0, Math.min(100, metric.value + change));
        
        return {
          ...metric,
          previousValue: metric.value,
          value: newValue,
          trend: newValue > metric.value ? 'up' : newValue < metric.value ? 'down' : 'stable'
        };
      }));
      setLastUpdate(new Date());
    }, 10000); // Update every 10 seconds

    return () => clearInterval(interval);
  }, []);

  const refreshData = () => {
    setIsRefreshing(true);
    setTimeout(() => {
      setLastUpdate(new Date());
      setIsRefreshing(false);
    }, 1000);
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getChangePercentage = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return Math.round(((current - previous) / previous) * 100);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Live Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Real-time insights into your learning progress
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse" />
            Live
          </Badge>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={refreshData}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric) => (
          <Card key={metric.id} className="relative overflow-hidden">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">{metric.label}</p>
                  <p className="text-2xl font-bold">
                    {Math.round(metric.value)}{metric.unit}
                  </p>
                </div>
                <metric.icon className={`h-8 w-8 ${metric.color}`} />
              </div>
              
              <div className="flex items-center gap-2 mt-2">
                {getTrendIcon(metric.trend)}
                <span className={`text-xs ${
                  metric.trend === 'up' ? 'text-green-600' : 
                  metric.trend === 'down' ? 'text-red-600' : 
                  'text-muted-foreground'
                }`}>
                  {getChangePercentage(metric.value, metric.previousValue)}% vs yesterday
                </span>
              </div>
              
              {/* Animated progress bar */}
              <div className="mt-3">
                <Progress 
                  value={metric.value} 
                  className="h-2"
                />
              </div>
            </CardContent>
            
            {/* Animated background effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 animate-pulse opacity-0 hover:opacity-100 transition-opacity" />
          </Card>
        ))}
      </div>

      {/* Performance by Subject */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance by Subject
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {performanceData.map((subject) => (
              <div key={subject.subject} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{subject.subject}</span>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{subject.questionsAnswered} questions</span>
                    <span>{subject.timeSpent}min</span>
                    <Badge className={`${
                      subject.improvement > 10 ? 'bg-green-100 text-green-800' :
                      subject.improvement > 5 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      +{subject.improvement}%
                    </Badge>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Progress value={subject.accuracy} className="flex-1 h-2" />
                  <span className="text-sm font-medium w-12">{subject.accuracy}%</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Study Patterns */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Today's Study Pattern
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {studyPatterns.map((pattern) => (
                <div key={pattern.hour} className="flex items-center gap-4">
                  <span className="text-sm font-medium w-12">
                    {pattern.hour}:00
                  </span>
                  
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span>Activity</span>
                      <span>{pattern.activity}%</span>
                    </div>
                    <Progress value={pattern.activity} className="h-1" />
                  </div>
                  
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span>Focus</span>
                      <span>{pattern.focus}%</span>
                    </div>
                    <Progress value={pattern.focus} className="h-1" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Learning Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-800">Peak Performance</span>
                </div>
                <p className="text-sm text-green-700">
                  Your best learning hours are 2-4 PM with 92% focus score.
                </p>
              </div>
              
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <Target className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-800">Strength Area</span>
                </div>
                <p className="text-sm text-blue-700">
                  Drag & Drop exercises: 92% accuracy with consistent improvement.
                </p>
              </div>
              
              <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <Brain className="h-4 w-4 text-orange-600" />
                  <span className="font-medium text-orange-800">Growth Opportunity</span>
                </div>
                <p className="text-sm text-orange-700">
                  Focus on coding challenges for 15% improvement potential.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Footer */}
      <div className="text-center text-sm text-muted-foreground">
        Last updated: {lastUpdate.toLocaleTimeString()} • 
        Data refreshes every 10 seconds automatically
      </div>
    </div>
  );
};
