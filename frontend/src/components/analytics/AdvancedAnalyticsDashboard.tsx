import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  Brain, 
  Target, 
  Clock,
  Zap,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  <PERSON>Chart,
  <PERSON><PERSON>hart,
  Activity,
  Lightbulb,
  Calendar,
  Award,
  BookOpen,
  Users,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AdvancedAnalyticsData {
  learningMetrics: {
    totalStudyTime: number;
    overallAccuracy: number;
    learningVelocity: number;
    retentionRate: number;
    masteryProgression: number;
    consistencyScore: number;
    engagementLevel: number;
    subjectBreakdown: Record<string, {
      accuracy: number;
      masteryLevel: number;
      progressRate: number;
      strengthAreas: string[];
      weaknessAreas: string[];
    }>;
  };
  performanceAnalysis: {
    performanceTrends: Array<{
      metric: string;
      trend: 'improving' | 'declining' | 'stable';
      confidence: number;
      dataPoints: Array<{ timestamp: string; value: number }>;
    }>;
    learningCurve: {
      initialPerformance: number;
      currentPerformance: number;
      peakPerformance: number;
      learningRate: number;
    };
    errorAnalysis: {
      commonMistakes: Array<{
        pattern: string;
        frequency: number;
        severity: string;
        trend: string;
      }>;
      conceptualGaps: Array<{
        concept: string;
        severity: number;
        priority: number;
      }>;
    };
  };
  predictiveInsights: {
    examReadiness: {
      overallReadiness: number;
      subjectReadiness: Record<string, number>;
      weakAreas: string[];
      timeRequired: number;
    };
    riskAssessment: {
      overallRisk: number;
      riskFactors: Array<{
        factor: string;
        probability: number;
        impact: number;
        severity: string;
      }>;
    };
    opportunities: Array<{
      title: string;
      potential: number;
      effort: number;
      timeline: string;
    }>;
  };
  recommendations: Array<{
    type: string;
    title: string;
    description: string;
    priority: number;
    impact: number;
    timeline: string;
  }>;
}

interface AdvancedAnalyticsDashboardProps {
  data: AdvancedAnalyticsData;
  onRefresh: () => void;
  onExportReport: () => void;
}

export function AdvancedAnalyticsDashboard({
  data,
  onRefresh,
  onExportReport
}: AdvancedAnalyticsDashboardProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState('week');
  const [activeTab, setActiveTab] = useState('overview');

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-orange-600 bg-orange-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Learning Analytics</h1>
          <p className="text-gray-600">Comprehensive insights into your learning journey</p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="border rounded px-3 py-2 text-sm"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </select>
          <Button onClick={onRefresh} variant="outline" size="sm">
            <Activity className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={onExportReport} size="sm">
            <BarChart3 className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Study Time</p>
                <p className="text-2xl font-bold">{formatTime(data.learningMetrics.totalStudyTime)}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
            <div className="mt-2">
              <Badge variant="outline" className="text-xs">
                +12% vs last week
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Overall Accuracy</p>
                <p className="text-2xl font-bold">{(data.learningMetrics.overallAccuracy * 100).toFixed(1)}%</p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
            <div className="mt-2">
              <Progress value={data.learningMetrics.overallAccuracy * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Learning Velocity</p>
                <p className="text-2xl font-bold">{(data.learningMetrics.learningVelocity * 100).toFixed(1)}%</p>
              </div>
              <Zap className="h-8 w-8 text-purple-500" />
            </div>
            <div className="mt-2">
              <Badge variant="secondary" className="text-xs">
                Accelerating
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Mastery Level</p>
                <p className="text-2xl font-bold">{(data.learningMetrics.masteryProgression * 100).toFixed(0)}%</p>
              </div>
              <Award className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="mt-2">
              <Progress value={data.learningMetrics.masteryProgression * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="recommendations">Actions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Subject Performance Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="h-5 w-5 mr-2" />
                Subject Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(data.learningMetrics.subjectBreakdown).map(([subject, metrics]) => (
                  <div key={subject} className="p-4 border rounded-lg">
                    <h3 className="font-semibold mb-3 capitalize">{subject}</h3>
                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Accuracy</span>
                          <span>{(metrics.accuracy * 100).toFixed(1)}%</span>
                        </div>
                        <Progress value={metrics.accuracy * 100} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Mastery</span>
                          <span>{(metrics.masteryLevel * 100).toFixed(0)}%</span>
                        </div>
                        <Progress value={metrics.masteryLevel * 100} className="h-2" />
                      </div>
                      <div className="text-xs">
                        <div className="text-green-600 mb-1">
                          <strong>Strengths:</strong> {metrics.strengthAreas.join(', ')}
                        </div>
                        <div className="text-orange-600">
                          <strong>Focus Areas:</strong> {metrics.weaknessAreas.join(', ')}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Learning Consistency */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Learning Consistency
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm">Consistency Score</span>
                      <span className="text-sm font-medium">
                        {(data.learningMetrics.consistencyScore * 100).toFixed(0)}%
                      </span>
                    </div>
                    <Progress value={data.learningMetrics.consistencyScore * 100} className="h-3" />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm">Engagement Level</span>
                      <span className="text-sm font-medium">
                        {(data.learningMetrics.engagementLevel * 100).toFixed(0)}%
                      </span>
                    </div>
                    <Progress value={data.learningMetrics.engagementLevel * 100} className="h-3" />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm">Retention Rate</span>
                      <span className="text-sm font-medium">
                        {(data.learningMetrics.retentionRate * 100).toFixed(0)}%
                      </span>
                    </div>
                    <Progress value={data.learningMetrics.retentionRate * 100} className="h-3" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <LineChart className="h-5 w-5 mr-2" />
                  Learning Curve
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Initial Performance</span>
                    <Badge variant="outline">
                      {(data.performanceAnalysis.learningCurve.initialPerformance * 100).toFixed(0)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Current Performance</span>
                    <Badge variant="secondary">
                      {(data.performanceAnalysis.learningCurve.currentPerformance * 100).toFixed(0)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Peak Performance</span>
                    <Badge>
                      {(data.performanceAnalysis.learningCurve.peakPerformance * 100).toFixed(0)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Learning Rate</span>
                    <Badge variant="outline" className="text-green-600">
                      +{(data.performanceAnalysis.learningCurve.learningRate * 100).toFixed(1)}%/week
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          {/* Performance Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Performance Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {data.performanceAnalysis.performanceTrends.map((trend, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium capitalize">{trend.metric}</span>
                      {getTrendIcon(trend.trend)}
                    </div>
                    <div className="text-xs text-gray-600 mb-2">
                      Confidence: {(trend.confidence * 100).toFixed(0)}%
                    </div>
                    <Badge variant="outline" className={cn(
                      "text-xs",
                      trend.trend === 'improving' && "text-green-600",
                      trend.trend === 'declining' && "text-red-600"
                    )}>
                      {trend.trend}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Error Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Error Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-3">Common Mistakes</h4>
                  <div className="space-y-2">
                    {data.performanceAnalysis.errorAnalysis.commonMistakes.map((mistake, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div>
                          <span className="text-sm font-medium">{mistake.pattern}</span>
                          <div className="text-xs text-gray-600">
                            Frequency: {mistake.frequency} times
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={getSeverityColor(mistake.severity)}>
                            {mistake.severity}
                          </Badge>
                          {getTrendIcon(mistake.trend)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3">Conceptual Gaps</h4>
                  <div className="space-y-2">
                    {data.performanceAnalysis.errorAnalysis.conceptualGaps.map((gap, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <span className="text-sm font-medium">{gap.concept}</span>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">Priority {gap.priority}</Badge>
                          <div className="w-16">
                            <Progress value={gap.severity * 100} className="h-2" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="predictions" className="space-y-6">
          {/* Exam Readiness */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2" />
                Exam Readiness Prediction
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="text-center mb-4">
                    <div className="text-3xl font-bold text-green-600">
                      {(data.predictiveInsights.examReadiness.overallReadiness * 100).toFixed(0)}%
                    </div>
                    <div className="text-sm text-gray-600">Overall Readiness</div>
                  </div>
                  <Progress value={data.predictiveInsights.examReadiness.overallReadiness * 100} className="h-3" />
                  <div className="mt-4 text-sm">
                    <div className="flex justify-between">
                      <span>Estimated study time needed:</span>
                      <span className="font-medium">{data.predictiveInsights.examReadiness.timeRequired} hours</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3">Subject Readiness</h4>
                  <div className="space-y-3">
                    {Object.entries(data.predictiveInsights.examReadiness.subjectReadiness).map(([subject, readiness]) => (
                      <div key={subject}>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="capitalize">{subject}</span>
                          <span>{(readiness * 100).toFixed(0)}%</span>
                        </div>
                        <Progress value={readiness * 100} className="h-2" />
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {data.predictiveInsights.examReadiness.weakAreas.length > 0 && (
                <div className="mt-6 p-4 bg-orange-50 rounded-lg">
                  <h4 className="font-medium text-orange-800 mb-2">Areas Needing Focus</h4>
                  <div className="flex flex-wrap gap-2">
                    {data.predictiveInsights.examReadiness.weakAreas.map((area, index) => (
                      <Badge key={index} variant="outline" className="text-orange-700">
                        {area}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Risk Assessment */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Risk Assessment
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Overall Risk Level</span>
                  <Badge className={cn(
                    data.predictiveInsights.riskAssessment.overallRisk > 0.7 ? "bg-red-100 text-red-800" :
                    data.predictiveInsights.riskAssessment.overallRisk > 0.4 ? "bg-orange-100 text-orange-800" :
                    "bg-green-100 text-green-800"
                  )}>
                    {data.predictiveInsights.riskAssessment.overallRisk > 0.7 ? "High" :
                     data.predictiveInsights.riskAssessment.overallRisk > 0.4 ? "Medium" : "Low"}
                  </Badge>
                </div>
                <Progress value={data.predictiveInsights.riskAssessment.overallRisk * 100} className="h-3" />
              </div>

              <div className="space-y-3">
                {data.predictiveInsights.riskAssessment.riskFactors.map((risk, index) => (
                  <div key={index} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">{risk.factor}</span>
                      <Badge className={getSeverityColor(risk.severity)}>
                        {risk.severity}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
                      <div>Probability: {(risk.probability * 100).toFixed(0)}%</div>
                      <div>Impact: {(risk.impact * 100).toFixed(0)}%</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          {/* Learning Opportunities */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lightbulb className="h-5 w-5 mr-2" />
                Learning Opportunities
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {data.predictiveInsights.opportunities.map((opportunity, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">{opportunity.title}</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Potential Impact:</span>
                        <Progress value={opportunity.potential * 100} className="w-16 h-2" />
                      </div>
                      <div className="flex justify-between">
                        <span>Effort Required:</span>
                        <Progress value={opportunity.effort * 100} className="w-16 h-2" />
                      </div>
                      <div className="flex justify-between">
                        <span>Timeline:</span>
                        <Badge variant="outline" className="text-xs">{opportunity.timeline}</Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          {/* Action Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Sparkles className="h-5 w-5 mr-2" />
                Personalized Recommendations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.recommendations
                  .sort((a, b) => b.priority - a.priority)
                  .map((rec, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h4 className="font-medium">{rec.title}</h4>
                          <Badge variant="outline" className="text-xs mt-1 capitalize">
                            {rec.type}
                          </Badge>
                        </div>
                        <div className="text-right">
                          <Badge className={cn(
                            rec.priority >= 8 ? "bg-red-100 text-red-800" :
                            rec.priority >= 6 ? "bg-orange-100 text-orange-800" :
                            "bg-green-100 text-green-800"
                          )}>
                            Priority {rec.priority}
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{rec.description}</p>
                      <div className="flex items-center justify-between text-xs">
                        <span>Impact: {(rec.impact * 100).toFixed(0)}%</span>
                        <span>Timeline: {rec.timeline}</span>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
