import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import '@testing-library/jest-dom';
import { ResponsiveAnalyticsDashboard } from '../ResponsiveAnalyticsDashboard';

// Mock the hooks
vi.mock('@/hooks/useAnalyticsData', () => ({
  useAnalyticsData: vi.fn(() => ({
    dashboard: {
      total_tests_taken: 25,
      average_score: 78.5,
      overall_proficiency: 82.3,
    },
    performanceTrends: [
      { period: 'Week 1', avg_score: 75, test_count: 3 },
      { period: 'Week 2', avg_score: 80, test_count: 4 },
    ],
    insights: [
      {
        type: 'improvement',
        title: 'Great progress!',
        description: 'Your scores have improved by 15% this week',
        action: 'Continue practicing',
      },
    ],
    recommendations: [
      {
        title: 'Focus on Algebra',
        description: 'Your algebra scores could use improvement',
        priority: 4,
      },
    ],
    studyPatterns: {
      current_streak: 7,
      consistency_score: 85,
    },
    timeFilter: 'month',
    dateRange: undefined,
    isLoading: false,
    hasError: false,
    updateTimeFilter: vi.fn(),
    refreshData: vi.fn(),
  })),
}));

vi.mock('@/lib/responsive', () => ({
  useScreenSize: vi.fn(() => ({
    width: 1024,
    height: 768,
    breakpoint: 'lg',
  })),
  useResponsiveGrid: vi.fn((type) => {
    const configs = {
      dashboard: 'grid-cols-4',
      charts: 'grid-cols-2',
      insights: 'grid-cols-2',
    };
    return configs[type] || 'grid-cols-1';
  }),
  useResponsiveSpacing: vi.fn(() => 'p-8'),
  useAnalyticsLayout: vi.fn(() => ({
    showSidebar: true,
    contentPadding: 'p-8',
  })),
  useResponsiveTextSize: vi.fn(() => 'text-3xl'),
  useResponsiveChart: vi.fn(() => ({
    height: 350,
    fontSize: 13,
    margin: { top: 35, right: 35, bottom: 70, left: 70 },
  })),
}));

vi.mock('@/lib/memoization', () => ({
  usePerformanceMonitor: vi.fn(() => ({
    measureOperation: vi.fn((name, fn) => fn()),
  })),
}));

// Mock the TimeFilterSelector component
vi.mock('../TimeFilterSelector', () => ({
  TimeFilterSelector: ({ value, onChange }: any) => (
    <div data-testid="time-filter-selector">
      <button onClick={() => onChange('week')}>Week</button>
      <button onClick={() => onChange('month')}>Month</button>
      <span>Current: {value}</span>
    </div>
  ),
}));

describe('ResponsiveAnalyticsDashboard', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Clean up after each test
    vi.restoreAllMocks();
  });

  describe('Desktop Layout', () => {
    it('renders the dashboard with all main components', () => {
      render(<ResponsiveAnalyticsDashboard />);

      // Check for main title
      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();

      // Check for overview cards
      expect(screen.getByText('Total Tests')).toBeInTheDocument();
      expect(screen.getByText('Average Score')).toBeInTheDocument();
      expect(screen.getByText('Overall Proficiency')).toBeInTheDocument();

      // Check for navigation tabs
      expect(screen.getByText('Overview')).toBeInTheDocument();
      expect(screen.getByText('Trends')).toBeInTheDocument();
      expect(screen.getByText('Insights')).toBeInTheDocument();
      expect(screen.getByText('Recommendations')).toBeInTheDocument();
    });

    it('displays correct analytics data', () => {
      render(<ResponsiveAnalyticsDashboard />);

      // Check if the data is displayed correctly
      expect(screen.getByText('25')).toBeInTheDocument(); // Total tests
      expect(screen.getByText('78.5%')).toBeInTheDocument(); // Average score
      expect(screen.getByText('82.3%')).toBeInTheDocument(); // Overall proficiency
    });

    it('handles tab navigation correctly', async () => {
      render(<ResponsiveAnalyticsDashboard />);

      // Click on Trends tab
      fireEvent.click(screen.getByText('Trends'));
      
      await waitFor(() => {
        expect(screen.getByText('Performance Trends')).toBeInTheDocument();
      });

      // Click on Insights tab
      fireEvent.click(screen.getByText('Insights'));
      
      await waitFor(() => {
        expect(screen.getByText('Great progress!')).toBeInTheDocument();
      });

      // Click on Recommendations tab
      fireEvent.click(screen.getByText('Recommendations'));
      
      await waitFor(() => {
        expect(screen.getByText('Focus on Algebra')).toBeInTheDocument();
      });
    });
  });

  describe('Mobile Layout', () => {
    beforeEach(() => {
      // Mock mobile screen size
      const { useScreenSize } = require('@/lib/responsive');
      useScreenSize.mockReturnValue({
        width: 375,
        height: 667,
        breakpoint: 'xs',
      });
    });

    it('renders mobile-optimized layout', () => {
      render(<ResponsiveAnalyticsDashboard />);

      // Should show mobile header instead of desktop header
      expect(screen.getByText('Analytics')).toBeInTheDocument();
      expect(screen.queryByText('Analytics Dashboard')).not.toBeInTheDocument();

      // Should show menu button
      expect(screen.getByLabelText('Open menu')).toBeInTheDocument();
    });

    it('opens and closes mobile sidebar', async () => {
      render(<ResponsiveAnalyticsDashboard />);

      // Open sidebar
      fireEvent.click(screen.getByLabelText('Open menu'));
      
      await waitFor(() => {
        expect(screen.getByText('Menu')).toBeInTheDocument();
      });

      // Close sidebar
      fireEvent.click(screen.getByLabelText('Close menu') || screen.getByText('×'));
      
      await waitFor(() => {
        expect(screen.queryByText('Menu')).not.toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<ResponsiveAnalyticsDashboard />);

      // Check for ARIA labels on interactive elements
      expect(screen.getByLabelText('Refresh all data')).toBeInTheDocument();
      
      // Check for proper heading structure
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(0);
    });

    it('supports keyboard navigation', () => {
      render(<ResponsiveAnalyticsDashboard />);

      // Test tab navigation
      const refreshButton = screen.getByLabelText('Refresh all data');
      refreshButton.focus();
      expect(refreshButton).toHaveFocus();

      // Test tab key navigation
      fireEvent.keyDown(refreshButton, { key: 'Tab' });
      // The next focusable element should receive focus
    });

    it('provides screen reader announcements for data updates', async () => {
      const { rerender } = render(<ResponsiveAnalyticsDashboard />);

      // Mock updated data
      const { useAnalyticsData } = require('@/hooks/useAnalyticsData');
      useAnalyticsData.mockReturnValue({
        dashboard: {
          total_tests_taken: 30, // Updated value
          average_score: 82.1,   // Updated value
          overall_proficiency: 85.0,
        },
        performanceTrends: [],
        insights: [],
        recommendations: [],
        studyPatterns: { current_streak: 8 },
        timeFilter: 'month',
        isLoading: false,
        hasError: false,
        updateTimeFilter: vi.fn(),
        refreshData: vi.fn(),
      });

      rerender(<ResponsiveAnalyticsDashboard />);

      // Check if updated values are displayed
      await waitFor(() => {
        expect(screen.getByText('30')).toBeInTheDocument();
        expect(screen.getByText('82.1%')).toBeInTheDocument();
      });
    });
  });

  describe('Performance and Loading States', () => {
    it('shows loading states correctly', () => {
      const { useAnalyticsData } = require('@/hooks/useAnalyticsData');
      useAnalyticsData.mockReturnValue({
        dashboard: null,
        performanceTrends: [],
        insights: [],
        recommendations: [],
        studyPatterns: null,
        timeFilter: 'month',
        isLoading: true,
        hasError: false,
        updateTimeFilter: vi.fn(),
        refreshData: vi.fn(),
      });

      render(<ResponsiveAnalyticsDashboard />);

      // Check for loading indicators
      expect(screen.getAllByLabelText('Loading...')).toHaveLength(4); // One for each card
    });

    it('handles error states gracefully', () => {
      const { useAnalyticsData } = require('@/hooks/useAnalyticsData');
      useAnalyticsData.mockReturnValue({
        dashboard: null,
        performanceTrends: [],
        insights: [],
        recommendations: [],
        studyPatterns: null,
        timeFilter: 'month',
        isLoading: false,
        hasError: true,
        updateTimeFilter: vi.fn(),
        refreshData: vi.fn(),
      });

      render(<ResponsiveAnalyticsDashboard />);

      // Check for error message
      expect(screen.getByText(/Failed to load analytics data/)).toBeInTheDocument();
    });

    it('calls refresh function when refresh button is clicked', async () => {
      const mockRefreshData = vi.fn();
      const { useAnalyticsData } = require('@/hooks/useAnalyticsData');
      useAnalyticsData.mockReturnValue({
        dashboard: { total_tests_taken: 25 },
        performanceTrends: [],
        insights: [],
        recommendations: [],
        studyPatterns: null,
        timeFilter: 'month',
        isLoading: false,
        hasError: false,
        updateTimeFilter: vi.fn(),
        refreshData: mockRefreshData,
      });

      render(<ResponsiveAnalyticsDashboard />);

      fireEvent.click(screen.getByLabelText('Refresh all data'));
      
      await waitFor(() => {
        expect(mockRefreshData).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Time Filter Integration', () => {
    it('updates time filter when changed', async () => {
      const mockUpdateTimeFilter = vi.fn();
      const { useAnalyticsData } = require('@/hooks/useAnalyticsData');
      useAnalyticsData.mockReturnValue({
        dashboard: { total_tests_taken: 25 },
        performanceTrends: [],
        insights: [],
        recommendations: [],
        studyPatterns: null,
        timeFilter: 'month',
        isLoading: false,
        hasError: false,
        updateTimeFilter: mockUpdateTimeFilter,
        refreshData: vi.fn(),
      });

      render(<ResponsiveAnalyticsDashboard />);

      // Find and click the week filter
      fireEvent.click(screen.getByText('Week'));
      
      await waitFor(() => {
        expect(mockUpdateTimeFilter).toHaveBeenCalledWith('week', undefined);
      });
    });
  });

  describe('Cross-browser Compatibility', () => {
    it('renders correctly without modern CSS features', () => {
      // Mock older browser environment
      const originalCSS = window.CSS;
      delete (window as any).CSS;

      render(<ResponsiveAnalyticsDashboard />);

      // Should still render basic content
      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();

      // Restore CSS
      (window as any).CSS = originalCSS;
    });

    it('handles touch events on mobile devices', () => {
      // Mock touch device
      Object.defineProperty(navigator, 'maxTouchPoints', {
        writable: true,
        value: 5,
      });

      render(<ResponsiveAnalyticsDashboard />);

      // Should render touch-friendly interface
      expect(screen.getByText('Analytics')).toBeInTheDocument();
    });
  });
});
