import React, { useState, useEffect } from 'react';
import { Calendar, ChevronDown, Clock, Filter } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { TimeFilter, DateRange } from '@/lib/api-types';
import { format, subDays, subWeeks, subMonths, subYears } from 'date-fns';

interface TimeFilterSelectorProps {
  value: TimeFilter;
  dateRange?: DateRange;
  onChange: (filter: TimeFilter, dateRange?: DateRange) => void;
  className?: string;
  showPresets?: boolean;
  disabled?: boolean;
}

const TIME_FILTER_OPTIONS = [
  { value: 'week' as TimeFilter, label: 'Last Week', icon: Clock },
  { value: 'month' as TimeFilter, label: 'Last Month', icon: Clock },
  { value: 'quarter' as TimeFilter, label: 'Last Quarter', icon: Clock },
  { value: 'year' as TimeFilter, label: 'Last Year', icon: Clock },
  { value: 'custom' as TimeFilter, label: 'Custom Range', icon: Calendar },
];

const PRESET_RANGES = [
  { label: 'Last 7 days', days: 7 },
  { label: 'Last 30 days', days: 30 },
  { label: 'Last 90 days', days: 90 },
  { label: 'Last 6 months', days: 180 },
  { label: 'Last year', days: 365 },
];

export function TimeFilterSelector({
  value,
  dateRange,
  onChange,
  className = '',
  showPresets = true,
  disabled = false,
}: TimeFilterSelectorProps) {
  const [isCustomOpen, setIsCustomOpen] = useState(false);
  const [tempStartDate, setTempStartDate] = useState<Date | undefined>();
  const [tempEndDate, setTempEndDate] = useState<Date | undefined>();

  // Initialize temp dates when switching to custom or when dateRange changes
  useEffect(() => {
    if (value === 'custom' && dateRange) {
      setTempStartDate(new Date(dateRange.start_date));
      setTempEndDate(new Date(dateRange.end_date));
    } else if (value === 'custom' && !dateRange) {
      // Default to last 30 days
      const end = new Date();
      const start = subDays(end, 30);
      setTempStartDate(start);
      setTempEndDate(end);
    }
  }, [value, dateRange]);

  const handleFilterChange = (newFilter: TimeFilter) => {
    if (newFilter === 'custom') {
      setIsCustomOpen(true);
      // If no temp dates set, use default range
      if (!tempStartDate || !tempEndDate) {
        const end = new Date();
        const start = subDays(end, 30);
        setTempStartDate(start);
        setTempEndDate(end);
      }
    } else {
      onChange(newFilter);
    }
  };

  const handleCustomRangeApply = () => {
    if (tempStartDate && tempEndDate) {
      const range: DateRange = {
        start_date: format(tempStartDate, 'yyyy-MM-dd'),
        end_date: format(tempEndDate, 'yyyy-MM-dd'),
      };
      onChange('custom', range);
      setIsCustomOpen(false);
    }
  };

  const handlePresetSelect = (days: number) => {
    const end = new Date();
    const start = subDays(end, days);
    setTempStartDate(start);
    setTempEndDate(end);
    
    const range: DateRange = {
      start_date: format(start, 'yyyy-MM-dd'),
      end_date: format(end, 'yyyy-MM-dd'),
    };
    onChange('custom', range);
    setIsCustomOpen(false);
  };

  const getDisplayLabel = () => {
    if (value === 'custom' && dateRange) {
      return `${format(new Date(dateRange.start_date), 'MMM dd')} - ${format(new Date(dateRange.end_date), 'MMM dd, yyyy')}`;
    }
    return TIME_FILTER_OPTIONS.find(option => option.value === value)?.label || 'Select period';
  };

  const isDateRangeValid = tempStartDate && tempEndDate && tempStartDate <= tempEndDate;

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Filter className="h-4 w-4 text-muted-foreground" />
      
      <Popover open={isCustomOpen} onOpenChange={setIsCustomOpen}>
        <PopoverTrigger asChild>
          <div>
            <Select
              value={value}
              onValueChange={handleFilterChange}
              disabled={disabled}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue>
                  <div className="flex items-center gap-2">
                    {value === 'custom' ? (
                      <Calendar className="h-4 w-4" />
                    ) : (
                      <Clock className="h-4 w-4" />
                    )}
                    <span className="truncate">{getDisplayLabel()}</span>
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {TIME_FILTER_OPTIONS.map((option) => {
                  const Icon = option.icon;
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        {option.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
        </PopoverTrigger>

        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-4 space-y-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Custom Date Range</Label>
              
              {showPresets && (
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Quick Select</Label>
                  <div className="flex flex-wrap gap-1">
                    {PRESET_RANGES.map((preset) => (
                      <Badge
                        key={preset.days}
                        variant="outline"
                        className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                        onClick={() => handlePresetSelect(preset.days)}
                      >
                        {preset.label}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-xs text-muted-foreground">Start Date</Label>
                <CalendarComponent
                  mode="single"
                  selected={tempStartDate}
                  onSelect={setTempStartDate}
                  disabled={(date) => date > new Date() || (tempEndDate && date > tempEndDate)}
                  className="rounded-md border"
                />
              </div>
              
              <div className="space-y-2">
                <Label className="text-xs text-muted-foreground">End Date</Label>
                <CalendarComponent
                  mode="single"
                  selected={tempEndDate}
                  onSelect={setTempEndDate}
                  disabled={(date) => date > new Date() || (tempStartDate && date < tempStartDate)}
                  className="rounded-md border"
                />
              </div>
            </div>

            <div className="flex justify-between gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsCustomOpen(false)}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleCustomRangeApply}
                disabled={!isDateRangeValid}
              >
                Apply Range
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {value === 'custom' && dateRange && (
        <Badge variant="secondary" className="text-xs">
          Custom
        </Badge>
      )}
    </div>
  );
}
