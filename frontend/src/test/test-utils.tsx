import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { Toaster } from '@/components/ui/toaster';

// Mock Z<PERSON> store for testing
const mockGamificationStore = {
  userStats: {
    totalXP: 1000,
    level: 5,
    currentStreak: 3,
    longestStreak: 7,
    demosCompleted: 12,
    perfectScores: 3,
    totalAttempts: 15,
    averageScore: 85,
    timeSpent: 120,
  },
  achievements: [
    {
      id: 'first_demo',
      name: 'First Steps',
      description: 'Complete your first interactive demo',
      icon: '🎯',
      xpReward: 50,
      unlocked: true,
      category: 'demo'
    },
    {
      id: 'perfect_score',
      name: 'Perfectionist',
      description: 'Get a perfect score on any demo',
      icon: '⭐',
      xpReward: 100,
      unlocked: false,
      category: 'learning'
    }
  ],
  recentActivity: [
    {
      id: '1',
      type: 'xp_gained' as const,
      message: 'Earned 50 XP from Math Demo',
      timestamp: new Date(),
      xp: 50
    }
  ],
  isLoading: false,
  error: null,
  awardXP: jest.fn(),
  completeDemo: jest.fn(),
  checkAchievements: jest.fn(() => []),
  updateLeaderboard: jest.fn(),
  addActivity: jest.fn(),
  resetProgress: jest.fn(),
  setError: jest.fn(),
  setLoading: jest.fn(),
};

// Mock the gamification store
jest.mock('@/stores/gamificationStore', () => ({
  useGamificationStore: () => mockGamificationStore,
}));

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock performance API
global.performance = {
  ...global.performance,
  now: jest.fn(() => Date.now()),
  mark: jest.fn(),
  measure: jest.fn(),
};

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
        <Toaster />
      </BrowserRouter>
    </QueryClientProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Test data factories
export const createMockUser = (overrides = {}) => ({
  id: '1',
  email: '<EMAIL>',
  name: 'Test User',
  ...overrides,
});

export const createMockAchievement = (overrides = {}) => ({
  id: 'test_achievement',
  name: 'Test Achievement',
  description: 'A test achievement',
  icon: '🏆',
  xpReward: 100,
  unlocked: false,
  category: 'test',
  ...overrides,
});

export const createMockActivity = (overrides = {}) => ({
  id: '1',
  type: 'xp_gained' as const,
  message: 'Test activity',
  timestamp: new Date(),
  xp: 50,
  ...overrides,
});

// Custom matchers
expect.extend({
  toBeInTheDocument(received) {
    const pass = received !== null && document.body.contains(received);
    return {
      message: () =>
        pass
          ? `Expected element not to be in the document`
          : `Expected element to be in the document`,
      pass,
    };
  },
});

// Utility functions for testing
export const waitForLoadingToFinish = () =>
  new Promise(resolve => setTimeout(resolve, 0));

export const mockApiResponse = (data: any, delay = 0) =>
  new Promise(resolve => setTimeout(() => resolve(data), delay));

export const mockApiError = (error: string, delay = 0) =>
  new Promise((_, reject) => setTimeout(() => reject(new Error(error)), delay));

// Mock drag and drop events
export const createMockDragEvent = (type: string, dataTransfer = {}) => {
  const event = new Event(type, { bubbles: true });
  Object.defineProperty(event, 'dataTransfer', {
    value: {
      getData: jest.fn(),
      setData: jest.fn(),
      clearData: jest.fn(),
      dropEffect: 'none',
      effectAllowed: 'all',
      files: [],
      items: [],
      types: [],
      ...dataTransfer,
    },
  });
  return event;
};

// Mock file upload
export const createMockFile = (name = 'test.txt', type = 'text/plain', size = 1024) => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

// Animation testing utilities
export const mockAnimations = () => {
  // Mock requestAnimationFrame
  global.requestAnimationFrame = jest.fn(cb => setTimeout(cb, 16));
  global.cancelAnimationFrame = jest.fn();
  
  // Mock CSS animations
  Element.prototype.animate = jest.fn(() => ({
    finished: Promise.resolve(),
    cancel: jest.fn(),
    pause: jest.fn(),
    play: jest.fn(),
  }));
};

// Network testing utilities
export const mockNetworkStatus = (isOnline = true) => {
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: isOnline,
  });
  
  // Mock connection API
  Object.defineProperty(navigator, 'connection', {
    writable: true,
    value: {
      effectiveType: '4g',
      downlink: 10,
      rtt: 100,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
  });
};

// Error boundary testing
export const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

// Async component testing
export const AsyncComponent = ({ delay = 100 }: { delay?: number }) => {
  const [loaded, setLoaded] = React.useState(false);
  
  React.useEffect(() => {
    setTimeout(() => setLoaded(true), delay);
  }, [delay]);
  
  if (!loaded) return <div>Loading...</div>;
  return <div>Loaded</div>;
};

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };
export { mockGamificationStore };
