import { useQuery, useQueries, useMutation, useQueryClient } from '@tanstack/react-query';
import { analyticsApi } from '@/lib/api/analytics';
import { analyticsQueryKeys, invalidateAnalytics } from '@/lib/query-client';
import {
  AdvancedAnalytics,
  PerformanceTrend,
  AnalyticsInsight,
  StudyPattern,
  RecommendationItem,
  HeatmapData,
  TimeFilter,
  DateRange,
  DashboardSummary,
} from '@/lib/api-types';

interface UseAnalyticsQueryOptions {
  timeFilter: TimeFilter;
  dateRange?: DateRange;
  enabled?: boolean;
}

// Hook for dashboard data
export function useDashboardQuery(enabled = true) {
  return useQuery({
    queryKey: analyticsQueryKeys.dashboard(),
    queryFn: () => analyticsApi.getDashboard(),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for advanced analytics
export function useAdvancedAnalyticsQuery({ timeFilter, dateRange, enabled = true }: UseAnalyticsQueryOptions) {
  const dateRangeKey = dateRange ? `${dateRange.start_date}_${dateRange.end_date}` : undefined;
  
  return useQuery({
    queryKey: analyticsQueryKeys.advanced(timeFilter, dateRangeKey),
    queryFn: () => analyticsApi.getAdvancedAnalytics(timeFilter, dateRange),
    enabled,
    staleTime: 3 * 60 * 1000, // 3 minutes for time-sensitive data
  });
}

// Hook for performance trends
export function usePerformanceTrendsQuery({ timeFilter, dateRange, enabled = true }: UseAnalyticsQueryOptions) {
  const dateRangeKey = dateRange ? `${dateRange.start_date}_${dateRange.end_date}` : undefined;
  
  return useQuery({
    queryKey: analyticsQueryKeys.trends(timeFilter, dateRangeKey),
    queryFn: () => analyticsApi.getPerformanceTrends(timeFilter, dateRange),
    enabled,
    staleTime: 3 * 60 * 1000, // 3 minutes
  });
}

// Hook for insights
export function useInsightsQuery(enabled = true) {
  return useQuery({
    queryKey: analyticsQueryKeys.insights(),
    queryFn: () => analyticsApi.getInsights(),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes - insights change less frequently
  });
}

// Hook for study patterns
export function useStudyPatternsQuery(enabled = true) {
  return useQuery({
    queryKey: analyticsQueryKeys.patterns(),
    queryFn: () => analyticsApi.getStudyPatterns(),
    enabled,
    staleTime: 15 * 60 * 1000, // 15 minutes - patterns change slowly
  });
}

// Hook for recommendations
export function useRecommendationsQuery(enabled = true) {
  return useQuery({
    queryKey: analyticsQueryKeys.recommendations(),
    queryFn: () => analyticsApi.getRecommendations(),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for heatmap data
export function useHeatmapQuery(enabled = true) {
  return useQuery({
    queryKey: analyticsQueryKeys.heatmap(),
    queryFn: () => analyticsApi.getHeatmapData(),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Comprehensive hook that fetches all analytics data with intelligent caching
export function useAnalyticsQueries({ timeFilter, dateRange }: { timeFilter: TimeFilter; dateRange?: DateRange }) {
  const queries = useQueries({
    queries: [
      {
        queryKey: analyticsQueryKeys.dashboard(),
        queryFn: () => analyticsApi.getDashboard(),
        staleTime: 5 * 60 * 1000,
      },
      {
        queryKey: analyticsQueryKeys.advanced(timeFilter, dateRange ? `${dateRange.start_date}_${dateRange.end_date}` : undefined),
        queryFn: () => analyticsApi.getAdvancedAnalytics(timeFilter, dateRange),
        staleTime: 3 * 60 * 1000,
      },
      {
        queryKey: analyticsQueryKeys.trends(timeFilter, dateRange ? `${dateRange.start_date}_${dateRange.end_date}` : undefined),
        queryFn: () => analyticsApi.getPerformanceTrends(timeFilter, dateRange),
        staleTime: 3 * 60 * 1000,
      },
      {
        queryKey: analyticsQueryKeys.insights(),
        queryFn: () => analyticsApi.getInsights(),
        staleTime: 10 * 60 * 1000,
      },
      {
        queryKey: analyticsQueryKeys.patterns(),
        queryFn: () => analyticsApi.getStudyPatterns(),
        staleTime: 15 * 60 * 1000,
      },
      {
        queryKey: analyticsQueryKeys.recommendations(),
        queryFn: () => analyticsApi.getRecommendations(),
        staleTime: 10 * 60 * 1000,
      },
      {
        queryKey: analyticsQueryKeys.heatmap(),
        queryFn: () => analyticsApi.getHeatmapData(),
        staleTime: 5 * 60 * 1000,
      },
    ],
  });

  const [
    dashboardQuery,
    advancedQuery,
    trendsQuery,
    insightsQuery,
    patternsQuery,
    recommendationsQuery,
    heatmapQuery,
  ] = queries;

  return {
    // Data
    dashboard: dashboardQuery.data as DashboardSummary | undefined,
    advancedAnalytics: advancedQuery.data as AdvancedAnalytics | undefined,
    performanceTrends: trendsQuery.data as PerformanceTrend[] | undefined,
    insights: insightsQuery.data as AnalyticsInsight[] | undefined,
    studyPatterns: patternsQuery.data as StudyPattern | undefined,
    recommendations: recommendationsQuery.data as RecommendationItem[] | undefined,
    heatmapData: heatmapQuery.data as HeatmapData[] | undefined,

    // Loading states
    isLoading: queries.some(q => q.isLoading),
    isLoadingDashboard: dashboardQuery.isLoading,
    isLoadingAdvanced: advancedQuery.isLoading,
    isLoadingTrends: trendsQuery.isLoading,
    isLoadingInsights: insightsQuery.isLoading,
    isLoadingPatterns: patternsQuery.isLoading,
    isLoadingRecommendations: recommendationsQuery.isLoading,
    isLoadingHeatmap: heatmapQuery.isLoading,

    // Error states
    hasError: queries.some(q => q.isError),
    errors: queries.map(q => q.error).filter(Boolean),
    dashboardError: dashboardQuery.error,
    advancedError: advancedQuery.error,
    trendsError: trendsQuery.error,
    insightsError: insightsQuery.error,
    patternsError: patternsQuery.error,
    recommendationsError: recommendationsQuery.error,
    heatmapError: heatmapQuery.error,

    // Refetch functions
    refetchAll: () => Promise.all(queries.map(q => q.refetch())),
    refetchDashboard: dashboardQuery.refetch,
    refetchAdvanced: advancedQuery.refetch,
    refetchTrends: trendsQuery.refetch,
    refetchInsights: insightsQuery.refetch,
    refetchPatterns: patternsQuery.refetch,
    refetchRecommendations: recommendationsQuery.refetch,
    refetchHeatmap: heatmapQuery.refetch,

    // Cache status
    isCached: queries.every(q => q.isFetched),
    isStale: queries.some(q => q.isStale),
  };
}

// Hook for cache management
export function useAnalyticsCache() {
  const queryClient = useQueryClient();

  return {
    invalidateAll: () => invalidateAnalytics.all(),
    invalidateDashboard: () => invalidateAnalytics.dashboard(),
    invalidateAdvanced: () => invalidateAnalytics.advanced(),
    invalidateTrends: () => invalidateAnalytics.trends(),
    invalidateInsights: () => invalidateAnalytics.insights(),
    invalidatePatterns: () => invalidateAnalytics.patterns(),
    invalidateRecommendations: () => invalidateAnalytics.recommendations(),
    invalidateHeatmap: () => invalidateAnalytics.heatmap(),
    
    clearCache: () => {
      queryClient.removeQueries({ queryKey: analyticsQueryKeys.all });
    },
    
    prefetchForTimeFilter: async (timeFilter: TimeFilter, dateRange?: DateRange) => {
      const dateRangeKey = dateRange ? `${dateRange.start_date}_${dateRange.end_date}` : undefined;
      
      await Promise.all([
        queryClient.prefetchQuery({
          queryKey: analyticsQueryKeys.advanced(timeFilter, dateRangeKey),
          queryFn: () => analyticsApi.getAdvancedAnalytics(timeFilter, dateRange),
          staleTime: 3 * 60 * 1000,
        }),
        queryClient.prefetchQuery({
          queryKey: analyticsQueryKeys.trends(timeFilter, dateRangeKey),
          queryFn: () => analyticsApi.getPerformanceTrends(timeFilter, dateRange),
          staleTime: 3 * 60 * 1000,
        }),
      ]);
    },
  };
}
