import { useEffect, useCallback, useRef, useState } from 'react';

// Keyboard navigation hook
export const useKeyboardNavigation = (
  items: HTMLElement[],
  options: {
    loop?: boolean;
    orientation?: 'horizontal' | 'vertical';
    onSelect?: (index: number) => void;
  } = {}
) => {
  const { loop = true, orientation = 'horizontal', onSelect } = options;
  const [currentIndex, setCurrentIndex] = useState(0);
  const containerRef = useRef<HTMLElement>(null);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!items.length) return;

    const isHorizontal = orientation === 'horizontal';
    const nextKey = isHorizontal ? 'ArrowRight' : 'ArrowDown';
    const prevKey = isHorizontal ? 'ArrowLeft' : 'ArrowUp';

    switch (event.key) {
      case nextKey:
        event.preventDefault();
        setCurrentIndex(prev => {
          const next = prev + 1;
          return loop ? next % items.length : Math.min(next, items.length - 1);
        });
        break;

      case prevKey:
        event.preventDefault();
        setCurrentIndex(prev => {
          const next = prev - 1;
          return loop ? (next + items.length) % items.length : Math.max(next, 0);
        });
        break;

      case 'Home':
        event.preventDefault();
        setCurrentIndex(0);
        break;

      case 'End':
        event.preventDefault();
        setCurrentIndex(items.length - 1);
        break;

      case 'Enter':
      case ' ':
        event.preventDefault();
        onSelect?.(currentIndex);
        break;

      case 'Escape':
        event.preventDefault();
        items[currentIndex]?.blur();
        break;
    }
  }, [items, loop, orientation, onSelect, currentIndex]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('keydown', handleKeyDown);
    return () => container.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  useEffect(() => {
    if (items[currentIndex]) {
      items[currentIndex].focus();
    }
  }, [currentIndex, items]);

  return { currentIndex, setCurrentIndex, containerRef };
};

// Focus management hook
export const useFocusManagement = () => {
  const focusStackRef = useRef<HTMLElement[]>([]);

  const pushFocus = useCallback((element: HTMLElement) => {
    const currentFocus = document.activeElement as HTMLElement;
    if (currentFocus) {
      focusStackRef.current.push(currentFocus);
    }
    element.focus();
  }, []);

  const popFocus = useCallback(() => {
    const previousFocus = focusStackRef.current.pop();
    if (previousFocus) {
      previousFocus.focus();
    }
  }, []);

  const trapFocus = useCallback((container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }, []);

  return { pushFocus, popFocus, trapFocus };
};

// Screen reader announcements
export const useScreenReader = () => {
  const announcementRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Create live region for announcements
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.style.position = 'absolute';
    liveRegion.style.left = '-10000px';
    liveRegion.style.width = '1px';
    liveRegion.style.height = '1px';
    liveRegion.style.overflow = 'hidden';
    
    document.body.appendChild(liveRegion);
    announcementRef.current = liveRegion;

    return () => {
      if (announcementRef.current) {
        document.body.removeChild(announcementRef.current);
      }
    };
  }, []);

  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!announcementRef.current) return;

    announcementRef.current.setAttribute('aria-live', priority);
    announcementRef.current.textContent = message;

    // Clear after announcement
    setTimeout(() => {
      if (announcementRef.current) {
        announcementRef.current.textContent = '';
      }
    }, 1000);
  }, []);

  return { announce };
};

// Reduced motion detection
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

// High contrast detection
export const useHighContrast = () => {
  const [prefersHighContrast, setPrefersHighContrast] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setPrefersHighContrast(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersHighContrast(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersHighContrast;
};

// Skip link functionality
export const useSkipLinks = () => {
  const skipLinksRef = useRef<HTMLElement[]>([]);

  const addSkipLink = useCallback((element: HTMLElement, label: string) => {
    element.setAttribute('id', `skip-${label.toLowerCase().replace(/\s+/g, '-')}`);
    skipLinksRef.current.push(element);
  }, []);

  const createSkipNavigation = useCallback(() => {
    const skipNav = document.createElement('nav');
    skipNav.setAttribute('aria-label', 'Skip navigation');
    skipNav.className = 'skip-navigation';
    
    const skipList = document.createElement('ul');
    
    skipLinksRef.current.forEach((element, index) => {
      const listItem = document.createElement('li');
      const link = document.createElement('a');
      
      link.href = `#${element.id}`;
      link.textContent = `Skip to ${element.getAttribute('aria-label') || `section ${index + 1}`}`;
      link.className = 'skip-link';
      
      // Style skip links
      link.style.position = 'absolute';
      link.style.top = '-40px';
      link.style.left = '6px';
      link.style.background = '#000';
      link.style.color = '#fff';
      link.style.padding = '8px';
      link.style.textDecoration = 'none';
      link.style.zIndex = '1000';
      link.style.transition = 'top 0.3s';
      
      link.addEventListener('focus', () => {
        link.style.top = '6px';
      });
      
      link.addEventListener('blur', () => {
        link.style.top = '-40px';
      });
      
      listItem.appendChild(link);
      skipList.appendChild(listItem);
    });
    
    skipNav.appendChild(skipList);
    document.body.insertBefore(skipNav, document.body.firstChild);
  }, []);

  return { addSkipLink, createSkipNavigation };
};

// ARIA live region hook
export const useAriaLiveRegion = (initialMessage = '') => {
  const [message, setMessage] = useState(initialMessage);
  const regionRef = useRef<HTMLDivElement>(null);

  const updateMessage = useCallback((newMessage: string, priority: 'polite' | 'assertive' = 'polite') => {
    setMessage(newMessage);
    if (regionRef.current) {
      regionRef.current.setAttribute('aria-live', priority);
    }
  }, []);

  const clearMessage = useCallback(() => {
    setMessage('');
  }, []);

  return {
    message,
    updateMessage,
    clearMessage,
    regionRef,
    ariaLiveProps: {
      ref: regionRef,
      'aria-live': 'polite' as const,
      'aria-atomic': true,
      className: 'sr-only',
    },
  };
};

// Color contrast utilities
export const getContrastRatio = (color1: string, color2: string): number => {
  const getLuminance = (color: string): number => {
    // Convert hex to RGB
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    // Calculate relative luminance
    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
};

export const meetsWCAGContrast = (color1: string, color2: string, level: 'AA' | 'AAA' = 'AA'): boolean => {
  const ratio = getContrastRatio(color1, color2);
  return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
};

// Accessible form validation
export const useAccessibleForm = () => {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const { announce } = useScreenReader();

  const setFieldError = useCallback((fieldName: string, error: string) => {
    setErrors(prev => ({ ...prev, [fieldName]: error }));
    announce(`Error in ${fieldName}: ${error}`, 'assertive');
  }, [announce]);

  const clearFieldError = useCallback((fieldName: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  const getFieldProps = useCallback((fieldName: string) => ({
    'aria-invalid': !!errors[fieldName],
    'aria-describedby': errors[fieldName] ? `${fieldName}-error` : undefined,
  }), [errors]);

  const getErrorProps = useCallback((fieldName: string) => ({
    id: `${fieldName}-error`,
    role: 'alert',
    'aria-live': 'polite' as const,
  }), []);

  return {
    errors,
    setFieldError,
    clearFieldError,
    getFieldProps,
    getErrorProps,
  };
};
