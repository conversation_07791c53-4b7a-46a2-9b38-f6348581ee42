import { useState, useEffect, useCallback } from 'react';
import { analyticsApi } from '@/lib/api/analytics';
import { useAnalyticsTransforms, usePerformanceMonitor, useAnalyticsCache } from '@/lib/memoization';
import { useToast } from '@/hooks/use-toast';
import {
  AdvancedAnalytics,
  PerformanceTrend,
  AnalyticsInsight,
  StudyPattern,
  RecommendationItem,
  HeatmapData,
  TimeFilter,
  DateRange,
  DashboardSummary,
} from '@/lib/api-types';

interface AnalyticsState {
  // Data
  advancedAnalytics: AdvancedAnalytics | null;
  performanceTrends: PerformanceTrend[] | null;
  insights: AnalyticsInsight[] | null;
  studyPatterns: StudyPattern | null;
  recommendations: RecommendationItem[] | null;
  heatmapData: HeatmapData[] | null;
  dashboard: DashboardSummary | null;
  
  // Loading states
  isLoading: boolean;
  isLoadingTrends: boolean;
  isLoadingInsights: boolean;
  isLoadingPatterns: boolean;
  isLoadingRecommendations: boolean;
  isLoadingHeatmap: boolean;
  isLoadingDashboard: boolean;
  
  // Error states
  error: string | null;
  trendsError: string | null;
  insightsError: string | null;
  patternsError: string | null;
  recommendationsError: string | null;
  heatmapError: string | null;
  dashboardError: string | null;
}

const initialState: AnalyticsState = {
  // Data
  advancedAnalytics: null,
  performanceTrends: null,
  insights: null,
  studyPatterns: null,
  recommendations: null,
  heatmapData: null,
  dashboard: null,
  
  // Loading states
  isLoading: false,
  isLoadingTrends: false,
  isLoadingInsights: false,
  isLoadingPatterns: false,
  isLoadingRecommendations: false,
  isLoadingHeatmap: false,
  isLoadingDashboard: false,
  
  // Error states
  error: null,
  trendsError: null,
  insightsError: null,
  patternsError: null,
  recommendationsError: null,
  heatmapError: null,
  dashboardError: null,
};

export const useAnalyticsData = (initialTimeFilter: TimeFilter = 'month') => {
  console.log('useAnalyticsData: Hook initialized with timeFilter:', initialTimeFilter);
  const [state, setState] = useState<AnalyticsState>(initialState);
  const [timeFilter, setTimeFilter] = useState<TimeFilter>(initialTimeFilter);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const { toast } = useToast();

  // Performance monitoring and memoization
  const { measureAsyncOperation } = usePerformanceMonitor('useAnalyticsData');
  const { calculateTrendDirection, generateStudyInsights } = useAnalyticsTransforms();
  const { getCachedData, setCachedData } = useAnalyticsCache(`analytics_${timeFilter}`);

  // Fetch all analytics data with caching and performance monitoring
  const fetchAdvancedAnalytics = useCallback(async (filter: TimeFilter = timeFilter, customDateRange?: DateRange) => {
    console.log('fetchAdvancedAnalytics: Starting advanced analytics fetch...');
    const cacheKey = `advanced_${filter}_${customDateRange ? `${customDateRange.start_date}_${customDateRange.end_date}` : 'default'}`;

    // Try to get cached data first
    const cachedData = getCachedData();
    if (cachedData && cachedData[cacheKey]) {
      console.log('fetchAdvancedAnalytics: Using cached data');
      setState(prev => ({
        ...prev,
        advancedAnalytics: cachedData[cacheKey],
        isLoading: false,
      }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log('fetchAdvancedAnalytics: Making API call to /analytics/advanced');
      const response = await measureAsyncOperation('fetchAdvancedAnalytics', () =>
        analyticsApi.getAdvancedAnalytics(filter, customDateRange)
      );
      console.log('fetchAdvancedAnalytics: Advanced analytics response received:', response);

      // Cache the response
      const currentCache = getCachedData() || {};
      setCachedData({ ...currentCache, [cacheKey]: response });

      setState(prev => ({
        ...prev,
        advancedAnalytics: response,
        isLoading: false,
      }));
    } catch (error) {
      console.error('fetchAdvancedAnalytics: Error fetching advanced analytics:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to fetch analytics',
        isLoading: false,
      }));
    }
  }, [timeFilter, measureAsyncOperation, getCachedData, setCachedData]);

  // Fetch performance trends
  const fetchPerformanceTrends = useCallback(async (filter: TimeFilter = timeFilter, customDateRange?: DateRange) => {
    console.log('fetchPerformanceTrends: Starting performance trends fetch...');
    setState(prev => ({ ...prev, isLoadingTrends: true, trendsError: null }));

    try {
      console.log('fetchPerformanceTrends: Making API call to /analytics/trends');
      const response = await analyticsApi.getPerformanceTrends(filter, customDateRange);
      console.log('fetchPerformanceTrends: Performance trends response received:', response);
      setState(prev => ({
        ...prev,
        performanceTrends: response,
        isLoadingTrends: false,
      }));
    } catch (error) {
      console.error('fetchPerformanceTrends: Error fetching performance trends:', error);
      setState(prev => ({
        ...prev,
        trendsError: error instanceof Error ? error.message : 'Failed to fetch trends',
        isLoadingTrends: false,
      }));
    }
  }, [timeFilter]);

  // Fetch insights
  const fetchInsights = useCallback(async () => {
    console.log('fetchInsights: Starting insights fetch...');
    setState(prev => ({ ...prev, isLoadingInsights: true, insightsError: null }));

    try {
      const response = await analyticsApi.getInsights();
      console.log('fetchInsights: Insights response received:', response);
      setState(prev => ({
        ...prev,
        insights: response,
        isLoadingInsights: false,
      }));
    } catch (error) {
      console.error('fetchInsights: Error fetching insights:', error);
      setState(prev => ({
        ...prev,
        insightsError: error instanceof Error ? error.message : 'Failed to fetch insights',
        isLoadingInsights: false,
      }));
    }
  }, []);

  // Fetch study patterns
  const fetchStudyPatterns = useCallback(async () => {
    console.log('fetchStudyPatterns: Starting study patterns fetch...');
    setState(prev => ({ ...prev, isLoadingPatterns: true, patternsError: null }));

    try {
      console.log('fetchStudyPatterns: Making API call to /analytics/study-patterns');
      const response = await analyticsApi.getStudyPatterns();
      console.log('fetchStudyPatterns: Study patterns response received:', response);
      setState(prev => ({
        ...prev,
        studyPatterns: response,
        isLoadingPatterns: false,
      }));
    } catch (error) {
      console.error('fetchStudyPatterns: Error fetching study patterns:', error);
      setState(prev => ({
        ...prev,
        patternsError: error instanceof Error ? error.message : 'Failed to fetch patterns',
        isLoadingPatterns: false,
      }));
    }
  }, []);

  // Fetch recommendations
  const fetchRecommendations = useCallback(async () => {
    console.log('fetchRecommendations: Starting recommendations fetch...');
    setState(prev => ({ ...prev, isLoadingRecommendations: true, recommendationsError: null }));

    try {
      console.log('fetchRecommendations: Making API call to /analytics/recommendations');
      const response = await analyticsApi.getRecommendations();
      console.log('fetchRecommendations: Recommendations response received:', response);
      setState(prev => ({
        ...prev,
        recommendations: response,
        isLoadingRecommendations: false,
      }));
    } catch (error) {
      console.error('fetchRecommendations: Error fetching recommendations:', error);
      setState(prev => ({
        ...prev,
        recommendationsError: error instanceof Error ? error.message : 'Failed to fetch recommendations',
        isLoadingRecommendations: false,
      }));
    }
  }, []);

  // Fetch heatmap data
  const fetchHeatmapData = useCallback(async () => {
    console.log('fetchHeatmapData: Starting heatmap data fetch...');
    setState(prev => ({ ...prev, isLoadingHeatmap: true, heatmapError: null }));

    try {
      console.log('fetchHeatmapData: Making API call to /analytics/heatmap-data');
      const response = await analyticsApi.getHeatmapData();
      console.log('fetchHeatmapData: Heatmap data response received:', response);
      setState(prev => ({
        ...prev,
        heatmapData: response,
        isLoadingHeatmap: false,
      }));
    } catch (error) {
      console.error('fetchHeatmapData: Error fetching heatmap data:', error);
      setState(prev => ({
        ...prev,
        heatmapError: error instanceof Error ? error.message : 'Failed to fetch heatmap data',
        isLoadingHeatmap: false,
      }));
    }
  }, []);

  // Fetch dashboard data
  const fetchDashboard = useCallback(async () => {
    console.log('fetchDashboard: Starting dashboard fetch...');
    setState(prev => ({ ...prev, isLoadingDashboard: true, dashboardError: null }));

    try {
      const response = await analyticsApi.getDashboard();
      console.log('fetchDashboard: Dashboard response received:', response);
      setState(prev => ({
        ...prev,
        dashboard: response,
        isLoadingDashboard: false,
      }));
    } catch (error) {
      console.error('fetchDashboard: Error fetching dashboard:', error);
      setState(prev => ({
        ...prev,
        dashboardError: error instanceof Error ? error.message : 'Failed to fetch dashboard',
        isLoadingDashboard: false,
      }));
    }
  }, []);

  // Fetch all data
  const fetchAllData = useCallback(async (filter: TimeFilter = timeFilter, customDateRange?: DateRange) => {
    console.log('fetchAllData: Starting to fetch all analytics data...');
    await Promise.all([
      fetchAdvancedAnalytics(filter, customDateRange),
      fetchPerformanceTrends(filter, customDateRange),
      fetchInsights(),
      fetchStudyPatterns(),
      fetchRecommendations(),
      fetchHeatmapData(),
      fetchDashboard(),
    ]);
    console.log('fetchAllData: All analytics data fetch completed');
  }, [
    timeFilter,
    fetchAdvancedAnalytics,
    fetchPerformanceTrends,
    fetchInsights,
    fetchStudyPatterns,
    fetchRecommendations,
    fetchHeatmapData,
    fetchDashboard,
  ]);

  // Update time filter and refetch relevant data
  const updateTimeFilter = useCallback(async (newFilter: TimeFilter, customDateRange?: DateRange) => {
    setTimeFilter(newFilter);
    setDateRange(customDateRange);
    await Promise.all([
      fetchAdvancedAnalytics(newFilter, customDateRange),
      fetchPerformanceTrends(newFilter, customDateRange),
    ]);
  }, [fetchAdvancedAnalytics, fetchPerformanceTrends]);

  // Refresh all data
  const refreshData = useCallback(() => {
    fetchAllData(timeFilter);
  }, [fetchAllData, timeFilter]);

  // Auto-refresh data every 30 seconds when user is active
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    let isFirstRefresh = true;

    const startAutoRefresh = () => {
      intervalId = setInterval(() => {
        // Only refresh if document is visible (user is active)
        if (!document.hidden) {
          fetchAllData(timeFilter);
          // Show toast notification for auto-refresh (but not on first load)
          if (!isFirstRefresh) {
            toast({
              title: "Analytics Updated",
              description: "Your analytics data has been refreshed with the latest information.",
              duration: 2000,
            });
          }
          isFirstRefresh = false;
        }
      }, 30000); // 30 seconds
    };

    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Clear interval when tab is not visible
        if (intervalId) {
          clearInterval(intervalId);
        }
      } else {
        // Restart interval when tab becomes visible
        startAutoRefresh();
        // Also refresh immediately when tab becomes visible
        fetchAllData(timeFilter);
      }
    };

    // Start auto-refresh
    startAutoRefresh();

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [fetchAllData, timeFilter]);

  // Initial data fetch
  useEffect(() => {
    console.log('useAnalyticsData: Initial mount, starting data fetch...');
    fetchAllData();
  }, []); // Only run once on mount

  return {
    // Data
    ...state,
    timeFilter,
    dateRange,

    // Actions
    fetchAllData,
    fetchAdvancedAnalytics,
    fetchPerformanceTrends,
    fetchInsights,
    fetchStudyPatterns,
    fetchRecommendations,
    fetchHeatmapData,
    fetchDashboard,
    updateTimeFilter,
    refreshData,

    // Computed states
    hasData: !!(state.advancedAnalytics || state.performanceTrends || state.dashboard),
    hasError: !!(state.error || state.trendsError || state.insightsError ||
                 state.patternsError || state.recommendationsError ||
                 state.heatmapError || state.dashboardError),
    isAnyLoading: state.isLoading || state.isLoadingTrends || state.isLoadingInsights ||
                  state.isLoadingPatterns || state.isLoadingRecommendations ||
                  state.isLoadingHeatmap || state.isLoadingDashboard,
  };
};
