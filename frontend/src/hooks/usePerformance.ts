import { useEffect, useCallback, useRef, useMemo } from 'react';

// Performance monitoring hook
export const usePerformanceMonitor = (componentName: string) => {
  const startTimeRef = useRef<number>();

  useEffect(() => {
    startTimeRef.current = performance.now();
    
    return () => {
      if (startTimeRef.current) {
        const endTime = performance.now();
        const renderTime = endTime - startTimeRef.current;
        
        // Log performance in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`);
        }
        
        // Warn about slow renders
        if (renderTime > 1000) {
          console.warn(`Slow render detected: ${componentName} took ${renderTime.toFixed(2)}ms`);
        }
        
        // In production, send to analytics service
        if (process.env.NODE_ENV === 'production' && renderTime > 500) {
          // Example: analytics.track('slow_render', { component: componentName, time: renderTime });
        }
      }
    };
  });
};

// Debounced callback hook
export const useDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  ) as T;

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
};

// Throttled callback hook
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastCallRef = useRef<number>(0);

  const throttledCallback = useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();
      
      if (now - lastCallRef.current >= delay) {
        lastCallRef.current = now;
        callback(...args);
      }
    },
    [callback, delay]
  ) as T;

  return throttledCallback;
};

// Intersection Observer hook for lazy loading
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const elementRef = useRef<HTMLElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const observe = useCallback((element: HTMLElement | null) => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    if (element) {
      elementRef.current = element;
      observerRef.current = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Element is visible
            element.setAttribute('data-visible', 'true');
          } else {
            element.setAttribute('data-visible', 'false');
          }
        });
      }, options);

      observerRef.current.observe(element);
    }
  }, [options]);

  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  return observe;
};

// Memory usage monitoring
export const useMemoryMonitor = (componentName: string) => {
  useEffect(() => {
    if ('memory' in performance) {
      const memoryInfo = (performance as any).memory;
      const initialMemory = memoryInfo.usedJSHeapSize;

      return () => {
        const finalMemory = memoryInfo.usedJSHeapSize;
        const memoryDiff = finalMemory - initialMemory;

        if (process.env.NODE_ENV === 'development') {
          console.log(`${componentName} memory usage: ${(memoryDiff / 1024 / 1024).toFixed(2)}MB`);
        }

        // Warn about memory leaks
        if (memoryDiff > 10 * 1024 * 1024) { // 10MB
          console.warn(`Potential memory leak in ${componentName}: ${(memoryDiff / 1024 / 1024).toFixed(2)}MB`);
        }
      };
    }
  }, [componentName]);
};

// Optimized state updates
export const useOptimizedState = <T>(
  initialState: T,
  isEqual?: (a: T, b: T) => boolean
) => {
  const [state, setState] = useState(initialState);
  const previousStateRef = useRef(initialState);

  const optimizedSetState = useCallback((newState: T | ((prev: T) => T)) => {
    setState((prevState) => {
      const nextState = typeof newState === 'function' 
        ? (newState as (prev: T) => T)(prevState)
        : newState;

      // Use custom equality check or shallow comparison
      const areEqual = isEqual 
        ? isEqual(prevState, nextState)
        : Object.is(prevState, nextState);

      if (areEqual) {
        return prevState; // Prevent unnecessary re-renders
      }

      previousStateRef.current = prevState;
      return nextState;
    });
  }, [isEqual]);

  return [state, optimizedSetState, previousStateRef.current] as const;
};

// Memoized expensive calculations
export const useExpensiveCalculation = <T>(
  calculation: () => T,
  dependencies: React.DependencyList,
  shouldRecalculate?: (prev: T, deps: React.DependencyList) => boolean
) => {
  const previousDepsRef = useRef<React.DependencyList>();
  const previousResultRef = useRef<T>();

  return useMemo(() => {
    // Custom recalculation logic
    if (shouldRecalculate && previousResultRef.current && previousDepsRef.current) {
      if (!shouldRecalculate(previousResultRef.current, dependencies)) {
        return previousResultRef.current;
      }
    }

    const result = calculation();
    previousDepsRef.current = dependencies;
    previousResultRef.current = result;
    return result;
  }, dependencies);
};

// Batch state updates
export const useBatchedUpdates = () => {
  const updatesRef = useRef<(() => void)[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const batchUpdate = useCallback((update: () => void) => {
    updatesRef.current.push(update);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      // Execute all batched updates
      updatesRef.current.forEach(update => update());
      updatesRef.current = [];
    }, 0);
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return batchUpdate;
};

// Resource preloading
export const usePreloader = () => {
  const preloadedResources = useRef(new Set<string>());

  const preloadImage = useCallback((src: string) => {
    if (preloadedResources.current.has(src)) {
      return Promise.resolve();
    }

    return new Promise<void>((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        preloadedResources.current.add(src);
        resolve();
      };
      img.onerror = reject;
      img.src = src;
    });
  }, []);

  const preloadScript = useCallback((src: string) => {
    if (preloadedResources.current.has(src)) {
      return Promise.resolve();
    }

    return new Promise<void>((resolve, reject) => {
      const script = document.createElement('script');
      script.onload = () => {
        preloadedResources.current.add(src);
        resolve();
      };
      script.onerror = reject;
      script.src = src;
      document.head.appendChild(script);
    });
  }, []);

  const preloadCSS = useCallback((href: string) => {
    if (preloadedResources.current.has(href)) {
      return Promise.resolve();
    }

    return new Promise<void>((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.onload = () => {
        preloadedResources.current.add(href);
        resolve();
      };
      link.onerror = reject;
      link.href = href;
      document.head.appendChild(link);
    });
  }, []);

  return { preloadImage, preloadScript, preloadCSS };
};

// Network status monitoring
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionType, setConnectionType] = useState<string>('unknown');

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check connection type if available
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      setConnectionType(connection.effectiveType || 'unknown');

      const handleConnectionChange = () => {
        setConnectionType(connection.effectiveType || 'unknown');
      };

      connection.addEventListener('change', handleConnectionChange);

      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
        connection.removeEventListener('change', handleConnectionChange);
      };
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return { isOnline, connectionType };
};

// Import missing useState
import { useState } from 'react';
