import { useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { ApiError } from '@/lib/api';

interface ErrorHandlerOptions {
  showToast?: boolean;
  toastTitle?: string;
  toastDescription?: string;
  logError?: boolean;
  onError?: (error: Error) => void;
}

export function useErrorHandler() {
  const { toast } = useToast();

  const handleError = useCallback((
    error: unknown,
    options: ErrorHandlerOptions = {}
  ) => {
    const {
      showToast = true,
      toastTitle = 'Error',
      toastDescription,
      logError = true,
      onError
    } = options;

    let errorMessage = 'An unexpected error occurred';
    let errorDetails = '';

    // Parse different error types
    if (error instanceof ApiError) {
      errorMessage = error.message;
      errorDetails = `Status: ${error.status}`;
    } else if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = error.stack || '';
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else {
      errorMessage = 'Unknown error occurred';
    }

    // Log error if enabled
    if (logError) {
      console.error('Error handled:', {
        message: errorMessage,
        details: errorDetails,
        originalError: error
      });
    }

    // Show toast notification if enabled
    if (showToast) {
      toast({
        title: toastTitle,
        description: toastDescription || errorMessage,
        variant: 'destructive',
      });
    }

    // Call custom error handler if provided
    if (onError && error instanceof Error) {
      onError(error);
    }

    return {
      message: errorMessage,
      details: errorDetails
    };
  }, [toast]);

  // Specific handlers for common scenarios
  const handleApiError = useCallback((error: unknown, operation: string) => {
    return handleError(error, {
      toastTitle: `${operation} Failed`,
      toastDescription: error instanceof ApiError 
        ? error.message 
        : `Failed to ${operation.toLowerCase()}. Please try again.`
    });
  }, [handleError]);

  const handleNetworkError = useCallback((error: unknown) => {
    return handleError(error, {
      toastTitle: 'Network Error',
      toastDescription: 'Please check your internet connection and try again.'
    });
  }, [handleError]);

  const handleAuthError = useCallback((error: unknown) => {
    return handleError(error, {
      toastTitle: 'Authentication Error',
      toastDescription: 'Please log in again to continue.',
      onError: () => {
        // Redirect to login page
        window.location.href = '/login';
      }
    });
  }, [handleError]);

  const handleValidationError = useCallback((error: unknown, field?: string) => {
    const fieldText = field ? ` for ${field}` : '';
    return handleError(error, {
      toastTitle: 'Validation Error',
      toastDescription: error instanceof Error 
        ? error.message 
        : `Invalid input${fieldText}. Please check and try again.`
    });
  }, [handleError]);

  return {
    handleError,
    handleApiError,
    handleNetworkError,
    handleAuthError,
    handleValidationError
  };
}

// Hook for async operations with error handling
export function useAsyncOperation() {
  const { handleError } = useErrorHandler();

  const executeAsync = useCallback(async <T>(
    operation: () => Promise<T>,
    options: ErrorHandlerOptions & {
      onSuccess?: (result: T) => void;
      onFinally?: () => void;
    } = {}
  ): Promise<{ success: boolean; data?: T; error?: string }> => {
    const { onSuccess, onFinally, ...errorOptions } = options;

    try {
      const result = await operation();
      onSuccess?.(result);
      return { success: true, data: result };
    } catch (error) {
      const errorInfo = handleError(error, errorOptions);
      return { success: false, error: errorInfo.message };
    } finally {
      onFinally?.();
    }
  }, [handleError]);

  return { executeAsync };
}

// Hook for form validation errors
export function useFormErrorHandler() {
  const { handleValidationError } = useErrorHandler();

  const handleFormError = useCallback((
    error: unknown,
    fieldErrors?: Record<string, string[]>
  ) => {
    if (fieldErrors) {
      // Handle field-specific errors
      Object.entries(fieldErrors).forEach(([field, errors]) => {
        errors.forEach(errorMessage => {
          handleValidationError(new Error(errorMessage), field);
        });
      });
    } else {
      // Handle general form error
      handleValidationError(error);
    }
  }, [handleValidationError]);

  return { handleFormError };
}
