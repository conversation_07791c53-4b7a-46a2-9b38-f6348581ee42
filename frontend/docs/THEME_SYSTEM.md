# Test-Spark Theme System

## Overview

Test-Spark now includes a comprehensive dark/light theme system with automatic system preference detection and user preference persistence.

## Features

- 🌙 **Dark Mode**: Professional dark theme optimized for educational content
- ☀️ **Light Mode**: Clean, bright theme for daytime use
- 🖥️ **System Mode**: Automatically follows system preference
- 💾 **Persistence**: User preference saved in localStorage
- 🎨 **Smooth Transitions**: Animated theme switching
- 📱 **Responsive**: Works on all device sizes

## Components

### ThemeProvider
The main provider that wraps the entire application and manages theme state.

```tsx
import { ThemeProvider } from '@/components/theme';

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="test-spark-ui-theme">
      {/* Your app content */}
    </ThemeProvider>
  );
}
```

### ThemeToggle
A dropdown menu with all theme options (Light, Dark, System).

```tsx
import { ThemeToggle } from '@/components/theme';

function Navbar() {
  return (
    <div className="navbar">
      <ThemeToggle />
    </div>
  );
}
```

### SimpleThemeToggle
A simple button that cycles through themes.

```tsx
import { SimpleThemeToggle } from '@/components/theme/SimpleThemeToggle';

function Header() {
  return (
    <div className="header">
      <SimpleThemeToggle />
    </div>
  );
}
```

### useTheme Hook
Access and control theme state from any component.

```tsx
import { useTheme } from '@/components/theme';

function MyComponent() {
  const { theme, setTheme } = useTheme();
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <button onClick={() => setTheme('dark')}>
        Switch to Dark
      </button>
    </div>
  );
}
```

## Theme Configuration

### CSS Variables
All theme colors are defined using CSS custom properties in `src/index.css`:

```css
:root {
  /* Light theme variables */
  --background: 250 100% 99%;
  --foreground: 222 47% 11%;
  --primary: 217 91% 60%;
  /* ... */
}

.dark {
  /* Dark theme variables */
  --background: 222 47% 11%;
  --foreground: 210 40% 98%;
  --primary: 217 91% 65%;
  /* ... */
}
```

### Educational Color Palette

#### Light Theme
- **Background**: Clean white with subtle blue tint
- **Primary**: Educational blue (#4F8EF7)
- **Secondary**: Academic purple (#B794F6)
- **Success**: Progress green (#48BB78)
- **Cards**: Gradient backgrounds with soft shadows

#### Dark Theme
- **Background**: Deep blue-gray (#1A202C)
- **Primary**: Brighter educational blue (#63A4FF)
- **Secondary**: Lighter academic purple (#C3A6F0)
- **Success**: Vibrant progress green (#68D391)
- **Cards**: Dark gradients with blue-tinted shadows

## Usage Examples

### Basic Theme Toggle in Corner
```tsx
// Place in top-right corner of any page
<div className="fixed top-4 right-4 z-50">
  <ThemeToggle />
</div>
```

### Theme-Aware Components
```tsx
function Card({ children }) {
  return (
    <div className="bg-card text-card-foreground border border-border rounded-lg p-6">
      {children}
    </div>
  );
}
```

### Custom Theme Logic
```tsx
function CustomComponent() {
  const { theme } = useTheme();
  
  const isDark = theme === 'dark' || 
    (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches);
  
  return (
    <div className={`custom-component ${isDark ? 'dark-specific' : 'light-specific'}`}>
      Content adapts to theme
    </div>
  );
}
```

## Styling Guidelines

### Use CSS Variables
Always use the predefined CSS variables for consistent theming:

```css
/* Good */
.my-component {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
}

/* Avoid */
.my-component {
  background-color: white;
  color: black;
  border: 1px solid #ccc;
}
```

### Tailwind Classes
Use Tailwind's semantic color classes that automatically adapt:

```tsx
// These automatically work with both themes
<div className="bg-background text-foreground">
<button className="bg-primary text-primary-foreground">
<div className="border border-border">
```

### Dark Mode Specific Styles
Use the `dark:` prefix for dark-mode specific styles:

```tsx
<div className="bg-white dark:bg-gray-900 text-black dark:text-white">
  Content that adapts to theme
</div>
```

## Best Practices

1. **Always use CSS variables** for colors to ensure theme compatibility
2. **Test both themes** during development
3. **Use semantic color names** (primary, secondary, etc.) instead of specific colors
4. **Provide theme toggle** in an easily accessible location
5. **Consider accessibility** - ensure sufficient contrast in both themes
6. **Use transitions** for smooth theme switching

## Accessibility

- All theme toggles include proper ARIA labels
- Color contrast ratios meet WCAG guidelines in both themes
- Screen readers announce theme changes
- Keyboard navigation supported for all theme controls

## Browser Support

- Modern browsers with CSS custom properties support
- Graceful fallback for older browsers
- Respects `prefers-color-scheme` media query
- Works with browser zoom and accessibility settings

## Troubleshooting

### Theme not applying
- Check that `ThemeProvider` wraps your app
- Verify CSS variables are properly defined
- Ensure Tailwind's `darkMode: ["class"]` is configured

### Flashing on page load
- Theme is applied immediately on mount
- Consider adding a loading state if needed
- Check that the correct theme is being read from localStorage

### Custom components not theming
- Use CSS variables instead of hardcoded colors
- Apply appropriate Tailwind classes
- Test with both light and dark themes
