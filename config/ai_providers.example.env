# AI Provider Configuration
# Copy this file to .env and configure your AI providers

# =============================================================================
# PRIMARY AI PROVIDER CONFIGURATION
# =============================================================================

# Primary AI provider to use for question generation
# Options: groq, openai, mock
AI_PRIMARY_PROVIDER=groq

# Fallback AI provider to use if primary fails
# Options: groq, openai, mock
AI_FALLBACK_PROVIDER=mock

# Enable fallback to secondary provider if primary fails
AI_ENABLE_FALLBACK=true

# Global timeout for AI requests (seconds)
AI_TIMEOUT_SECONDS=60

# Maximum retry attempts for failed requests
AI_MAX_RETRIES=3

# =============================================================================
# GROQ CONFIGURATION
# =============================================================================

# Groq API Key (required if using Groq)
# Get your API key from: https://console.groq.com
GROQ_API_KEY=your_groq_api_key_here

# Groq model to use for question generation
# Options: llama-3.3-70b-versatile, llama-3.1-70b-versatile, mixtral-8x7b-32768
GROQ_MODEL=llama-3.3-70b-versatile

# Temperature for response generation (0.0 to 1.0)
# Lower values = more deterministic, Higher values = more creative
GROQ_TEMPERATURE=0.7

# Maximum tokens per request
GROQ_MAX_TOKENS=4000

# Groq API base URL (usually don't need to change)
GROQ_BASE_URL=https://api.groq.com/openai/v1/chat/completions

# =============================================================================
# OPENAI CONFIGURATION
# =============================================================================

# OpenAI API Key (required if using OpenAI)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# OpenAI model to use for question generation
# Options: gpt-4, gpt-4-turbo, gpt-3.5-turbo
OPENAI_MODEL=gpt-4

# Temperature for response generation (0.0 to 1.0)
OPENAI_TEMPERATURE=0.7

# Maximum tokens per request
OPENAI_MAX_TOKENS=4000

# OpenAI API base URL (usually don't need to change)
OPENAI_BASE_URL=https://api.openai.com/v1/chat/completions

# OpenAI Organization ID (optional)
OPENAI_ORG_ID=your_org_id_here

# =============================================================================
# FEATURE CONFIGURATION
# =============================================================================

# Enable grade-specific question adaptation
AI_ENABLE_GRADE_ADAPTATION=true

# Enable educational board-specific content
AI_ENABLE_BOARD_SUPPORT=true

# Enable comprehensive question validation
AI_ENABLE_VALIDATION=true

# Enable detailed logging of AI operations
AI_ENABLE_LOGGING=true

# =============================================================================
# RATE LIMITING & QUOTAS
# =============================================================================

# Maximum questions per single request
AI_MAX_QUESTIONS_PER_REQUEST=50

# Maximum API requests per minute
AI_MAX_REQUESTS_PER_MINUTE=60

# Maximum tokens per request (overrides provider-specific settings)
AI_MAX_TOKENS_PER_REQUEST=4000

# =============================================================================
# EXAMPLE CONFIGURATIONS
# =============================================================================

# Example 1: Groq Primary with Mock Fallback (Recommended for development)
# AI_PRIMARY_PROVIDER=groq
# AI_FALLBACK_PROVIDER=mock
# GROQ_API_KEY=your_groq_key
# AI_ENABLE_FALLBACK=true

# Example 2: OpenAI Primary with Groq Fallback (Production setup)
# AI_PRIMARY_PROVIDER=openai
# AI_FALLBACK_PROVIDER=groq
# OPENAI_API_KEY=your_openai_key
# GROQ_API_KEY=your_groq_key
# AI_ENABLE_FALLBACK=true

# Example 3: Mock Only (Testing/Development without API keys)
# AI_PRIMARY_PROVIDER=mock
# AI_FALLBACK_PROVIDER=mock
# AI_ENABLE_FALLBACK=false

# =============================================================================
# PROVIDER COMPARISON
# =============================================================================

# GROQ:
# - Pros: Fast inference, cost-effective, good for development
# - Cons: Limited model selection, newer service
# - Best for: Development, testing, cost-conscious production

# OPENAI:
# - Pros: Most advanced models, excellent quality, reliable
# - Cons: Higher cost, rate limits
# - Best for: Production, highest quality requirements

# MOCK:
# - Pros: No API key required, always available, predictable
# - Cons: Generated content is not real AI, limited educational value
# - Best for: Testing, development, fallback scenarios

# =============================================================================
# SECURITY NOTES
# =============================================================================

# 1. Never commit API keys to version control
# 2. Use environment variables or secure secret management
# 3. Rotate API keys regularly
# 4. Monitor API usage and costs
# 5. Set up proper rate limiting and quotas
# 6. Use different keys for development and production

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# If you encounter issues:
# 1. Verify API keys are correct and active
# 2. Check API quotas and rate limits
# 3. Ensure network connectivity to provider APIs
# 4. Review logs for detailed error messages
# 5. Test with mock provider to isolate issues
# 6. Verify environment variables are loaded correctly
